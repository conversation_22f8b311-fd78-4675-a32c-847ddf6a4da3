#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Test rápido para verificar que AMI funciona con la corrección.
"""

import sys
import os
import asyncio
import logging

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.asterisk_ami import AsteriskAMI, AMIConfig
from src.config import Config


async def test_ami_quick():
    """Prueba rápida de AMI corregido."""
    print("🔧 PRUEBA RÁPIDA DE AMI CORREGIDO")
    print("=" * 50)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
    
    try:
        config = Config()
        
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        print(f"🔗 Conectando a AMI...")
        if await ami.connect():
            print("✅ Conexión AMI exitosa")
            
            # Listar canales
            print(f"\n📋 Obteniendo canales activos...")
            channels = await ami._get_all_channels()
            
            print(f"📞 Canales encontrados: {len(channels)}")
            for i, channel in enumerate(channels, 1):
                print(f"   {i}. {channel}")
            
            if not channels:
                print("   (No hay canales activos - esto es normal)")
            
            await ami.disconnect()
            return True
        else:
            print("❌ Error conectando a AMI")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_hangup_simulation():
    """Simula un hangup con canales ficticios."""
    print(f"\n🎭 SIMULACIÓN DE HANGUP")
    print("=" * 50)
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            print("✅ Conectado para simulación")
            
            # Buscar canal por UUID ficticio
            test_uuid = "test-session-123"
            print(f"🔍 Buscando canal para UUID: {test_uuid}")
            
            channel = await ami.find_channel_by_uuid(test_uuid)
            
            if channel:
                print(f"✅ Canal encontrado: {channel}")
                
                # Intentar hangup (probablemente fallará porque es ficticio)
                print(f"🔌 Intentando hangup del canal...")
                success = await ami.hangup_channel(channel)
                
                if success:
                    print("✅ Hangup exitoso")
                else:
                    print("❌ Hangup falló (esperado para canal ficticio)")
            else:
                print("❌ No se encontró canal (esperado sin llamadas activas)")
            
            await ami.disconnect()
            return True
        else:
            print("❌ No se pudo conectar")
            return False
            
    except Exception as e:
        print(f"❌ Error en simulación: {e}")
        return False


async def main():
    """Función principal."""
    print("🚀 PRUEBA DE AMI CORREGIDO")
    print("=" * 80)
    
    # Prueba 1: Conexión básica
    connection_ok = await test_ami_quick()
    
    # Prueba 2: Simulación de hangup
    hangup_ok = await test_hangup_simulation()
    
    # Resumen
    print("\n" + "=" * 80)
    print("📊 RESUMEN")
    print("=" * 80)
    
    print(f"🔗 Conexión AMI: {'✅ EXITOSA' if connection_ok else '❌ FALLIDA'}")
    print(f"🔌 Funcionalidad Hangup: {'✅ DISPONIBLE' if hangup_ok else '❌ NO DISPONIBLE'}")
    
    if connection_ok and hangup_ok:
        print(f"\n🎉 AMI COMPLETAMENTE FUNCIONAL")
        print(f"   - El sistema puede conectar a Asterisk")
        print(f"   - Los comandos de hangup están listos")
        print(f"   - Las llamadas reales se cortarán automáticamente")
        
        print(f"\n📞 PARA PROBAR CON LLAMADA REAL:")
        print(f"   1. Hacer una llamada al sistema AudioSocket")
        print(f"   2. Decir 'adiós' durante la llamada")
        print(f"   3. La llamada debería cortarse automáticamente")
    else:
        print(f"\n⚠️ PROBLEMAS DETECTADOS")
        if not connection_ok:
            print(f"   - AMI no se puede conectar")
        if not hangup_ok:
            print(f"   - Funcionalidad de hangup no disponible")


if __name__ == "__main__":
    asyncio.run(main())
