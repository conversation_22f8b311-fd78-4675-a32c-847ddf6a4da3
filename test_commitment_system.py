#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script de prueba para el sistema de compromisos de pago.
Simula diferentes frases que los clientes pueden decir.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.commitment_service import CommitmentService
from src.date_calculator import DateCalculator
import asyncio
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


class MockDBManager:
    """Mock del database manager para pruebas."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Datos simulados
        self.deudas = [
            {'id': 1, 'monto': 1500.00, 'concepto': 'Préstamo personal', 'estado': 'vencida'},
            {'id': 2, 'monto': 850.50, 'concepto': 'Tarjeta de crédito', 'estado': 'pendiente'}
        ]
        
        self.compromisos_created = []
    
    async def fetch_one(self, query: str, params: tuple):
        """Simula fetch_one."""
        if "SELECT id, monto, estado FROM deudas WHERE id" in query:
            # Validación de deuda
            deuda_id = params[0]
            for deuda in self.deudas:
                if deuda['id'] == deuda_id:
                    return deuda
            return None
        
        elif "SELECT id, monto, concepto FROM deudas" in query:
            # Búsqueda de deudas del cliente
            if self.deudas:
                return self.deudas[0]  # Retornar primera deuda
            return None
        
        elif "INSERT INTO compromisos_pago" in query:
            # Simular inserción de compromiso
            compromiso_id = len(self.compromisos_created) + 1
            compromiso = {
                'id': compromiso_id,
                'deuda_id': params[0],
                'fecha_compromiso': params[1],
                'monto_acordado': params[2],
                'tipo_compromiso': params[3],
                'observaciones': params[4]
            }
            self.compromisos_created.append(compromiso)
            return {'id': compromiso_id}
        
        return None
    
    async def execute(self, query: str, params: tuple):
        """Simula execute."""
        if "UPDATE deudas SET estado" in query:
            deuda_id = params[0]
            for deuda in self.deudas:
                if deuda['id'] == deuda_id:
                    deuda['estado'] = 'en_compromiso'
                    self.logger.info(f"Deuda {deuda_id} actualizada a 'en_compromiso'")
        return True


async def test_commitment_extraction():
    """Prueba la extracción de compromisos de diferentes frases."""
    
    # Crear mock DB manager
    mock_db = MockDBManager()
    
    # Crear servicio de compromisos
    commitment_service = CommitmentService(mock_db)
    
    # Frases de prueba que los clientes pueden decir
    test_phrases = [
        "Sí, puedo pagar 500 soles mañana",
        "Le pago 1000 el próximo viernes",
        "Puedo abonar 750 pasado mañana",
        "El lunes que viene le deposito 1200",
        "Mañana sin falta le pago 300 soles",
        "Este jueves puedo pagar 850",
        "En una semana le pago todo, 1500",
        "Puedo hacer un pago parcial de 400 el martes",
        "Le abono 600 en dos días",
        "El 15 de marzo puedo pagar 1000",
        "Hoy mismo le deposito 200",
        "Pasado mañana le pago la mitad, 750"
    ]
    
    print("=" * 80)
    print("PRUEBA DEL SISTEMA DE COMPROMISOS DE PAGO")
    print("=" * 80)
    print(f"Cliente ID simulado: 1")
    print(f"Deudas disponibles: {len(mock_db.deudas)}")
    print()
    
    successful_extractions = 0
    total_tests = len(test_phrases)
    
    for i, phrase in enumerate(test_phrases, 1):
        print(f"\n🔄 PRUEBA {i}/{total_tests}")
        print(f"Frase: '{phrase}'")
        print("-" * 50)
        
        try:
            # Procesar compromiso
            result = await commitment_service.procesar_compromiso_desde_conversacion(
                cliente_id=1, 
                texto_compromiso=phrase
            )
            
            if result['success']:
                successful_extractions += 1
                print(f"✅ ÉXITO:")
                print(f"   - Compromiso ID: {result['compromiso_id']}")
                print(f"   - Deuda ID: {result['deuda_id']}")
                print(f"   - Monto: {result['monto_acordado']} soles")
                print(f"   - Fecha: {result['fecha_compromiso']}")
            else:
                print(f"❌ FALLO: {result['error']}")
                
        except Exception as e:
            print(f"💥 ERROR: {e}")
    
    print("\n" + "=" * 80)
    print("RESUMEN DE RESULTADOS")
    print("=" * 80)
    print(f"Total de pruebas: {total_tests}")
    print(f"Extracciones exitosas: {successful_extractions}")
    print(f"Tasa de éxito: {(successful_extractions/total_tests)*100:.1f}%")
    
    print(f"\nCompromisos creados en BD simulada: {len(mock_db.compromisos_created)}")
    
    if mock_db.compromisos_created:
        print("\nDetalle de compromisos creados:")
        for compromiso in mock_db.compromisos_created:
            print(f"  - ID {compromiso['id']}: {compromiso['monto_acordado']} soles el {compromiso['fecha_compromiso']}")
    
    return successful_extractions == total_tests


async def test_date_extraction_only():
    """Prueba solo la extracción de fechas."""
    calculator = DateCalculator()
    
    date_phrases = [
        "mañana",
        "pasado mañana", 
        "el próximo viernes",
        "lunes que viene",
        "este jueves",
        "en una semana",
        "en dos días",
        "hoy mismo"
    ]
    
    print("\n" + "=" * 80)
    print("PRUEBA DE EXTRACCIÓN DE FECHAS")
    print("=" * 80)
    
    for phrase in date_phrases:
        result = calculator.parse_natural_date(phrase)
        if result:
            days_diff = (result - calculator.parse_natural_date("hoy")).days
            print(f"✅ '{phrase}' -> {result} (en {days_diff} días)")
        else:
            print(f"❌ '{phrase}' -> No se pudo calcular")


if __name__ == "__main__":
    print("Iniciando pruebas del sistema de compromisos...")
    
    async def run_tests():
        # Prueba de extracción de fechas
        await test_date_extraction_only()
        
        # Prueba completa del sistema
        success = await test_commitment_extraction()
        
        print("\n" + "=" * 80)
        if success:
            print("🎉 TODAS LAS PRUEBAS DEL SISTEMA PASARON")
        else:
            print("⚠️  ALGUNAS PRUEBAS DEL SISTEMA FALLARON")
        print("=" * 80)
    
    # Ejecutar pruebas
    asyncio.run(run_tests())
