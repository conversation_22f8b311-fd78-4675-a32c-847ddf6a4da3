#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Módulo de Base de Datos
===============================================
Manejo de conexiones y operaciones con PostgreSQL.
Entorno virtual: /opt/cobranza-bot/venv

Este módulo proporciona:
- Conexión a PostgreSQL
- Operaciones CRUD para clientes, deudas y compromisos
- Logging de llamadas
- Consultas optimizadas para el bot de cobranza

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
Versión: 1.0.0
"""

import logging
import asyncio
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, date
from dataclasses import dataclass
from contextlib import asynccontextmanager

try:
    import asyncpg
    from asyncpg import Pool, Connection
except ImportError:
    raise ImportError(
        "asyncpg no está instalado. Ejecute: pip install asyncpg"
    )

from config import get_config


# Configurar logger
logger = logging.getLogger(__name__)


@dataclass
class Cliente:
    """Modelo de datos para cliente."""
    id: Optional[int] = None
    nombre: str = ""
    telefono: str = ""
    email: Optional[str] = None
    created_at: Optional[datetime] = None


@dataclass
class Deuda:
    """Modelo de datos para deuda."""
    id: Optional[int] = None
    cliente_id: int = 0
    monto: float = 0.0
    fecha_vencimiento: Optional[date] = None
    estado: str = "pendiente"
    created_at: Optional[datetime] = None


@dataclass
class CompromisoPago:
    """Modelo de datos para compromiso de pago."""
    id: Optional[int] = None
    deuda_id: int = 0
    fecha_compromiso: Optional[date] = None
    monto_acordado: float = 0.0
    created_at: Optional[datetime] = None


@dataclass
class LogLlamada:
    """Modelo de datos para log de llamada."""
    id: Optional[int] = None
    cliente_id: Optional[int] = None
    uuid_llamada: str = ""
    transcripcion: Optional[str] = None
    resultado: Optional[str] = None
    duracion: Optional[int] = None
    created_at: Optional[datetime] = None


class DatabaseManager:
    """Gestor de base de datos para el bot de cobranza."""
    
    def __init__(self):
        self.config = get_config()
        self.pool: Optional[Pool] = None
        self._connection_string = self.config.database.connection_string
    
    async def initialize(self) -> None:
        """Inicializa el pool de conexiones."""
        try:
            self.pool = await asyncpg.create_pool(
                self._connection_string,
                min_size=5,
                max_size=self.config.database.pool_size,
                max_inactive_connection_lifetime=300
            )
            logger.info("Pool de conexiones PostgreSQL inicializado")
            
            # Verificar conexión
            await self.health_check()
            
        except Exception as e:
            logger.error(f"Error inicializando base de datos: {e}")
            raise
    
    async def close(self) -> None:
        """Cierra el pool de conexiones."""
        if self.pool:
            await self.pool.close()
            logger.info("Pool de conexiones cerrado")
    
    async def health_check(self) -> bool:
        """Verifica la salud de la conexión a la base de datos."""
        try:
            async with self.pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                return result == 1
        except Exception as e:
            logger.error(f"Health check falló: {e}")
            return False
    
    @asynccontextmanager
    async def get_connection(self):
        """Context manager para obtener una conexión del pool."""
        if not self.pool:
            raise RuntimeError("Base de datos no inicializada")
        
        async with self.pool.acquire() as conn:
            yield conn
    
    # ==========================================
    # MÉTODOS GENÉRICOS DE CONSULTA
    # ==========================================

    async def fetch_one(self, query: str, params: tuple = ()) -> Optional[dict]:
        """Ejecuta una consulta y retorna una fila como diccionario."""
        try:
            async with self.get_connection() as conn:
                row = await conn.fetchrow(query, *params)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Error en fetch_one: {e}")
            return None

    async def fetch_all(self, query: str, params: tuple = ()) -> List[dict]:
        """Ejecuta una consulta y retorna todas las filas como lista de diccionarios."""
        try:
            async with self.get_connection() as conn:
                rows = await conn.fetch(query, *params)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error en fetch_all: {e}")
            return []

    # ==========================================
    # OPERACIONES DE CLIENTES
    # ==========================================

    # Método de búsqueda por teléfono eliminado por requerimiento del usuario
    # La funcionalidad de búsqueda de clientes se manejará por otros medios
    
    async def crear_cliente(self, cliente: Cliente) -> Optional[int]:
        """Crea un nuevo cliente y retorna su ID."""
        try:
            async with self.get_connection() as conn:
                query = """
                    INSERT INTO clientes (nombre, telefono, email)
                    VALUES ($1, $2, $3)
                    RETURNING id
                """
                cliente_id = await conn.fetchval(
                    query, cliente.nombre, cliente.telefono, cliente.email
                )
                
                logger.info(f"Cliente creado: {cliente.nombre} (ID: {cliente_id})")
                return cliente_id
                
        except Exception as e:
            logger.error(f"Error creando cliente: {e}")
            return None
    
    async def actualizar_cliente(self, cliente: Cliente) -> bool:
        """Actualiza información de un cliente."""
        try:
            async with self.get_connection() as conn:
                query = """
                    UPDATE clientes
                    SET nombre = $2, telefono = $3, email = $4
                    WHERE id = $1
                """
                result = await conn.execute(
                    query, cliente.id, cliente.nombre, cliente.telefono, cliente.email
                )
                
                success = result.split()[-1] == '1'
                if success:
                    logger.info(f"Cliente actualizado: ID {cliente.id}")
                return success
                
        except Exception as e:
            logger.error(f"Error actualizando cliente ID {cliente.id}: {e}")
            return False
    
    # ==========================================
    # OPERACIONES DE DEUDAS
    # ==========================================
    
    async def obtener_deudas_cliente(self, cliente_id: int) -> List[Deuda]:
        """Obtiene todas las deudas pendientes de un cliente."""
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT id, cliente_id, monto, fecha_vencimiento, estado, created_at
                    FROM deudas
                    WHERE cliente_id = $1 AND estado = 'pendiente'
                    ORDER BY fecha_vencimiento ASC
                """
                rows = await conn.fetch(query, cliente_id)
                
                deudas = []
                for row in rows:
                    deudas.append(Deuda(
                        id=row['id'],
                        cliente_id=row['cliente_id'],
                        monto=float(row['monto']),
                        fecha_vencimiento=row['fecha_vencimiento'],
                        estado=row['estado'],
                        created_at=row['created_at']
                    ))
                
                return deudas
                
        except Exception as e:
            logger.error(f"Error obteniendo deudas del cliente {cliente_id}: {e}")
            return []
    
    async def obtener_deuda_total_cliente(self, cliente_id: int) -> float:
        """Calcula el total de deuda pendiente de un cliente."""
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT COALESCE(SUM(monto), 0) as total
                    FROM deudas
                    WHERE cliente_id = $1 AND estado = 'pendiente'
                """
                total = await conn.fetchval(query, cliente_id)
                return float(total) if total else 0.0
                
        except Exception as e:
            logger.error(f"Error calculando deuda total del cliente {cliente_id}: {e}")
            return 0.0
    
    async def crear_deuda(self, deuda: Deuda) -> Optional[int]:
        """Crea una nueva deuda y retorna su ID."""
        try:
            async with self.get_connection() as conn:
                query = """
                    INSERT INTO deudas (cliente_id, monto, fecha_vencimiento, estado)
                    VALUES ($1, $2, $3, $4)
                    RETURNING id
                """
                deuda_id = await conn.fetchval(
                    query, deuda.cliente_id, deuda.monto, 
                    deuda.fecha_vencimiento, deuda.estado
                )
                
                logger.info(f"Deuda creada: ${deuda.monto} (ID: {deuda_id})")
                return deuda_id
                
        except Exception as e:
            logger.error(f"Error creando deuda: {e}")
            return None
    
    async def actualizar_estado_deuda(self, deuda_id: int, nuevo_estado: str) -> bool:
        """Actualiza el estado de una deuda."""
        try:
            async with self.get_connection() as conn:
                query = """
                    UPDATE deudas
                    SET estado = $2
                    WHERE id = $1
                """
                result = await conn.execute(query, deuda_id, nuevo_estado)
                
                success = result.split()[-1] == '1'
                if success:
                    logger.info(f"Estado de deuda actualizado: ID {deuda_id} -> {nuevo_estado}")
                return success
                
        except Exception as e:
            logger.error(f"Error actualizando estado de deuda ID {deuda_id}: {e}")
            return False
    
    # ==========================================
    # OPERACIONES DE COMPROMISOS DE PAGO
    # ==========================================
    
    async def crear_compromiso_pago(self, compromiso: CompromisoPago) -> Optional[int]:
        """Crea un nuevo compromiso de pago."""
        try:
            async with self.get_connection() as conn:
                # Iniciar transacción
                async with conn.transaction():
                    # Crear compromiso
                    query_compromiso = """
                        INSERT INTO compromisos_pago (deuda_id, fecha_compromiso, monto_acordado)
                        VALUES ($1, $2, $3)
                        RETURNING id
                    """
                    compromiso_id = await conn.fetchval(
                        query_compromiso, compromiso.deuda_id, 
                        compromiso.fecha_compromiso, compromiso.monto_acordado
                    )
                    
                    # Actualizar estado de la deuda
                    await conn.execute(
                        "UPDATE deudas SET estado = 'en_compromiso' WHERE id = $1",
                        compromiso.deuda_id
                    )
                    
                    logger.info(
                        f"Compromiso de pago creado: ${compromiso.monto_acordado} "
                        f"para {compromiso.fecha_compromiso} (ID: {compromiso_id})"
                    )
                    return compromiso_id
                    
        except Exception as e:
            logger.error(f"Error creando compromiso de pago: {e}")
            return None
    
    async def obtener_compromisos_cliente(self, cliente_id: int) -> List[CompromisoPago]:
        """Obtiene todos los compromisos de pago de un cliente."""
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT cp.id, cp.deuda_id, cp.fecha_compromiso, 
                           cp.monto_acordado, cp.created_at
                    FROM compromisos_pago cp
                    JOIN deudas d ON cp.deuda_id = d.id
                    WHERE d.cliente_id = $1
                    ORDER BY cp.fecha_compromiso ASC
                """
                rows = await conn.fetch(query, cliente_id)
                
                compromisos = []
                for row in rows:
                    compromisos.append(CompromisoPago(
                        id=row['id'],
                        deuda_id=row['deuda_id'],
                        fecha_compromiso=row['fecha_compromiso'],
                        monto_acordado=float(row['monto_acordado']),
                        created_at=row['created_at']
                    ))
                
                return compromisos
                
        except Exception as e:
            logger.error(f"Error obteniendo compromisos del cliente {cliente_id}: {e}")
            return []
    
    # ==========================================
    # OPERACIONES DE LOGS DE LLAMADAS
    # ==========================================
    
    async def crear_log_llamada(self, log: LogLlamada) -> Optional[int]:
        """Crea un nuevo log de llamada."""
        try:
            async with self.get_connection() as conn:
                query = """
                    INSERT INTO logs_llamadas 
                    (cliente_id, uuid_llamada, transcripcion, resultado, duracion)
                    VALUES ($1, $2, $3, $4, $5)
                    RETURNING id
                """
                log_id = await conn.fetchval(
                    query, log.cliente_id, log.uuid_llamada, 
                    log.transcripcion, log.resultado, log.duracion
                )
                
                logger.info(f"Log de llamada creado: {log.uuid_llamada} (ID: {log_id})")
                return log_id
                
        except Exception as e:
            logger.error(f"Error creando log de llamada: {e}")
            return None
    
    async def actualizar_log_llamada(self, uuid_llamada: str,
                                    resultado: Optional[str] = None,
                                    notas: Optional[str] = None,
                                    duracion: Optional[int] = None) -> bool:
        """Actualiza un log de llamada existente."""
        try:
            async with self.get_connection() as conn:
                # Construir query dinámicamente
                updates = []
                params = []
                param_count = 1
                
                if resultado is not None:
                    updates.append(f"resultado = ${param_count}")
                    params.append(resultado)
                    param_count += 1
                
                if notas is not None:
                    updates.append(f"notas = ${param_count}")
                    params.append(notas)
                    param_count += 1
                
                if duracion is not None:
                    updates.append(f"duracion = ${param_count}")
                    params.append(duracion)
                    param_count += 1
                
                if not updates:
                    return True  # No hay nada que actualizar
                
                # Agregar timestamp de actualización
                updates.append(f"updated_at = ${param_count}")
                params.append(datetime.now())
                param_count += 1
                
                # Agregar UUID al final
                params.append(uuid_llamada)
                
                query = f"""
                    UPDATE logs_llamadas
                    SET {', '.join(updates)}
                    WHERE uuid_llamada = ${param_count}
                """
                
                result = await conn.execute(query, *params)
                
                logger.info(f"Log de llamada actualizado: {uuid_llamada}")
                return True
                
        except Exception as e:
            logger.error(f"Error actualizando log de llamada {uuid_llamada}: {e}")
            return False
    
    async def log_call(self, telefono: str, duracion: int, estado: str, notas: str = "") -> bool:
        """Registra una llamada en la base de datos sin buscar cliente específico."""
        try:
            # Crear log de llamada sin asociar a cliente específico
            # El teléfono se puede almacenar en las notas o transcripción para referencia
            import uuid
            log = LogLlamada(
                cliente_id=None,  # No se asocia a cliente específico
                uuid_llamada=str(uuid.uuid4()),
                duracion=duracion,
                resultado=estado,
                transcripcion=f"Teléfono: {telefono}. {notas}" if notas else f"Teléfono: {telefono}"
            )
            
            log_id = await self.crear_log_llamada(log)
            return log_id is not None
            
        except Exception as e:
            logger.error(f"Error registrando llamada: {e}")
            return False
    
    # ==========================================
    # CONSULTAS DE ESTADÍSTICAS
    # ==========================================
    
    async def obtener_estadisticas_llamadas(self, dias: int = 30) -> Dict[str, Any]:
        """Obtiene estadísticas de llamadas de los últimos N días."""
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT 
                        COUNT(*) as total_llamadas,
                        COUNT(CASE WHEN resultado = 'compromiso_obtenido' THEN 1 END) as compromisos_exitosos,
                        COUNT(CASE WHEN resultado = 'sin_respuesta' THEN 1 END) as sin_respuesta,
                        COUNT(CASE WHEN resultado = 'cliente_no_encontrado' THEN 1 END) as no_encontrados,
                        AVG(duracion) as duracion_promedio
                    FROM logs_llamadas
                    WHERE created_at >= NOW() - INTERVAL '%s days'
                """ % dias
                
                row = await conn.fetchrow(query)
                
                return {
                    'total_llamadas': row['total_llamadas'],
                    'compromisos_exitosos': row['compromisos_exitosos'],
                    'sin_respuesta': row['sin_respuesta'],
                    'no_encontrados': row['no_encontrados'],
                    'duracion_promedio': float(row['duracion_promedio']) if row['duracion_promedio'] else 0,
                    'tasa_exito': (row['compromisos_exitosos'] / row['total_llamadas'] * 100) if row['total_llamadas'] > 0 else 0
                }
                
        except Exception as e:
            logger.error(f"Error obteniendo estadísticas: {e}")
            return {}


# Instancia global del gestor de base de datos
db_manager = DatabaseManager()


async def get_db_manager() -> DatabaseManager:
    """Retorna la instancia global del gestor de base de datos."""
    if not db_manager.pool:
        await db_manager.initialize()
    return db_manager


if __name__ == "__main__":
    # Prueba de conexión
    async def test_connection():
        try:
            db = await get_db_manager()
            health = await db.health_check()
            print(f"✅ Conexión a base de datos: {'OK' if health else 'FALLO'}")
            
            # Prueba de estadísticas
            stats = await db.obtener_estadisticas_llamadas()
            print(f"📊 Estadísticas: {stats}")
            
            await db.close()
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    asyncio.run(test_connection())