#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Debt Service
============

Servicio para consultar información de deudas y clientes en la base de datos.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import logging
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from decimal import Decimal
from datetime import date


@dataclass
class ClienteInfo:
    """Información del cliente."""
    id: int
    nombre: str
    telefono: str
    email: Optional[str]
    documento_identidad: str
    tipo_documento: str


@dataclass
class DeudaInfo:
    """Información de una deuda."""
    id: int
    numero_factura: Optional[str]
    concepto: str
    monto: Decimal
    monto_original: Decimal
    fecha_emision: date
    fecha_vencimiento: date
    estado: str
    dias_vencido: int
    prioridad: str
    moneda: str


class DebtService:
    """Servicio para consultar información de deudas."""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    async def buscar_cliente_por_dni(self, dni: str) -> Optional[ClienteInfo]:
        """Busca un cliente por su DNI."""
        try:
            # Limpiar DNI (solo números)
            dni_limpio = ''.join(filter(str.isdigit, dni))
            
            if len(dni_limpio) != 8:
                self.logger.warning(f"DNI inválido: {dni} (debe tener 8 dígitos)")
                return None
            
            query = """
                SELECT id, nombre, telefono, email, documento_identidad, tipo_documento
                FROM clientes
                WHERE documento_identidad = $1
                  AND activo = TRUE
                LIMIT 1
            """
            
            result = await self.db_manager.fetch_one(query, (dni_limpio,))
            
            if result:
                return ClienteInfo(
                    id=result['id'],
                    nombre=result['nombre'],
                    telefono=result['telefono'],
                    email=result['email'],
                    documento_identidad=result['documento_identidad'],
                    tipo_documento=result['tipo_documento']
                )
            
            self.logger.info(f"Cliente no encontrado para DNI: {dni_limpio}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error buscando cliente por DNI {dni}: {e}")
            return None
    
    async def obtener_deudas_cliente(self, cliente_id: int) -> List[DeudaInfo]:
        """Obtiene todas las deudas de un cliente."""
        try:
            query = """
                SELECT id, numero_factura, concepto, monto, monto_original,
                       fecha_emision, fecha_vencimiento, estado, dias_vencido,
                       prioridad, moneda
                FROM deudas
                WHERE cliente_id = $1
                  AND estado IN ('pendiente', 'vencida', 'en_cobranza')
                ORDER BY dias_vencido DESC, monto DESC
            """
            
            results = await self.db_manager.fetch_all(query, (cliente_id,))
            
            deudas = []
            for row in results:
                deudas.append(DeudaInfo(
                    id=row['id'],
                    numero_factura=row['numero_factura'],
                    concepto=row['concepto'],
                    monto=row['monto'],
                    monto_original=row['monto_original'],
                    fecha_emision=row['fecha_emision'],
                    fecha_vencimiento=row['fecha_vencimiento'],
                    estado=row['estado'],
                    dias_vencido=row['dias_vencido'],
                    prioridad=row['prioridad'],
                    moneda=row['moneda']
                ))
            
            self.logger.info(f"Encontradas {len(deudas)} deudas para cliente {cliente_id}")
            return deudas
            
        except Exception as e:
            self.logger.error(f"Error obteniendo deudas del cliente {cliente_id}: {e}")
            return []
    
    async def obtener_resumen_deuda(self, cliente_id: int) -> Dict[str, Any]:
        """Obtiene un resumen de la deuda del cliente."""
        try:
            query = """
                SELECT
                    COUNT(*) as total_deudas,
                    COALESCE(SUM(monto), 0) as deuda_total,
                    COALESCE(SUM(CASE WHEN estado = 'vencida' THEN monto ELSE 0 END), 0) as deuda_vencida,
                    COALESCE(MAX(dias_vencido), 0) as max_dias_vencido,
                    COALESCE(MIN(fecha_vencimiento), CURRENT_DATE) as proxima_fecha_vencimiento
                FROM deudas
                WHERE cliente_id = $1
                  AND estado IN ('pendiente', 'vencida', 'en_cobranza')
            """
            
            result = await self.db_manager.fetch_one(query, (cliente_id,))
            
            if result:
                return {
                    'total_deudas': result['total_deudas'],
                    'deuda_total': float(result['deuda_total']),
                    'deuda_vencida': float(result['deuda_vencida']),
                    'max_dias_vencido': result['max_dias_vencido'],
                    'proxima_fecha_vencimiento': result['proxima_fecha_vencimiento'],
                    'tiene_deudas': result['total_deudas'] > 0
                }
            
            return {
                'total_deudas': 0,
                'deuda_total': 0.0,
                'deuda_vencida': 0.0,
                'max_dias_vencido': 0,
                'proxima_fecha_vencimiento': None,
                'tiene_deudas': False
            }
            
        except Exception as e:
            self.logger.error(f"Error obteniendo resumen de deuda del cliente {cliente_id}: {e}")
            return {
                'total_deudas': 0,
                'deuda_total': 0.0,
                'deuda_vencida': 0.0,
                'max_dias_vencido': 0,
                'proxima_fecha_vencimiento': None,
                'tiene_deudas': False,
                'error': str(e)
            }
    
    async def validar_dni_y_obtener_info(self, dni: str) -> Dict[str, Any]:
        """Valida DNI y obtiene información completa del cliente y sus deudas."""
        try:
            # Buscar cliente
            cliente = await self.buscar_cliente_por_dni(dni)
            
            if not cliente:
                return {
                    'encontrado': False,
                    'mensaje': f"No se encontró ningún cliente con DNI {dni}",
                    'cliente': None,
                    'deudas': [],
                    'resumen': None
                }
            
            # Obtener resumen de deudas
            resumen = await self.obtener_resumen_deuda(cliente.id)
            
            # Obtener deudas detalladas
            deudas = await self.obtener_deudas_cliente(cliente.id)
            
            return {
                'encontrado': True,
                'mensaje': f"Cliente {cliente.nombre} encontrado",
                'cliente': {
                    'id': cliente.id,
                    'nombre': cliente.nombre,
                    'telefono': cliente.telefono,
                    'email': cliente.email,
                    'documento_identidad': cliente.documento_identidad,
                    'tipo_documento': cliente.tipo_documento
                },
                'deudas': [
                    {
                        'id': d.id,
                        'numero_factura': d.numero_factura,
                        'concepto': d.concepto,
                        'monto': float(d.monto),
                        'monto_original': float(d.monto_original),
                        'fecha_emision': d.fecha_emision.isoformat(),
                        'fecha_vencimiento': d.fecha_vencimiento.isoformat(),
                        'estado': d.estado,
                        'dias_vencido': d.dias_vencido,
                        'prioridad': d.prioridad,
                        'moneda': d.moneda
                    }
                    for d in deudas
                ],
                'resumen': resumen
            }
            
        except Exception as e:
            self.logger.error(f"Error validando DNI {dni}: {e}")
            return {
                'encontrado': False,
                'mensaje': f"Error interno al consultar DNI {dni}",
                'cliente': None,
                'deudas': [],
                'resumen': None,
                'error': str(e)
            }
    
    def formatear_resumen_deuda(self, info: Dict[str, Any]) -> str:
        """Formatea el resumen de deuda para respuesta de voz ULTRA CORTA."""
        try:
            if not info['encontrado']:
                return "DNI no encontrado. ¿Puede repetirlo?"

            cliente = info['cliente']
            resumen = info['resumen']

            if not resumen['tiene_deudas']:
                return f"{cliente['nombre']}, sin deudas pendientes."

            # Respuesta ULTRA CORTA
            nombre = cliente['nombre'].split()[0]  # Solo primer nombre
            total = resumen['deuda_total']

            if resumen['deuda_vencida'] > 0:
                return f"{nombre}, debe {total:.0f} soles vencidos. ¿Puede pagar?"
            else:
                return f"{nombre}, debe {total:.0f} soles. ¿Puede pagar?"

        except Exception as e:
            self.logger.error(f"Error formateando resumen: {e}")
            return "Error consultando. ¿Repetir DNI?"
