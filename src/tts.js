const a414_0x117588=a414_0x2040;function a414_0x5bf0(){const _0x33f698=['40pOOdqc','end','http://localhost:6003/text-to-speech-stream','2903397mxYphg','shift','2GvNodb','axios','4bxOfsc','1424226yuOcYY','config','data','isSendingText','dotenv','5198175BIlNuT','concat','2533495UiuQRa','10919277gzpKzR','TTS\x20streaming\x20complete:','error','emit','push','bytes','exports','7638992jAkQUq','post','events','stream','4966554KCFVYg','sendToTts','processTextQueue','length','textQueue','audio','message','info'];a414_0x5bf0=function(){return _0x33f698;};return a414_0x5bf0();}(function(_0x223627,_0x1b9c6e){const _0x6c94d1=a414_0x2040,_0x2a32fb=_0x223627();while(!![]){try{const _0x33b2fd=parseInt(_0x6c94d1(0x8c))/0x1*(-parseInt(_0x6c94d1(0x8f))/0x2)+-parseInt(_0x6c94d1(0x94))/0x3+parseInt(_0x6c94d1(0x8e))/0x4*(-parseInt(_0x6c94d1(0x96))/0x5)+-parseInt(_0x6c94d1(0xa2))/0x6+-parseInt(_0x6c94d1(0xad))/0x7+parseInt(_0x6c94d1(0x9e))/0x8+-parseInt(_0x6c94d1(0x97))/0x9*(-parseInt(_0x6c94d1(0xaa))/0xa);if(_0x33b2fd===_0x1b9c6e)break;else _0x2a32fb['push'](_0x2a32fb['shift']());}catch(_0x4471ca){_0x2a32fb['push'](_0x2a32fb['shift']());}}}(a414_0x5bf0,0xdc24a),require(a414_0x117588(0x93))[a414_0x117588(0x90)]());function a414_0x2040(_0x1aa429,_0xdcb7b7){const _0x5bf0f=a414_0x5bf0();return a414_0x2040=function(_0x20403f,_0x4c0131){_0x20403f=_0x20403f-0x8c;let _0x212671=_0x5bf0f[_0x20403f];return _0x212671;},a414_0x2040(_0x1aa429,_0xdcb7b7);}const axios=require(a414_0x117588(0x8d)),EventEmitter=require(a414_0x117588(0xa0)),logger=require('./logger');class Tts extends EventEmitter{constructor(){const _0x520151=a414_0x117588;super(),this['textQueue']=[],this[_0x520151(0x92)]=![];}async[a414_0x117588(0xa3)](_0x385d1c){const _0x2987b5=a414_0x117588;this[_0x2987b5(0xa6)]['push'](_0x385d1c),this[_0x2987b5(0xa4)]();}async['processTextQueue'](){const _0x3432e9=a414_0x117588;if(this[_0x3432e9(0x92)]||this[_0x3432e9(0xa6)][_0x3432e9(0xa5)]===0x0)return;this[_0x3432e9(0x92)]=!![];const _0x140fbf=this[_0x3432e9(0xa6)][_0x3432e9(0xae)](),_0x32d8ff=[];try{const _0x28fa9e=await axios({'method':_0x3432e9(0x9f),'url':process['env']['TTS_URL']||_0x3432e9(0xac),'data':{'text':_0x140fbf},'responseType':_0x3432e9(0xa1)});_0x28fa9e[_0x3432e9(0x91)]['on'](_0x3432e9(0x91),_0xc70a44=>{const _0x45ba94=_0x3432e9;_0x32d8ff[_0x45ba94(0x9b)](_0xc70a44);}),_0x28fa9e[_0x3432e9(0x91)]['on'](_0x3432e9(0xab),()=>{const _0xa40b36=_0x3432e9,_0x4ce8f2=Buffer[_0xa40b36(0x95)](_0x32d8ff);this[_0xa40b36(0x9a)](_0xa40b36(0xa7),_0x4ce8f2),logger[_0xa40b36(0xa9)](_0xa40b36(0x98),_0x4ce8f2[_0xa40b36(0xa5)],_0xa40b36(0x9c)),this['emit']('end'),this[_0xa40b36(0x92)]=![],this['processTextQueue']();}),_0x28fa9e[_0x3432e9(0x91)]['on'](_0x3432e9(0x99),_0x4da565=>{const _0x38d9e8=_0x3432e9;logger[_0x38d9e8(0x99)]('Error\x20during\x20TTS\x20streaming:\x20'+_0x4da565),this[_0x38d9e8(0x9a)](_0x38d9e8(0x99),_0x4da565),this[_0x38d9e8(0x92)]=![],this[_0x38d9e8(0xa4)]();});}catch(_0x4102fc){logger[_0x3432e9(0x99)]('Error\x20sending\x20text\x20to\x20TTS\x20service:\x20'+_0x4102fc[_0x3432e9(0xa8)]),this[_0x3432e9(0x9a)](_0x3432e9(0x99),_0x4102fc),this[_0x3432e9(0x92)]=![],this['processTextQueue']();}}['clearTextQueue'](){const _0x4a5c52=a414_0x117588;this[_0x4a5c52(0xa6)]=[],this[_0x4a5c52(0x92)]=![];}}module[a414_0x117588(0x9d)]={'Tts':Tts};