#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Servidor AudioSocket Refactorizado
==========================================================

Servidor principal AudioSocket refactorizado para mejor mantenimiento.
Archivo principal < 400 líneas con componentes separados.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import logging
import socket
from typing import Dict, Optional, Callable, Any

from config import Config
from database import DatabaseManager
from connection_handler import ConnectionHandler


class AudioSocketServer:
    """Servidor AudioSocket refactorizado para comunicación con Asterisk."""
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        
        # Servidor
        self.server: Optional[asyncio.Server] = None
        self.is_running = False
        self.call_event_callback = None
        
        # Estadísticas
        self.stats = {
            'total_calls': 0,
            'active_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0
        }
        
        # Handler de conexiones (contiene toda la lógica de procesamiento)
        self.connection_handler = ConnectionHandler(config, db_manager, self.logger, self.stats)
    
    async def start_server(self) -> None:
        """Inicia el servidor AudioSocket con configuraciones optimizadas."""
        try:
            # Configurar el servidor con opciones de socket optimizadas
            self.server = await asyncio.start_server(
                self.connection_handler.handle_connection,
                self.config.audiosocket.host,
                self.config.audiosocket.port,
                # Configuraciones de socket para mejor estabilidad
                reuse_address=True,
                reuse_port=True,
                backlog=100  # Aumentar backlog para manejar más conexiones concurrentes
            )

            # Configurar opciones de socket a nivel TCP
            self._configure_server_sockets()

            self.is_running = True
            addr = self.server.sockets[0].getsockname()
            self.logger.info(f"AudioSocket server iniciado en {addr[0]}:{addr[1]} con configuraciones optimizadas")
            self.logger.info(f"Configuración: buffer_size={self.config.audiosocket.buffer_size}, max_retries={self.config.audiosocket.max_retries}, chunk_timeout={self.config.audiosocket.chunk_timeout}s")

            async with self.server:
                await self.server.serve_forever()

        except Exception as e:
            self.logger.error(f"Error iniciando servidor AudioSocket: {e}")
            raise
    
    def _configure_server_sockets(self) -> None:
        """Configura opciones de socket a nivel TCP."""
        for sock in self.server.sockets:
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            sock.setsockopt(socket.SOL_TCP, socket.TCP_NODELAY, 1)  # Deshabilitar algoritmo de Nagle

    async def stop_server(self) -> None:
        """Detiene el servidor AudioSocket."""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            self.is_running = False
            self.logger.info("AudioSocket server detenido")

    async def cleanup(self) -> None:
        """Limpia recursos del servidor."""
        try:
            # Limpiar todas las sesiones activas a través del connection handler
            active_sessions = self.connection_handler.session_manager.get_all_sessions()
            for session_id in list(active_sessions.keys()):
                await self.connection_handler.session_manager.cleanup_session(session_id)

            # Detener servidor si está corriendo
            if self.is_running:
                await self.stop_server()

            self.logger.info("Limpieza del AudioSocket server completada")

        except Exception as e:
            self.logger.error(f"Error durante limpieza del servidor: {e}")
    
    async def start(self) -> None:
        """Inicia el servidor AudioSocket."""
        await self.start_server()
    
    async def stop(self) -> None:
         """Detiene el servidor AudioSocket."""
         await self.stop_server()
    
    def set_call_event_callback(self, callback: Callable) -> None:
        """Establece el callback para eventos de llamada."""
        self.call_event_callback = callback
        # Propagar el callback al connection handler si es necesario
        if hasattr(self.connection_handler, 'set_call_event_callback'):
            self.connection_handler.set_call_event_callback(callback)
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estadísticas del servidor."""
        return {
            **self.stats,
            'active_sessions': self.connection_handler.session_manager.get_active_sessions_count(),
            'server_running': self.is_running,
            'audiosocket_config': {
                'host': self.config.audiosocket.host,
                'port': self.config.audiosocket.port,
                'buffer_size': self.config.audiosocket.buffer_size,
                'max_retries': self.config.audiosocket.max_retries,
                'chunk_timeout': self.config.audiosocket.chunk_timeout,
                'connection_timeout': self.config.audiosocket.connection_timeout
            }
        }
    
    def get_active_sessions(self) -> Dict[str, Any]:
        """Retorna información de sesiones activas."""
        return {
            'count': self.connection_handler.session_manager.get_active_sessions_count(),
            'sessions': {
                session_id: {
                    'caller_number': session.caller_number,
                    'start_time': session.start_time.isoformat(),
                    'conversation_state': session.conversation_state,
                    'audio_playing': session.audio_playing,
                    'audio_interrupted': session.audio_interrupted
                }
                for session_id, session in self.connection_handler.session_manager.get_all_sessions().items()
            }
        }


async def main():
    """Función principal para ejecutar el servidor."""
    from config import Config
    from database import DatabaseManager
    
    # Configurar logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    config = Config()
    db_manager = DatabaseManager(config.database)
    server = AudioSocketServer(config, db_manager)
    
    try:
        await server.start_server()
    except KeyboardInterrupt:
        logging.info("Deteniendo servidor...")
    finally:
        await server.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
