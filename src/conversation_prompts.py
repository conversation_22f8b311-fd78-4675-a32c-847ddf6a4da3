#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Prompts de Conversación
===============================================

Este módulo maneja los prompts del sistema para diferentes estados de conversación.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

from typing import Dict
from conversation_models import ConversationContext, ConversationState


class ConversationPrompts:
    """Manejador de prompts para el sistema de conversación."""
    
    def __init__(self):
        self.system_prompts = self._load_system_prompts()
    
    def _load_system_prompts(self) -> Dict[str, str]:
        """Carga los prompts del sistema para diferentes estados."""
        return {
            'base_system': """
Eres un asistente de cobranza telefónica.

CRÍTICO: RESPUESTAS ULTRA CORTAS - Máximo 1 oración de 10-15 palabras.

Reglas:
1. Sé directo y conciso
2. Una sola pregunta por respuesta
3. Sin explicaciones largas
4. Máximo 15 palabras por respuesta

EJEMPLOS CORRECTOS:
- "¿Su DNI por favor?"
- "Tiene deuda de 1500 soles vencida."
- "¿Puede pagar hoy?"

RECUERDA: ULTRA CORTO SIEMPRE.
""",
            
            'identificacion': """
Estás en la fase de identificación del cliente.
Debes confirmar que estás hablando con la persona correcta antes de discutir información financiera.
Sé cortés pero persistente en verificar la identidad.
""",
            
            'negociacion': """
Estás negociando opciones de pago con el cliente.
Muestra flexibilidad y busca soluciones que beneficien a ambas partes.
Explica claramente los beneficios de resolver la deuda.
""",
            
            'compromiso': """
Estás formalizando un compromiso de pago.
Asegúrate de que todos los detalles estén claros: monto, fecha, método de pago.
Confirma que el cliente entiende y acepta los términos.
"""
        }
    
    def build_system_prompt(self, context: ConversationContext) -> str:
        """Construye el prompt del sistema basado en el contexto."""
        base_prompt = self.system_prompts['base_system']
        
        if context.cliente_id:
            # Formatear deuda de forma segura
            deuda_str = f"${context.deuda_total:,.2f}" if context.deuda_total is not None else "No disponible"
            cliente_info = f"""
Información del cliente:
- Nombre: {context.nombre_cliente}
- Teléfono: {context.telefono}
- Deuda total: {deuda_str}
- Número de deudas activas: {len(context.deudas_activas)}
"""
            base_prompt += "\n" + cliente_info
        else:
            base_prompt += f"\nTeléfono llamado: {context.telefono} (cliente no identificado en sistema)"
        
        # Agregar prompt específico del estado
        if context.estado == ConversationState.IDENTIFICANDO:
            base_prompt += "\n" + self.system_prompts['identificacion']
        elif context.estado == ConversationState.NEGOCIANDO:
            base_prompt += "\n" + self.system_prompts['negociacion']
        elif context.estado == ConversationState.COMPROMETIENDO:
            base_prompt += "\n" + self.system_prompts['compromiso']
        
        return base_prompt
    
    def get_intent_detection_prompt(self, text: str, state: ConversationState) -> str:
        """Genera prompt para detección de intenciones."""
        return f"""
Analiza el siguiente texto y determina la intención del usuario en el contexto de una llamada de cobranza.

Texto: "{text}"
Estado actual: {state.value}

Intenciones posibles:
- saludo: El usuario saluda o responde al saludo
- confirmacion_identidad: Confirma ser la persona buscada
- negacion_identidad: Niega ser la persona buscada
- reconocimiento_deuda: Reconoce tener la deuda
- negacion_deuda: Niega tener la deuda
- solicitud_informacion: Pide más información sobre la deuda
- propuesta_pago: Propone una forma o fecha de pago
- compromiso_pago: Acepta un compromiso de pago
- solicitud_descuento: Pide descuento o reducción
- despedida: Se despide educadamente
- colgar: Quiere terminar la llamada abruptamente
- otro: Otra intención

Responde en formato JSON:
{{
  "intent": "nombre_intencion",
  "confidence": 0.95,
  "reasoning": "explicación breve"
}}
"""
    
    def get_response_generation_prompt(self, intent_value: str, confidence: float, state: ConversationState, context=None) -> str:
        """Genera prompt para generación de respuestas."""

        # Información del cliente si está identificado
        cliente_info = ""
        if context and context.cliente_id:
            cliente_info = f"""
CLIENTE IDENTIFICADO:
- ID: {context.cliente_id}
- Nombre: {context.nombre_cliente}
- Deuda total: {context.deuda_total:.2f} soles
- Estado: Cliente YA identificado y validado

IMPORTANTE: El cliente YA está identificado. NO pidas DNI otra vez.
"""
        else:
            cliente_info = """
CLIENTE NO IDENTIFICADO:
- Necesita proporcionar DNI de 8 dígitos para continuar
- NO dar información de deuda sin identificación
"""

        return f"""
Intención detectada: {intent_value} (confianza: {confidence:.2f})
Estado actual: {state.value}

{cliente_info}

🚨 REGLA CRÍTICA DE SEGURIDAD:
- Si el cliente YA está identificado, NO pidas DNI otra vez
- Si el cliente NO está identificado, SIEMPRE pide DNI primero
- NUNCA inventes información financiera
- Usa la información del cliente identificado para responder apropiadamente

Genera una respuesta apropiada que:
1. Responda a la intención del usuario
2. Mantenga el flujo de la conversación hacia el objetivo de cobranza
3. Sea empática y profesional
4. Incluya próximos pasos claros
5. RESPETE las reglas de seguridad arriba mencionadas

Formato de respuesta JSON:
{{
  "response_text": "texto de respuesta al cliente",
  "next_state": "próximo estado de conversación",
  "actions": ["lista de acciones a ejecutar"],
  "confidence": 0.95
}}
"""