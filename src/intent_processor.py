#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Procesador de Intenciones
=================================================

Este módulo maneja la detección y procesamiento de intenciones del usuario.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import json
import logging
from typing import Optional

try:
    from openai import AsyncOpenAI
except ImportError:
    raise ImportError("openai no está instalado. Ejecute: pip install openai")

from conversation_models import ConversationContext, IntentType, AIResponse, ConversationState
from conversation_prompts import ConversationPrompts
from config import OpenAIConfig


class IntentProcessor:
    """Procesador de intenciones del usuario."""
    
    def __init__(self, config: OpenAIConfig, client: AsyncOpenAI):
        self.config = config
        self.client = client
        self.prompts = ConversationPrompts()
        self.logger = logging.getLogger(__name__)
    
    async def detect_intent(self, text: str, context: ConversationContext) -> AIResponse:
        """Detecta la intención del usuario usando IA."""
        try:
            intent_prompt = self.prompts.get_intent_detection_prompt(text, context.estado)
            
            response = await self.client.chat.completions.create(
                model=self.config.model,
                messages=[{"role": "user", "content": intent_prompt}],
                max_tokens=100,  # Reducido para velocidad
                temperature=0.0,  # Completamente determinístico
                # OPTIMIZACIONES PARA VELOCIDAD
                top_p=0.1,  # Muy determinístico
                frequency_penalty=0,  # Sin penalizaciones
                presence_penalty=0  # Sin penalizaciones
            )
            
            # Parsear respuesta JSON
            response_content = response.choices[0].message.content.strip()
            self.logger.debug(f"Respuesta de OpenAI para detección de intención: {response_content}")
            
            if not response_content:
                raise ValueError("Respuesta vacía de OpenAI")

            # Limpiar respuesta antes de parsear JSON
            response_content = response_content.strip()
            if response_content.startswith('```json'):
                response_content = response_content.replace('```json', '').replace('```', '').strip()

            try:
                result = json.loads(response_content)
            except json.JSONDecodeError as e:
                self.logger.error(f"Error parsing JSON: {e}. Contenido: {response_content[:200]}")
                # Fallback: extraer intent básico
                if 'consulta_deuda' in response_content.lower():
                    result = {'intent': 'consulta_deuda', 'confidence': 0.7}
                else:
                    result = {'intent': 'general', 'confidence': 0.5}
            
            return AIResponse(
                text="",
                intent=IntentType(result['intent']),
                confidence=result['confidence'],
                next_state=context.estado,
                actions=[],
                metadata={'reasoning': result.get('reasoning', '')}
            )
            
        except Exception as e:
            self.logger.error(f"Error detectando intención: {e}")
            # Fallback a intención "otro"
            return AIResponse(
                text="",
                intent=IntentType.OTRO,
                confidence=0.5,
                next_state=context.estado,
                actions=[],
                metadata={'error': str(e)}
            )
    
    async def generate_response(self, call_id: str, context: ConversationContext,
                              intent_result: AIResponse, conversation_history: list) -> AIResponse:
        """Genera respuesta usando el modelo de IA."""
        try:
            # 🚨 VALIDACIÓN CRÍTICA DE SEGURIDAD: No dar información de deuda sin cliente identificado
            if not context.cliente_id:
                # Intents que requieren identificación obligatoria
                intents_protegidos = [
                    IntentType.SOLICITUD_INFORMACION,
                    IntentType.RECONOCIMIENTO_DEUDA,
                    IntentType.NEGACION_DEUDA,
                    IntentType.PROPUESTA_PAGO,
                    IntentType.COMPROMISO_PAGO,
                    IntentType.SOLICITUD_DESCUENTO
                ]

                if intent_result.intent in intents_protegidos:
                    self.logger.warning(f"🚨 INTENT_PROCESSOR SEGURIDAD: Bloqueando intent {intent_result.intent} sin cliente identificado")
                    return AIResponse(
                        text="Para consultar información de deuda necesito verificar su identidad. ¿Podría proporcionarme su número de documento de 8 dígitos?",
                        intent=IntentType.CONFIRMACION_IDENTIDAD,
                        confidence=1.0,
                        next_state=ConversationState.IDENTIFICANDO,
                        actions=[],
                        metadata={'security_block_intent_processor': True}
                    )

            # Construir historial de mensajes para OpenAI
            messages = []
            for msg in conversation_history:
                if msg.role in ['system', 'user', 'assistant']:
                    messages.append({
                        'role': msg.role,
                        'content': msg.content
                    })

            # Agregar contexto de intención detectada CON información del cliente
            context_prompt = self.prompts.get_response_generation_prompt(
                intent_result.intent.value,
                intent_result.confidence,
                context.estado,
                context  # Pasar el contexto completo
            )
            
            messages.append({'role': 'user', 'content': context_prompt})
            
            response = await self.client.chat.completions.create(
                model=self.config.model,
                messages=messages,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                # OPTIMIZACIONES PARA VELOCIDAD
                stream=False,  # Sin streaming para respuestas más rápidas
                top_p=0.1,  # Muy determinístico
                frequency_penalty=0,  # Sin penalizaciones
                presence_penalty=0  # Sin penalizaciones
            )
            
            # Parsear respuesta con manejo robusto
            response_content = response.choices[0].message.content.strip()
            if response_content.startswith('```json'):
                response_content = response_content.replace('```json', '').replace('```', '').strip()

            try:
                result = json.loads(response_content)
            except json.JSONDecodeError as e:
                self.logger.error(f"Error parsing JSON en respuesta: {e}. Contenido: {response_content[:200]}")
                # Fallback: respuesta básica
                result = {
                    'response_text': 'Disculpe, ¿podría repetir lo que me dijo? No pude entender completamente.',
                    'confidence': 0.5
                }
            
            return AIResponse(
                text=result.get('response_text', result.get('text', 'Disculpe, no pude generar una respuesta.')),
                intent=intent_result.intent,
                confidence=result.get('confidence', 0.8),
                next_state=ConversationState(result['next_state']) if result.get('next_state') and result['next_state'] in [state.value for state in ConversationState] else context.estado,
                actions=result.get('actions', []),
                metadata={'model_response': result}
            )
            
        except Exception as e:
            self.logger.error(f"Error generando respuesta: {e}")
            # Respuesta de fallback
            return AIResponse(
                text="Disculpe, ¿podría repetir lo que me dijo? No pude entender completamente.",
                intent=intent_result.intent,
                confidence=0.5,
                next_state=context.estado,
                actions=[],
                metadata={'error': str(e)}
            )