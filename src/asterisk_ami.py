#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Módulo para conexión con Asterisk Manager Interface (AMI).
Permite controlar llamadas, obtener información de canales y ejecutar comandos.
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class AMIConfig:
    """Configuración para conexión AMI."""
    host: str
    port: int
    username: str
    secret: str
    timeout: int = 10


class AsteriskAMI:
    """Cliente para Asterisk Manager Interface."""
    
    def __init__(self, config: AMIConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.reader: Optional[asyncio.StreamReader] = None
        self.writer: Optional[asyncio.StreamWriter] = None
        self.connected = False
        self.authenticated = False
        self.action_id_counter = 0
        
    async def connect(self) -> bool:
        """Conecta al AMI de Asterisk."""
        try:
            self.logger.info(f"Conectando a Asterisk AMI en {self.config.host}:{self.config.port}")
            
            # Establecer conexión TCP
            self.reader, self.writer = await asyncio.wait_for(
                asyncio.open_connection(self.config.host, self.config.port),
                timeout=self.config.timeout
            )
            
            # Leer banner de bienvenida (primera línea directa)
            banner_line = await self.reader.readline()
            banner_text = banner_line.decode().strip()

            self.logger.info(f"Banner AMI recibido: '{banner_text}'")

            if not banner_text or 'Asterisk Call Manager' not in banner_text:
                self.logger.error(f"Banner AMI inválido: '{banner_text}'")
                return False
            
            self.connected = True
            self.logger.info("Conexión AMI establecida")
            
            # Autenticar
            return await self._authenticate()
            
        except Exception as e:
            self.logger.error(f"Error conectando a AMI: {e}")
            return False
    
    async def _authenticate(self) -> bool:
        """Autentica con el servidor AMI."""
        try:
            action_id = self._get_action_id()
            
            # Enviar comando de login
            login_cmd = (
                f"Action: Login\r\n"
                f"Username: {self.config.username}\r\n"
                f"Secret: {self.config.secret}\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )
            
            self.writer.write(login_cmd.encode())
            await self.writer.drain()
            
            # Leer respuesta
            response = await self._read_response()
            
            if response and response.get('response') == 'Success':
                self.authenticated = True
                self.logger.info("Autenticación AMI exitosa")
                return True
            else:
                self.logger.error(f"Error de autenticación AMI: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error en autenticación AMI: {e}")
            return False
    
    async def hangup_channel(self, channel: str) -> bool:
        """Cuelga un canal específico."""
        try:
            if not self.authenticated:
                self.logger.error("No autenticado en AMI")
                return False
            
            action_id = self._get_action_id()
            
            # Comando Hangup
            hangup_cmd = (
                f"Action: Hangup\r\n"
                f"Channel: {channel}\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )
            
            self.logger.info(f"🔌 Enviando comando Hangup para canal: {channel}")
            
            self.writer.write(hangup_cmd.encode())
            await self.writer.drain()
            
            # Leer respuesta
            response = await self._read_response()
            
            if response and response.get('response') == 'Success':
                self.logger.info(f"✅ Canal {channel} colgado exitosamente")
                return True
            else:
                self.logger.error(f"❌ Error colgando canal {channel}: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error ejecutando hangup: {e}")
            return False
    
    async def get_channel_info(self, channel: str) -> Optional[Dict[str, Any]]:
        """Obtiene información de un canal."""
        try:
            if not self.authenticated:
                return None
            
            action_id = self._get_action_id()
            
            # Comando Status
            status_cmd = (
                f"Action: Status\r\n"
                f"Channel: {channel}\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )
            
            self.writer.write(status_cmd.encode())
            await self.writer.drain()
            
            # Leer respuesta
            response = await self._read_response()
            
            if response and response.get('response') == 'Success':
                return response
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error obteniendo info de canal: {e}")
            return None
    
    async def find_channel_by_session(self, session_id: str, caller_number: str = None) -> Optional[str]:
        """Encuentra el canal específico de una sesión AudioSocket."""
        try:
            if not self.authenticated:
                return None

            action_id = self._get_action_id()

            # Comando CoreShowChannels para listar todos los canales
            channels_cmd = (
                f"Action: CoreShowChannels\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )

            self.writer.write(channels_cmd.encode())
            await self.writer.drain()

            channels_found = []
            channel_details = []

            # Leer múltiples respuestas hasta encontrar el canal
            while True:
                response = await self._read_response()
                if not response:
                    break

                # Recopilar todos los canales con detalles
                if 'channel' in response:
                    channel_name = response.get('channel', '')
                    channels_found.append(channel_name)

                    # Guardar detalles del canal
                    channel_info = {
                        'channel': channel_name,
                        'state': response.get('channelstate', ''),
                        'context': response.get('context', ''),
                        'calleridnum': response.get('calleridnum', ''),
                        'connectedlinenum': response.get('connectedlinenum', ''),
                        'uniqueid': response.get('uniqueid', '')
                    }
                    channel_details.append(channel_info)

                    # ESTRATEGIA 1: Buscar canal que contenga el session_id (UUID de Asterisk)
                    # El canal AudioSocket debería contener el UUID en su nombre
                    if session_id in channel_name or session_id.replace('-', '') in channel_name:
                        self.logger.info(f"🎯 Canal encontrado por session_id {session_id}: {channel_name}")
                        return channel_name

                    # También buscar por uniqueid que debería coincidir con el UUID
                    if session_id in channel_info.get('uniqueid', '') or session_id.replace('-', '') in channel_info.get('uniqueid', ''):
                        self.logger.info(f"🎯 Canal encontrado por uniqueid {session_id}: {channel_name}")
                        return channel_name

                # Si llegamos al final de la lista
                if response.get('event') == 'CoreShowChannelsComplete':
                    break

            self.logger.info(f"🔍 Canales activos encontrados: {len(channels_found)}")
            self.logger.info(f"🔍 Buscando session_id: {session_id}")
            for detail in channel_details:
                self.logger.info(f"   📞 {detail['channel']}")
                self.logger.info(f"      Estado: {detail['state']} | Caller: {detail['calleridnum']}")
                self.logger.info(f"      Context: {detail['context']} | UniqueID: {detail['uniqueid']}")
                self.logger.info(f"      Contiene session_id: {session_id in detail['channel'] or session_id in detail['uniqueid']}")

            # ESTRATEGIA 2: Buscar por número de teléfono (MÁS CONFIABLE)
            if caller_number and caller_number != "1234567890":  # Evitar placeholder
                for detail in channel_details:
                    caller_id = detail['calleridnum']
                    connected_id = detail['connectedlinenum']

                    # Buscar coincidencia exacta o parcial
                    if (caller_number == caller_id or
                        caller_number == connected_id or
                        caller_number in caller_id or
                        caller_number in connected_id):
                        self.logger.info(f"🎯 Canal encontrado por número {caller_number}: {detail['channel']}")
                        self.logger.info(f"   Caller ID: {caller_id}, Connected: {connected_id}")
                        return detail['channel']

            # ESTRATEGIA 3: Buscar canales AudioSocket o relacionados
            audiosocket_channels = []
            avr_context_channels = []

            for detail in channel_details:
                channel = detail['channel']
                context = detail['context']
                state = detail['state']

                # Buscar canales AudioSocket directos
                if any(pattern in channel.lower() for pattern in ['audiosocket']):
                    if state in ['6', 'Up']:  # Estado activo
                        audiosocket_channels.append(detail)
                        self.logger.info(f"🎯 Canal AudioSocket encontrado: {channel}")

                # Buscar canales en contexto 'avr' (nuestro contexto)
                if context == 'avr' and state in ['6', 'Up']:
                    avr_context_channels.append(detail)
                    self.logger.info(f"🎯 Canal en contexto AVR: {channel}")

            # Prioridad 1: Canales AudioSocket directos
            if len(audiosocket_channels) == 1:
                channel = audiosocket_channels[0]['channel']
                self.logger.info(f"🎯 Usando único canal AudioSocket: {channel}")
                return channel
            elif len(audiosocket_channels) > 1:
                self.logger.warning(f"⚠️ Múltiples canales AudioSocket ({len(audiosocket_channels)})")
                # Si hay múltiples, intentar filtrar por caller_number
                if caller_number and caller_number != "1234567890":
                    for detail in audiosocket_channels:
                        if caller_number in detail['calleridnum']:
                            self.logger.info(f"🎯 Canal AudioSocket por caller: {detail['channel']}")
                            return detail['channel']
                return None

            # Prioridad 2: Canales en contexto AVR (ACEPTAR COMO VÁLIDO)
            if len(avr_context_channels) == 1:
                channel = avr_context_channels[0]['channel']
                self.logger.info(f"🎯 Usando único canal en contexto AVR: {channel}")
                self.logger.info(f"   Este canal está relacionado con la llamada AudioSocket")
                return channel
            elif len(avr_context_channels) > 1:
                self.logger.warning(f"⚠️ Múltiples canales en contexto AVR ({len(avr_context_channels)})")
                # Si hay múltiples, usar el primero como fallback seguro
                if len(avr_context_channels) <= 2:  # Máximo 2 canales en AVR es normal (entrada + salida)
                    channel = avr_context_channels[0]['channel']
                    self.logger.info(f"🎯 Usando primer canal AVR como fallback: {channel}")
                    return channel
                return None

            # ESTRATEGIA 4: Si solo hay un canal activo total y no hay caller_number
            if len(channels_found) == 1 and not caller_number:
                self.logger.warning(f"⚠️ Usando único canal activo por defecto: {channels_found[0]}")
                self.logger.warning(f"   RIESGO: Podría ser de otro usuario")
                return channels_found[0]

            self.logger.warning(f"❌ No se pudo identificar canal específico para sesión {session_id}")
            self.logger.warning(f"   Canales disponibles: {len(channels_found)}")
            self.logger.warning(f"   Caller number: {caller_number or 'No proporcionado'}")
            return None

        except Exception as e:
            self.logger.error(f"Error buscando canal por sesión: {e}")
            return None

    async def find_channel_by_uuid(self, uuid: str) -> Optional[str]:
        """Método legacy - usa find_channel_by_session."""
        return await self.find_channel_by_session(uuid)

    async def hangup_all_channels(self) -> bool:
        """Cuelga todos los canales activos (para emergencias)."""
        try:
            if not self.authenticated:
                return False

            # Obtener todos los canales
            channels = await self._get_all_channels()

            if not channels:
                self.logger.info("No hay canales activos para colgar")
                return True

            success_count = 0
            for channel in channels:
                if await self.hangup_channel(channel):
                    success_count += 1

            self.logger.info(f"✅ Colgados {success_count}/{len(channels)} canales")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"Error colgando todos los canales: {e}")
            return False

    async def _get_all_channels(self) -> list:
        """Obtiene lista de todos los canales activos."""
        try:
            action_id = self._get_action_id()

            channels_cmd = (
                f"Action: CoreShowChannels\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )

            self.writer.write(channels_cmd.encode())
            await self.writer.drain()

            channels = []
            while True:
                response = await self._read_response()
                if not response:
                    break

                if 'channel' in response:
                    channels.append(response.get('channel'))

                if response.get('event') == 'CoreShowChannelsComplete':
                    break

            return channels

        except Exception as e:
            self.logger.error(f"Error obteniendo canales: {e}")
            return []
    
    async def _read_response(self) -> Optional[Dict[str, str]]:
        """Lee una respuesta del AMI."""
        try:
            response = {}
            
            while True:
                line = await self.reader.readline()
                if not line:
                    break
                
                line = line.decode().strip()
                
                # Línea vacía indica fin de respuesta
                if not line:
                    break
                
                # Parsear línea key: value
                if ':' in line:
                    key, value = line.split(':', 1)
                    response[key.strip().lower()] = value.strip()
            
            return response if response else None
            
        except Exception as e:
            self.logger.error(f"Error leyendo respuesta AMI: {e}")
            return None
    
    def _get_action_id(self) -> str:
        """Genera un ActionID único."""
        self.action_id_counter += 1
        return f"autobot_{int(time.time())}_{self.action_id_counter}"
    
    async def disconnect(self) -> None:
        """Desconecta del AMI."""
        try:
            if self.authenticated:
                # Enviar Logoff
                logoff_cmd = (
                    f"Action: Logoff\r\n"
                    f"ActionID: {self._get_action_id()}\r\n"
                    f"\r\n"
                )
                
                self.writer.write(logoff_cmd.encode())
                await self.writer.drain()
            
            if self.writer:
                self.writer.close()
                await self.writer.wait_closed()
            
            self.connected = False
            self.authenticated = False
            self.logger.info("Desconectado de AMI")
            
        except Exception as e:
            self.logger.error(f"Error desconectando AMI: {e}")
    
    async def __aenter__(self):
        """Context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        await self.disconnect()


# Función de utilidad para crear cliente AMI desde config
def create_ami_client(config) -> AsteriskAMI:
    """Crea un cliente AMI desde la configuración."""
    ami_config = AMIConfig(
        host=config.asterisk.host,
        port=config.asterisk.ami_port,
        username=config.asterisk.ami_user,
        secret=config.asterisk.ami_secret
    )
    return AsteriskAMI(ami_config)


# Función de prueba
async def test_ami_connection():
    """Prueba la conexión AMI."""
    from config import Config
    
    try:
        config = Config()
        ami = create_ami_client(config)
        
        if await ami.connect():
            print("✅ Conexión AMI exitosa")
            
            # Probar comando
            channels = await ami.find_channel_by_uuid("test-uuid")
            print(f"Canales encontrados: {channels}")
            
        else:
            print("❌ Error conectando a AMI")
        
        await ami.disconnect()
        
    except Exception as e:
        print(f"Error en prueba AMI: {e}")


if __name__ == "__main__":
    asyncio.run(test_ami_connection())
