#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Manejador de ASR (Automatic Speech Recognition)
========================================================================

Este módulo implementa el manejador de reconocimiento de voz usando Deepgram
para convertir audio en tiempo real a texto durante las llamadas.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import logging
import json
import time
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass
from datetime import datetime

try:
    from deepgram import Deepgram, DeepgramClient, PrerecordedOptions, LiveTranscriptionEvents
    from deepgram.clients.live.v1 import LiveOptions
except ImportError:
    raise ImportError("deepgram-sdk no está instalado. Ejecute: pip install deepgram-sdk")

from config import DeepgramConfig


@dataclass
class TranscriptionResult:
    """Resultado de transcripción de audio."""
    text: str
    confidence: float
    is_final: bool
    timestamp: datetime
    language: str = 'es'
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now()


class ASRHandler:
    """Manejador de reconocimiento automático de voz usando Deepgram."""
    
    def __init__(self, config: DeepgramConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Cliente Deepgram
        self.client: Optional[DeepgramClient] = None
        self.live_connection = None
        
        # Estado del handler
        self.is_initialized = False
        self.is_listening = False
        
        # Buffer de audio y resultados
        self.audio_buffer = b''
        self.transcription_buffer = []
        self.current_transcript = ""
        
        # Callbacks
        self.on_transcript: Optional[Callable[[TranscriptionResult], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
        self.transcription_callback: Optional[Callable[[str, str, bool], None]] = None
        
        # Keepalive para evitar timeouts durante TTS largos
        self.keepalive_task = None
        self.last_audio_time = time.time()

        # Estadísticas
        self.stats = {
            'total_audio_processed': 0,
            'total_transcriptions': 0,
            'average_confidence': 0.0,
            'errors': 0,
            'connections_opened': 0,
            'connections_closed': 0
        }
    
    async def initialize(self) -> None:
        """Inicializa el cliente Deepgram y la conexión."""
        try:
            if not self.config.validate():
                raise ValueError("Configuración de Deepgram inválida")
            
            # Crear cliente Deepgram
            self.client = DeepgramClient(self.config.api_key)
            
            # 🚀 OPTIMIZACIÓN: Configuración para MÁXIMA VELOCIDAD
            self.live_options = LiveOptions(
                model="nova-2",  # Modelo más rápido de Deepgram
                language=self.config.language,
                encoding=self.config.encoding,
                sample_rate=self.config.sample_rate,
                interim_results=True,  # Resultados inmediatos
                punctuate=False,  # Sin puntuación = más rápido
                profanity_filter=False,  # Sin filtros = más rápido
                redact=False,  # Sin redacción = más rápido
                numerals=True,  # Convertir números (importante para DNI)
                # 🚀 OPTIMIZACIONES ADICIONALES PARA VELOCIDAD
                smart_format=False,  # Sin formato inteligente = más rápido
                diarize=False,  # Sin diarización = más rápido
                filler_words=False,  # Sin palabras de relleno = más rápido
                multichannel=False,  # Sin multicanal = más rápido
                utterance_end_ms=1000,  # Detectar fin de frase más rápido (1s)
                vad_events=True  # Eventos de detección de voz para mejor timing
            )
            
            self.is_initialized = True
            self.logger.info("ASR Handler inicializado con Deepgram")
            
        except Exception as e:
            self.logger.error(f"Error inicializando ASR Handler: {e}")
            raise
    
    def set_transcription_callback(self, callback: Callable[[str, str, bool], None]) -> None:
        """Establece el callback para recibir transcripciones.
        
        Args:
            callback: Función que recibe (call_id, text, is_final)
        """
        self.transcription_callback = callback
    
    async def start_streaming(self) -> None:
        """Inicia el streaming de transcripción en tiempo real."""
        try:
            if not self.is_initialized:
                await self.initialize()

            # Crear conexión de streaming
            self.live_connection = self.client.listen.live.v("1")

            # Configurar event handlers
            self.live_connection.on(LiveTranscriptionEvents.Open, self._on_open)
            self.live_connection.on(LiveTranscriptionEvents.Transcript, self._on_transcript)
            self.live_connection.on(LiveTranscriptionEvents.Error, self._on_error)
            self.live_connection.on(LiveTranscriptionEvents.Close, self._on_close)

            # Iniciar conexión con manejo de errores mejorado
            try:
                self.live_connection.start(self.live_options)
                self.logger.info("Conexión Deepgram iniciada exitosamente")
            except Exception as conn_error:
                self.logger.error(f"Error conectando a Deepgram: {conn_error}")
                # Marcar como fallback mode
                self.is_listening = False
                self.logger.warning("ASR funcionando en modo fallback (sin Deepgram)")
                return

            self.is_listening = True
            self.logger.info("Streaming ASR iniciado")

            # Iniciar keepalive para evitar timeouts durante TTS largos
            await self._start_keepalive()

        except Exception as e:
            self.logger.error(f"Error iniciando streaming ASR: {e}")
            self.stats['errors'] += 1
            # No hacer raise para permitir funcionamiento sin ASR
            self.logger.warning("ASR no disponible - sistema funcionará sin transcripción")
    
    async def stop_streaming(self) -> None:
        """Detiene el streaming de transcripción."""
        try:
            # Detener keepalive
            await self._stop_keepalive()

            if self.live_connection and self.is_listening:
                self.live_connection.finish()
                self.is_listening = False
                self.logger.info("Streaming ASR detenido")

        except Exception as e:
            self.logger.error(f"Error deteniendo streaming ASR: {e}")

    async def _start_keepalive(self) -> None:
        """Inicia el sistema de keepalive para evitar timeouts."""
        try:
            if self.keepalive_task:
                await self._stop_keepalive()

            self.keepalive_task = asyncio.create_task(self._keepalive_loop())
            self.logger.debug("Keepalive iniciado para Deepgram")

        except Exception as e:
            self.logger.error(f"Error iniciando keepalive: {e}")

    async def _stop_keepalive(self) -> None:
        """Detiene el sistema de keepalive."""
        try:
            if self.keepalive_task:
                self.keepalive_task.cancel()
                try:
                    await self.keepalive_task
                except asyncio.CancelledError:
                    pass
                self.keepalive_task = None
                self.logger.debug("Keepalive detenido")

        except Exception as e:
            self.logger.error(f"Error deteniendo keepalive: {e}")

    async def _keepalive_loop(self) -> None:
        """Loop de keepalive que envía datos periódicos a Deepgram."""
        try:
            while self.is_listening:
                current_time = time.time()

                # Si han pasado más de 2 segundos sin audio, enviar keepalive
                if current_time - self.last_audio_time > 2.0:
                    if self.live_connection:
                        # Enviar silencio como keepalive (160 bytes = 20ms de silencio a 8kHz)
                        silence = b'\x00' * 160
                        self.live_connection.send(silence)
                        self.logger.debug("Keepalive enviado a Deepgram")

                    self.last_audio_time = current_time

                # Esperar 1 segundo antes del próximo check (más responsivo)
                await asyncio.sleep(1.0)

        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"Error en keepalive loop: {e}")
    
    async def process_audio(self, audio_data: bytes) -> Optional[str]:
        """Procesa un chunk de audio y retorna transcripción si está disponible."""
        try:
            # 🚀 OPTIMIZACIÓN: No re-inicializar si ya está escuchando
            if not self.is_listening:
                self.logger.warning("ASR no está escuchando, intentando re-inicializar...")
                await self.start_streaming()

            # Si no hay conexión activa, simular transcripción para testing
            if not self.live_connection or not self.is_listening:
                # Modo fallback - simular transcripción cada cierto tiempo
                current_time = time.time()
                if not hasattr(self, '_last_fallback_time'):
                    self._last_fallback_time = current_time

                # Simular transcripción cada 3 segundos
                if current_time - self._last_fallback_time > 3.0:
                    self._last_fallback_time = current_time
                    self.logger.warning("ASR FALLBACK: Simulando transcripción para testing")
                    return "texto simulado para testing"

                return None

            # Enviar audio al streaming (más eficiente)
            if self.live_connection and self.is_listening:
                self.live_connection.send(audio_data)
                self.stats['total_audio_processed'] += len(audio_data)

                # Actualizar tiempo del último audio para keepalive
                self.last_audio_time = time.time()

            # Retornar transcripción acumulada si está disponible
            if self.current_transcript:
                transcript = self.current_transcript
                self.current_transcript = ""  # Limpiar para próxima transcripción
                return transcript

            return None

        except Exception as e:
            self.logger.error(f"Error procesando audio: {e}")
            self.stats['errors'] += 1
            return None
    
    async def process_audio_file(self, audio_data: bytes) -> Optional[TranscriptionResult]:
        """Procesa un archivo de audio completo (no streaming)."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Configurar opciones para audio pregrabado
            options = PrerecordedOptions(
                model=self.config.model,
                language=self.config.language,
                punctuate=self.config.punctuate,
                profanity_filter=self.config.profanity_filter,
                redact=self.config.redact
            )
            
            # Procesar audio
            response = await self.client.listen.prerecorded.v("1").transcribe_file(
                {"buffer": audio_data},
                options
            )
            
            # Extraer resultado
            if response.results and response.results.channels:
                channel = response.results.channels[0]
                if channel.alternatives:
                    alternative = channel.alternatives[0]
                    
                    result = TranscriptionResult(
                        text=alternative.transcript,
                        confidence=alternative.confidence,
                        is_final=True,
                        timestamp=datetime.now(),
                        language=self.config.language
                    )
                    
                    self.stats['total_transcriptions'] += 1
                    self._update_confidence_stats(result.confidence)
                    
                    return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error procesando archivo de audio: {e}")
            self.stats['errors'] += 1
            return None
    
    def _on_open(self, client, open_event, **kwargs):
        """Callback cuando se abre la conexión."""
        self.logger.info("Conexión de streaming ASR abierta")
        self.stats['connections_opened'] += 1
    
    def _on_transcript(self, client, result, **kwargs):
        """Callback cuando se recibe una transcripción."""
        try:
            # Parsear evento de transcripción
            if hasattr(result, 'channel'):
                channel = result.channel
                if hasattr(channel, 'alternatives') and channel.alternatives:
                    alternative = channel.alternatives[0]
                    
                    transcription_result = TranscriptionResult(
                        text=alternative.transcript,
                        confidence=alternative.confidence,
                        is_final=result.is_final,
                        timestamp=datetime.now(),
                        language=self.config.language
                    )
                    
                    # Procesar resultado
                    self._process_transcription_result(transcription_result)
                    
                    # Llamar callback si está configurado
                    if self.on_transcript:
                        self.on_transcript(transcription_result)
            
        except Exception as e:
            self.logger.error(f"Error procesando transcripción: {e}")
            self.stats['errors'] += 1
    
    def _on_error(self, client, error_event, **kwargs):
        """Callback cuando ocurre un error."""
        error_msg = f"Error en streaming ASR: {error_event}"
        self.logger.error(error_msg)
        self.stats['errors'] += 1
        
        if self.on_error:
            self.on_error(error_msg)
    
    def _on_close(self, client, close_event, **kwargs):
        """Callback cuando se cierra la conexión."""
        self.logger.info("Conexión de streaming ASR cerrada")
        self.stats['connections_closed'] += 1
        self.is_listening = False
    
    def _process_transcription_result(self, result: TranscriptionResult) -> None:
        """Procesa un resultado de transcripción."""
        try:
            if result.is_final and result.text.strip():
                # Transcripción final - actualizar buffer
                self.current_transcript = result.text.strip()
                self.stats['total_transcriptions'] += 1
                self._update_confidence_stats(result.confidence)
                
                self.logger.info(f"Transcripción final: {result.text} (confianza: {result.confidence:.2f})")
            
            elif self.config.interim_results and result.text.strip():
                # Resultado intermedio - solo log
                self.logger.debug(f"Transcripción intermedia: {result.text}")
            
        except Exception as e:
            self.logger.error(f"Error procesando resultado de transcripción: {e}")
    
    def _update_confidence_stats(self, confidence: float) -> None:
        """Actualiza estadísticas de confianza."""
        try:
            current_avg = self.stats['average_confidence']
            total_transcriptions = self.stats['total_transcriptions']
            
            if total_transcriptions == 1:
                self.stats['average_confidence'] = confidence
            else:
                # Calcular promedio móvil
                self.stats['average_confidence'] = (
                    (current_avg * (total_transcriptions - 1) + confidence) / total_transcriptions
                )
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas: {e}")
    
    def set_transcript_callback(self, callback: Callable[[TranscriptionResult], None]) -> None:
        """Configura callback para recibir transcripciones."""
        self.on_transcript = callback
    
    def set_error_callback(self, callback: Callable[[str], None]) -> None:
        """Configura callback para recibir errores."""
        self.on_error = callback
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estadísticas del handler."""
        return {
            **self.stats,
            'is_initialized': self.is_initialized,
            'is_listening': self.is_listening,
            'buffer_size': len(self.audio_buffer)
        }
    
    async def cleanup(self) -> None:
        """Limpia recursos del handler."""
        try:
            await self.stop_streaming()
            
            if self.live_connection:
                self.live_connection = None
            
            self.client = None
            self.is_initialized = False
            
            self.logger.info("ASR Handler limpiado")
            
        except Exception as e:
            self.logger.error(f"Error limpiando ASR Handler: {e}")


# Función de utilidad para testing
async def test_asr_handler():
    """Función de prueba para el ASR Handler."""
    from config import Config
    
    config = Config()
    handler = ASRHandler(config.deepgram)
    
    try:
        await handler.initialize()
        print("ASR Handler inicializado correctamente")
        
        # Simular procesamiento de audio
        # En una implementación real, aquí se enviaría audio real
        print("Handler listo para procesar audio")
        
    except Exception as e:
        print(f"Error en prueba: {e}")
    finally:
        await handler.cleanup()


if __name__ == "__main__":
    asyncio.run(test_asr_handler())