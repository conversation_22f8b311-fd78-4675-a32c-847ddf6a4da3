#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Modelos de Conversación
===============================================

Este módulo define los modelos de datos para el sistema de conversación.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class ConversationState(Enum):
    """Estados de la conversación."""
    INICIANDO = "iniciando"
    IDENTIFICANDO = "identificando"
    VERIFICANDO = "verificando"
    NEGOCIANDO = "negociando"
    COMPROMETIENDO = "comprometiendo"
    FINALIZANDO = "finalizando"
    TERMINADA = "terminada"
    ERROR = "error"


class IntentType(Enum):
    """Tipos de intención detectados."""
    SALUDO = "saludo"
    CONFIRMACION_IDENTIDAD = "confirmacion_identidad"
    NEGACION_IDENTIDAD = "negacion_identidad"
    RECONOCIMIENTO_DEUDA = "reconocimiento_deuda"
    NEGACION_DEUDA = "negacion_deuda"
    SOLICITUD_INFORMACION = "solicitud_informacion"
    PROPUESTA_PAGO = "propuesta_pago"
    COMPROMISO_PAGO = "compromiso_pago"
    SOLICITUD_DESCUENTO = "solicitud_descuento"
    DESPEDIDA = "despedida"
    COLGAR = "colgar"
    OTRO = "otro"


@dataclass
class ConversationContext:
    """Contexto de la conversación."""
    call_id: Optional[str] = None
    cliente_id: Optional[int] = None
    telefono: Optional[str] = None
    nombre_cliente: Optional[str] = None
    deuda_total: Optional[float] = None
    deudas_activas: List[Dict] = None
    estado: ConversationState = ConversationState.INICIANDO
    intentos_identificacion: int = 0
    compromisos_previos: List[Dict] = None
    notas_conversacion: List[str] = None
    compromiso_info: Optional[Dict] = None  # Información del compromiso actual
    inicio_conversacion: datetime = None

    # 🎉 Nuevos atributos para manejo de finalización de llamada
    should_end_call: bool = False  # Indica si la llamada debe terminar
    commitment_data: Optional[Dict] = None  # Datos del compromiso exitoso
    
    def __post_init__(self):
        if self.deudas_activas is None:
            self.deudas_activas = []
        if self.compromisos_previos is None:
            self.compromisos_previos = []
        if self.notas_conversacion is None:
            self.notas_conversacion = []
        if self.inicio_conversacion is None:
            self.inicio_conversacion = datetime.now()
    
    def add_note(self, intent: IntentType, reasoning: str = "") -> None:
        """Agrega una nota al contexto de conversación."""
        timestamp = datetime.now().strftime('%H:%M:%S')
        nota = f"[{timestamp}] {intent.value}: {reasoning}"
        self.notas_conversacion.append(nota)


@dataclass
class ConversationMessage:
    """Mensaje de conversación."""
    role: str  # 'system', 'user', 'assistant'
    content: str
    timestamp: datetime
    intent: Optional[IntentType] = None
    confidence: Optional[float] = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now()


@dataclass
class AIResponse:
    """Respuesta del motor de IA."""
    text: str
    intent: IntentType
    confidence: float
    next_state: ConversationState
    actions: List[str]
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        if not self.actions:
            self.actions = []
        if not self.metadata:
            self.metadata = {}