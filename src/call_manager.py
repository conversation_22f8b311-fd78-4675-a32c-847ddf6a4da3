#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Módulo para gestión de llamadas: despedida, corte y logging final.
Maneja el ciclo completo de finalización de llamadas con AMI.
"""

import asyncio
import logging
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

try:
    from .asterisk_ami import create_ami_client
    from .ai_conversation import ConversationState
except ImportError:
    # Fallback para importación absoluta
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from asterisk_ami import create_ami_client
    from ai_conversation import ConversationState


class CallEndReason(Enum):
    """Razones de finalización de llamada."""
    COMPROMISO_OBTENIDO = "compromiso_obtenido"
    DESPEDIDA_NORMAL = "despedida_normal"
    CLIENTE_COLGO = "cliente_colgo"
    ERROR_TECNICO = "error_tecnico"
    TIMEOUT = "timeout"
    INTERRUMPIDO = "interrumpido"


class CallManager:
    """Gestor de llamadas para despedida y corte automático."""
    
    def __init__(self, config, db_manager, logger=None):
        self.config = config
        self.db_manager = db_manager
        self.logger = logger or logging.getLogger(__name__)
        
        # Frases de despedida según el contexto
        self.farewell_phrases = {
            CallEndReason.COMPROMISO_OBTENIDO: [
                "Perfecto, {nombre}. Confirmo su compromiso de pago por {monto} soles para el {fecha}. Que tenga buen día.",
                "Excelente, {nombre}. Queda registrado su compromiso de {monto} soles el {fecha}. Hasta luego.",
                "Gracias, {nombre}. Su compromiso de pago por {monto} soles está confirmado para el {fecha}. Adiós."
            ],
            CallEndReason.DESPEDIDA_NORMAL: [
                "Gracias por su tiempo, {nombre}. Que tenga buen día.",
                "Hasta luego, {nombre}. Gracias por atendernos.",
                "Que tenga buen día, {nombre}. Adiós."
            ],
            CallEndReason.ERROR_TECNICO: [
                "Disculpe las molestias técnicas, {nombre}. Lo contactaremos nuevamente. Adiós.",
                "Lamentamos los inconvenientes, {nombre}. Hasta luego."
            ]
        }
    
    async def handle_commitment_success(self, session, commitment_data: Dict[str, Any]) -> bool:
        """Maneja el éxito de un compromiso: despedida y corte de llamada."""
        try:
            self.logger.info(f"🎉 COMPROMISO EXITOSO (Sesión {session.session_id}): Iniciando despedida y corte")
            
            # Obtener información del cliente
            client_name = getattr(session, 'client_name', 'cliente')
            
            # Generar mensaje de despedida personalizado
            farewell_message = self._generate_farewell_message(
                CallEndReason.COMPROMISO_OBTENIDO,
                client_name=client_name,
                commitment_data=commitment_data
            )
            
            # Enviar despedida por TTS
            await self._send_farewell_message(session, farewell_message)
            
            # Registrar log de llamada exitosa
            await self._log_successful_call(session, commitment_data)
            
            # Esperar un momento para que termine el audio
            await asyncio.sleep(2.0)
            
            # Cortar la llamada via AMI
            await self._hangup_call(session)
            
            self.logger.info(f"✅ LLAMADA FINALIZADA EXITOSAMENTE (Sesión {session.session_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error manejando compromiso exitoso: {e}")
            return False
    
    async def handle_conversation_end(self, session, reason: CallEndReason = CallEndReason.DESPEDIDA_NORMAL) -> bool:
        """Maneja el final de conversación con despedida apropiada."""
        try:
            self.logger.info(f"👋 FINALIZANDO CONVERSACIÓN (Sesión {session.session_id}): Razón={reason.value}")
            
            # Obtener información del cliente
            client_name = getattr(session, 'client_name', 'cliente')
            
            # Generar mensaje de despedida
            farewell_message = self._generate_farewell_message(reason, client_name=client_name)
            
            # Enviar despedida
            await self._send_farewell_message(session, farewell_message)
            
            # Registrar log de llamada
            await self._log_call_end(session, reason)
            
            # Esperar un momento y cortar
            await asyncio.sleep(1.5)
            await self._hangup_call(session)
            
            self.logger.info(f"✅ CONVERSACIÓN FINALIZADA (Sesión {session.session_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error finalizando conversación: {e}")
            return False
    
    def _generate_farewell_message(self, reason: CallEndReason, client_name: str = "cliente", 
                                 commitment_data: Optional[Dict] = None) -> str:
        """Genera mensaje de despedida personalizado."""
        try:
            phrases = self.farewell_phrases.get(reason, self.farewell_phrases[CallEndReason.DESPEDIDA_NORMAL])
            
            # Seleccionar primera frase (se puede hacer aleatorio después)
            template = phrases[0]
            
            # Reemplazar placeholders
            message = template.format(nombre=client_name)
            
            # Si hay datos de compromiso, agregar detalles
            if commitment_data and reason == CallEndReason.COMPROMISO_OBTENIDO:
                monto = commitment_data.get('monto_acordado', 'el monto acordado')
                fecha = commitment_data.get('fecha_compromiso', 'la fecha acordada')

                # Formatear fecha si es string ISO
                if isinstance(fecha, str) and '-' in fecha:
                    try:
                        fecha_obj = datetime.fromisoformat(fecha)
                        fecha = fecha_obj.strftime('%d de %B')
                    except:
                        pass

                # Verificar que el template tenga los placeholders correctos
                if '{monto}' in template and '{fecha}' in template:
                    message = template.format(
                        nombre=client_name,
                        monto=monto,
                        fecha=fecha
                    )
                else:
                    # Fallback si el template no tiene los placeholders
                    message = f"Perfecto, {client_name}. Confirmo su compromiso de pago por {monto} soles para el {fecha}. Que tenga buen día."
            
            return message
            
        except Exception as e:
            self.logger.error(f"Error generando mensaje de despedida: {e}")
            return f"Gracias por su tiempo, {client_name}. Que tenga buen día."
    
    async def _send_farewell_message(self, session, message: str) -> None:
        """Envía mensaje de despedida por TTS."""
        try:
            self.logger.info(f"📢 DESPEDIDA (Sesión {session.session_id}): '{message}'")
            
            # Generar audio de despedida
            if hasattr(session, 'tts_handler') and session.tts_handler:
                audio_data = await session.tts_handler.text_to_speech(message)
                
                # Enviar audio (necesitamos el writer, lo obtenemos del contexto)
                # Por ahora, solo loggeamos - se implementará el envío en la integración
                self.logger.info(f"🔊 Audio de despedida generado: {len(audio_data)} bytes")
            else:
                self.logger.warning("No hay TTS handler disponible para despedida")
                
        except Exception as e:
            self.logger.error(f"Error enviando mensaje de despedida: {e}")
    
    async def _hangup_call(self, session) -> bool:
        """Cuelga la llamada usando AMI de Asterisk."""
        try:
            self.logger.info(f"🔌 CORTANDO LLAMADA (Sesión {session.session_id})")

            # Verificar si AMI está disponible
            if not hasattr(self.config, 'asterisk') or not self.config.asterisk.validate():
                self.logger.warning("⚠️ Configuración AMI no válida - Simulando corte de llamada")
                await asyncio.sleep(1)  # Simular tiempo de corte
                self.logger.info(f"✅ Llamada simulada como cortada: {session.session_id}")
                return True

            # Crear cliente AMI
            ami = create_ami_client(self.config)

            # Conectar y buscar canal específico
            if await ami.connect():
                self.logger.info("✅ Conectado a Asterisk AMI")

                # Obtener información de la sesión
                caller_number = getattr(session, 'caller_number', None)

                # Buscar canal específico de esta sesión
                channel = await ami.find_channel_by_session(session.session_id, caller_number)

                if channel:
                    # Colgar canal específico
                    success = await ami.hangup_channel(channel)
                    await ami.disconnect()

                    if success:
                        self.logger.info(f"✅ Llamada cortada exitosamente: {channel}")
                        return True
                    else:
                        self.logger.error(f"❌ Error cortando llamada específica: {channel}")
                        return False
                else:
                    self.logger.warning(f"⚠️ No se encontró canal específico para sesión {session.session_id}")
                    self.logger.warning(f"   Caller: {caller_number}")
                    self.logger.warning(f"   🛡️ SEGURIDAD: No se ejecutará hangup masivo para evitar cortar otras llamadas")
                    await ami.disconnect()
                    return False
            else:
                self.logger.error("❌ Error conectando a AMI - Simulando corte")
                await asyncio.sleep(1)  # Simular tiempo de corte
                self.logger.info(f"✅ Llamada simulada como cortada: {session.session_id}")
                return True

        except Exception as e:
            self.logger.error(f"Error cortando llamada: {e}")
            # En caso de error, simular corte exitoso para no bloquear el flujo
            self.logger.info(f"✅ Llamada simulada como cortada por error: {session.session_id}")
            return True
    
    async def _log_successful_call(self, session, commitment_data: Dict[str, Any]) -> None:
        """Registra llamada exitosa con compromiso en la BD."""
        try:
            duration = int((datetime.now() - session.start_time).total_seconds())
            
            # Preparar notas detalladas
            notes = f"Compromiso exitoso: {commitment_data.get('monto_acordado')} soles para {commitment_data.get('fecha_compromiso')}. "
            notes += f"Compromiso ID: {commitment_data.get('compromiso_id')}. "
            notes += f"Sesión: {session.session_id}"
            
            await self.db_manager.log_call(
                telefono=session.caller_number,
                duracion=duration,
                estado='compromiso_obtenido',
                notas=notes
            )
            
            self.logger.info(f"📝 Log de llamada exitosa guardado: {duration}s")
            
        except Exception as e:
            self.logger.error(f"Error guardando log de llamada exitosa: {e}")
    
    async def _log_call_end(self, session, reason: CallEndReason) -> None:
        """Registra finalización de llamada en la BD."""
        try:
            duration = int((datetime.now() - session.start_time).total_seconds())
            
            # Mapear razón a estado de BD
            estado_map = {
                CallEndReason.COMPROMISO_OBTENIDO: 'compromiso_obtenido',
                CallEndReason.DESPEDIDA_NORMAL: 'llamada_cortada',
                CallEndReason.CLIENTE_COLGO: 'cliente_colgo',
                CallEndReason.ERROR_TECNICO: 'error_tecnico',
                CallEndReason.TIMEOUT: 'timeout',
                CallEndReason.INTERRUMPIDO: 'interrumpido'
            }
            
            estado = estado_map.get(reason, 'llamada_cortada')
            notes = f"Finalizada por: {reason.value}. Sesión: {session.session_id}"
            
            await self.db_manager.log_call(
                telefono=session.caller_number,
                duracion=duration,
                estado=estado,
                notas=notes
            )
            
            self.logger.info(f"📝 Log de finalización guardado: {estado} ({duration}s)")
            
        except Exception as e:
            self.logger.error(f"Error guardando log de finalización: {e}")
    
    def should_end_conversation(self, ai_response_text: str) -> bool:
        """Determina si la conversación debe terminar basado en la respuesta de la IA."""
        farewell_indicators = [
            'que tenga buen día',
            'hasta luego',
            'adiós',
            'gracias por su tiempo',
            'nos comunicaremos',
            'lo contactaremos',
            'que esté bien',
            'hasta la vista',
            'nos vemos',
            'que tenga buena',
            'hasta pronto',
            'nos despedimos',
            'fin de la llamada',
            'terminar la llamada',
            'cortar la llamada',
            'finalizar',
            'despedida'
        ]

        text_lower = ai_response_text.lower()

        # Log para debugging
        self.logger.info(f"🔍 VERIFICANDO DESPEDIDA: '{ai_response_text}'")
        for indicator in farewell_indicators:
            if indicator in text_lower:
                self.logger.info(f"✅ DESPEDIDA DETECTADA: '{indicator}' en '{ai_response_text}'")
                return True

        self.logger.info(f"❌ NO ES DESPEDIDA: '{ai_response_text}'")
        return False


# Función de utilidad para integración fácil
async def handle_commitment_and_hangup(session, commitment_data: Dict[str, Any], 
                                     config, db_manager, logger) -> bool:
    """Función de utilidad para manejar compromiso y corte de llamada."""
    call_manager = CallManager(config, db_manager, logger)
    return await call_manager.handle_commitment_success(session, commitment_data)
