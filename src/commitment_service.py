#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Commitment Service
==================

Servicio para gestionar compromisos de pago en la base de datos.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import logging
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from decimal import Decimal
from datetime import date, datetime


@dataclass
class CompromisoInfo:
    """Información de un compromiso de pago."""
    id: int
    deuda_id: int
    fecha_compromiso: date
    monto_acordado: Decimal
    estado: str
    tipo_compromiso: str
    numero_cuotas: int
    observaciones: Optional[str]


class CommitmentService:
    """Servicio para gestionar compromisos de pago."""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    async def crear_compromiso_pago(self, deuda_id: int, monto_acordado: float,
                                   fecha_compromiso, tipo_compromiso: str = "pago_total",
                                   observaciones: str = "") -> Optional[int]:
        """Crea un nuevo compromiso de pago."""
        try:
            self.logger.info(f"🔄 CREANDO COMPROMISO: deuda_id={deuda_id}, monto={monto_acordado}, fecha={fecha_compromiso}")

            # Validar que la deuda existe
            deuda_query = "SELECT id, monto, estado FROM deudas WHERE id = $1"
            deuda = await self.db_manager.fetch_one(deuda_query, (deuda_id,))

            if not deuda:
                self.logger.error(f"❌ Deuda {deuda_id} no encontrada")
                return None

            self.logger.info(f"✅ Deuda validada: ID={deuda['id']}, Monto={deuda['monto']}, Estado={deuda['estado']}")

            # Insertar compromiso
            insert_query = """
                INSERT INTO compromisos_pago
                (deuda_id, fecha_compromiso, monto_acordado, tipo_compromiso, observaciones)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id
            """

            self.logger.info(f"📝 Ejecutando INSERT compromiso...")
            result = await self.db_manager.fetch_one(
                insert_query,
                (deuda_id, fecha_compromiso, monto_acordado, tipo_compromiso, observaciones)
            )

            if result:
                compromiso_id = result['id']
                self.logger.info(f"✅ Compromiso insertado: ID {compromiso_id}")

                # Actualizar estado de la deuda usando conexión directa
                try:
                    async with self.db_manager.get_connection() as conn:
                        update_query = "UPDATE deudas SET estado = 'en_compromiso' WHERE id = $1"
                        await conn.execute(update_query, deuda_id)
                        self.logger.info(f"✅ Estado de deuda actualizado a 'en_compromiso'")
                except Exception as e:
                    self.logger.error(f"⚠️ Error actualizando estado de deuda: {e}")
                    # No fallar el compromiso por esto, ya se guardó exitosamente

                return compromiso_id
            else:
                self.logger.error("❌ Error insertando compromiso - fetch_one retornó None")
                return None

        except Exception as e:
            self.logger.error(f"💥 Error creando compromiso: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    async def obtener_compromisos_cliente(self, cliente_id: int) -> List[CompromisoInfo]:
        """Obtiene compromisos de un cliente."""
        try:
            query = """
                SELECT cp.id, cp.deuda_id, cp.fecha_compromiso, cp.monto_acordado,
                       cp.estado, cp.tipo_compromiso, cp.numero_cuotas, cp.observaciones
                FROM compromisos_pago cp
                JOIN deudas d ON cp.deuda_id = d.id
                WHERE d.cliente_id = $1
                ORDER BY cp.fecha_compromiso DESC
            """
            
            results = await self.db_manager.fetch_all(query, (cliente_id,))
            
            compromisos = []
            for row in results:
                compromisos.append(CompromisoInfo(
                    id=row['id'],
                    deuda_id=row['deuda_id'],
                    fecha_compromiso=row['fecha_compromiso'],
                    monto_acordado=row['monto_acordado'],
                    estado=row['estado'],
                    tipo_compromiso=row['tipo_compromiso'],
                    numero_cuotas=row['numero_cuotas'],
                    observaciones=row['observaciones']
                ))
            
            return compromisos
            
        except Exception as e:
            self.logger.error(f"Error obteniendo compromisos del cliente {cliente_id}: {e}")
            return []
    
    async def procesar_compromiso_desde_conversacion(self, cliente_id: int, texto_compromiso: str) -> Dict[str, Any]:
        """Procesa un compromiso extraído de la conversación."""
        try:
            self.logger.info(f"🔄 PROCESANDO COMPROMISO - Cliente {cliente_id}: '{texto_compromiso}'")

            # Extraer información del compromiso del texto
            compromiso_info = self._extraer_info_compromiso(texto_compromiso)

            if not compromiso_info:
                self.logger.error(f"❌ No se pudo extraer información del compromiso: '{texto_compromiso}'")
                return {'success': False, 'error': 'No se pudo extraer información del compromiso'}

            self.logger.info(f"✅ Información extraída: {compromiso_info}")

            # Obtener deudas del cliente
            deudas_query = """
                SELECT id, monto, concepto FROM deudas
                WHERE cliente_id = $1 AND estado IN ('pendiente', 'vencida', 'en_cobranza')
                ORDER BY dias_vencido DESC, monto DESC
                LIMIT 1
            """

            deuda = await self.db_manager.fetch_one(deudas_query, (cliente_id,))

            if not deuda:
                self.logger.error(f"❌ No se encontraron deudas pendientes para cliente {cliente_id}")
                return {'success': False, 'error': 'No se encontraron deudas pendientes'}

            self.logger.info(f"💰 Deuda encontrada: ID={deuda['id']}, Monto={deuda['monto']}, Concepto={deuda['concepto']}")

            # Si no se extrajo monto específico, usar monto total de TODAS las deudas del cliente
            monto_final = compromiso_info['monto']
            if monto_final <= 0.0:  # No se encontró monto específico
                # Calcular deuda total del cliente (como lo hace debt_service)
                total_query = """
                    SELECT COALESCE(SUM(monto), 0) as deuda_total
                    FROM deudas
                    WHERE cliente_id = $1 AND estado IN ('pendiente', 'vencida', 'en_cobranza')
                """
                total_result = await self.db_manager.fetch_one(total_query, (cliente_id,))
                monto_final = float(total_result['deuda_total']) if total_result else float(deuda['monto'])
                self.logger.info(f"💡 No se encontró monto específico, usando deuda total del cliente: {monto_final}")
            else:
                self.logger.info(f"💰 Usando monto específico del cliente: {monto_final}")

            # Convertir fecha string a objeto date
            from datetime import datetime
            if isinstance(compromiso_info['fecha'], str):
                fecha_obj = datetime.fromisoformat(compromiso_info['fecha']).date()
            else:
                fecha_obj = compromiso_info['fecha']

            # Crear compromiso
            self.logger.info(f"💾 Creando compromiso en BD...")
            compromiso_id = await self.crear_compromiso_pago(
                deuda_id=deuda['id'],
                monto_acordado=monto_final,
                fecha_compromiso=fecha_obj,
                tipo_compromiso=compromiso_info['tipo'],
                observaciones=f"Compromiso obtenido por IA: {texto_compromiso}"
            )

            if compromiso_id:
                self.logger.info(f"🎉 COMPROMISO CREADO EXITOSAMENTE: ID={compromiso_id}")
                return {
                    'success': True,
                    'compromiso_id': compromiso_id,
                    'deuda_id': deuda['id'],
                    'monto_acordado': monto_final,
                    'fecha_compromiso': fecha_obj.isoformat()
                }
            else:
                self.logger.error(f"❌ Error creando compromiso en BD")
                return {'success': False, 'error': 'Error creando compromiso en BD'}

        except Exception as e:
            self.logger.error(f"💥 Error procesando compromiso: {e}")
            return {'success': False, 'error': str(e)}
    
    def _extraer_info_compromiso(self, texto: str) -> Optional[Dict[str, Any]]:
        """Extrae información de compromiso del texto usando calculador optimizado."""
        import re
        try:
            from .date_calculator import DateCalculator
        except ImportError:
            # Fallback para importación absoluta
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from date_calculator import DateCalculator

        try:
            self.logger.info(f"EXTRAYENDO COMPROMISO DE: '{texto}'")

            # Buscar montos con patrones más específicos
            # Buscar números seguidos de "soles", "pesos", etc.
            monto_patterns = [
                r'(\d+(?:\.\d{2})?)\s*(?:soles?|pesos?|s/\.?)',  # "500 soles", "1000.50 s/"
                r'(\d+(?:\.\d{2})?)\s*(?:nuevos soles|pen)',     # "500 nuevos soles"
                r's/\.?\s*(\d+(?:\.\d{2})?)',                    # "S/ 500", "s/500"
                r'\b(\d+(?:\.\d{2})?)\b'                         # Cualquier número
            ]

            monto = None
            for pattern in monto_patterns:
                matches = re.findall(pattern, texto.lower())
                if matches:
                    try:
                        monto = float(matches[0])
                        self.logger.info(f"💰 Monto extraído con patrón '{pattern}': {monto}")
                        break
                    except ValueError:
                        continue

            # Usar calculador optimizado de fechas
            date_calculator = DateCalculator()
            fecha_compromiso = date_calculator.parse_natural_date(texto)

            if not fecha_compromiso:
                # Fallback: mañana por defecto
                from datetime import datetime, timedelta
                fecha_compromiso = datetime.now().date() + timedelta(days=1)
                self.logger.warning(f"No se pudo parsear fecha de '{texto}', usando mañana por defecto")

            self.logger.info(f"📅 FECHA CALCULADA: {fecha_compromiso}")

            # Determinar tipo de compromiso
            tipo = "pago_total"
            if 'parcial' in texto.lower() or 'parte' in texto.lower() or 'mitad' in texto.lower():
                tipo = "pago_parcial"

            # Si no se encontró monto, usar valor por defecto que será reemplazado por monto de deuda
            if monto is None:
                monto = 0.0  # Será reemplazado por monto de deuda
                self.logger.info(f"💡 No se encontró monto específico en el texto")

            self.logger.info(f"✅ COMPROMISO EXTRAÍDO: Monto={monto}, Fecha={fecha_compromiso}, Tipo={tipo}")

            return {
                'monto': monto,
                'fecha': fecha_compromiso.isoformat(),
                'tipo': tipo
            }

        except Exception as e:
            self.logger.error(f"Error extrayendo info de compromiso: {e}")
            return None
