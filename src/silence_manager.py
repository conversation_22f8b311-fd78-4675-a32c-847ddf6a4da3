#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Silence Manager
===============

Maneja el silencio continuo y keepalive para mantener las llamadas activas.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import struct
from datetime import datetime
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from call_session import CallSession


class SilenceManager:
    """Maneja el silencio continuo y keepalive."""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
    
    async def send_continuous_silence(self, writer: asyncio.StreamWriter, session: 'CallSession') -> None:
        """Envía silencio continuo para mantener la llamada activa indefinidamente."""
        try:
            silence_chunk = b'\x00' * 320  # 320 bytes de silencio

            # Enviar silencio continuo hasta que se cancele la tarea
            silence_count = 0
            self.logger.info(f"CONTINUOUS_SILENCE (Sesión {session.session_id}): Iniciando silencio continuo")

            while True:  # Loop infinito hasta que se cancele
                try:
                    header = struct.pack('>BH', 0x10, 320)
                    writer.write(header + silence_chunk)
                    await writer.drain()

                    silence_count += 1

                    # Log cada 100 chunks (cada ~2 segundos)
                    if silence_count % 100 == 0:
                        self.logger.debug(f"CONTINUOUS_SILENCE (Sesión {session.session_id}): {silence_count} chunks de silencio enviados")

                    # Timing exacto de infra_avr para silencio
                    await asyncio.sleep(0.0214)  # 21.4ms

                except (ConnectionResetError, ConnectionAbortedError, BrokenPipeError):
                    self.logger.warning(f"CONTINUOUS_SILENCE (Sesión {session.session_id}): Conexión perdida")
                    break
                except Exception as e:
                    self.logger.warning(f"CONTINUOUS_SILENCE (Sesión {session.session_id}): Error enviando silencio: {e}")
                    # Intentar continuar
                    await asyncio.sleep(0.1)

            self.logger.info(f"CONTINUOUS_SILENCE (Sesión {session.session_id}): Silencio continuo terminado después de {silence_count} chunks")

        except asyncio.CancelledError:
            self.logger.info(f"CONTINUOUS_SILENCE (Sesión {session.session_id}): Silencio continuo cancelado correctamente")
            raise  # Re-raise para que la cancelación se propague
        except Exception as e:
            self.logger.error(f"CONTINUOUS_SILENCE (Sesión {session.session_id}): Error crítico: {e}")
    
    async def send_transition_silence(self, writer: asyncio.StreamWriter, session: 'CallSession') -> None:
        """Envía silencio de transición para mantener la llamada activa."""
        try:
            # Enviar 3 chunks de silencio (960ms) para mantener la llamada activa
            silence_chunk = b'\x00' * 320  # 320 bytes de silencio
            
            for i in range(3):
                if session.audio_interrupted:
                    break
                    
                header = struct.pack('>BH', 0x10, 320)
                writer.write(header + silence_chunk)
                await writer.drain()
                
                # Timing normal entre chunks de silencio
                await asyncio.sleep(0.04)  # 40ms
            
            self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Silencio de transición enviado")
            
        except Exception as e:
            self.logger.warning(f"Error enviando silencio de transición: {e}")
    
    async def keepalive_loop(self, writer: asyncio.StreamWriter, session: 'CallSession') -> None:
        """Loop de keepalive para mantener la llamada activa enviando silencio periódico."""
        try:
            silence_chunk = b'\x00' * 320  # 320 bytes de silencio
            
            while True:
                # Esperar 5 segundos entre keepalives (más conservador)
                await asyncio.sleep(5.0)
                
                # Solo enviar keepalive si no estamos enviando audio Y ha pasado tiempo suficiente
                if not session.audio_playing:
                    # Esperar 3 segundos adicionales después de que termine el audio
                    if session.audio_playing_start_time:
                        time_since_audio = (datetime.now() - session.audio_playing_start_time).total_seconds()
                        if time_since_audio < 3.0:
                            continue
                    
                    try:
                        header = struct.pack('>BH', 0x10, 320)
                        writer.write(header + silence_chunk)
                        await writer.drain()
                        
                        self.logger.debug(f"KEEPALIVE (Sesión {session.session_id}): Silencio enviado para mantener llamada activa")
                        
                    except Exception as e:
                        self.logger.warning(f"Error en keepalive: {e}")
                        break
                        
        except asyncio.CancelledError:
            self.logger.debug(f"KEEPALIVE (Sesión {session.session_id}): Keepalive cancelado")
        except Exception as e:
            self.logger.error(f"Error en keepalive loop: {e}")
    
    async def send_buffer_clear_command(self, writer: asyncio.StreamWriter, session: 'CallSession') -> None:
        """Envía comando para limpiar buffer de audio en Asterisk."""
        try:
            # Enviar comando de flush/clear buffer (tipo 0x00 con payload vacío)
            clear_header = struct.pack('>BH', 0x00, 0)
            writer.write(clear_header)
            await writer.drain()
            
            # Pequeña pausa para que Asterisk procese el comando
            await asyncio.sleep(0.01)
            
            self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Comando de limpieza de buffer enviado")
            
        except Exception as e:
            self.logger.warning(f"Error enviando comando de limpieza de buffer: {e}")
