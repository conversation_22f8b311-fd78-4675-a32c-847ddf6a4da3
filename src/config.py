#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Configuración del Sistema
==================================================
Archivo de configuración centralizada para el bot de cobranza.
Entorno virtual: /opt/cobranza-bot/venv

Este módulo maneja todas las configuraciones del sistema incluyendo:
- Variables de entorno
- Configuraciones de APIs
- Parámetros de base de datos
- Configuraciones de audio y conversación

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
Versión: 1.0.0
"""

import os
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass
from pathlib import Path

try:
    from dotenv import load_dotenv
except ImportError:
    load_dotenv = None


# Cargar variables de entorno
if load_dotenv:
    load_dotenv()


@dataclass
class DatabaseConfig:
    """Configuración de la base de datos PostgreSQL."""
    host: str = os.getenv('DB_HOST', 'localhost')
    port: int = int(os.getenv('DB_PORT', '5432'))
    name: str = os.getenv('DB_NAME', 'cobranza_bot')
    user: str = os.getenv('DB_USER', 'cobranza_user')
    password: str = os.getenv('DB_PASSWORD', '')
    pool_size: int = int(os.getenv('DB_POOL_SIZE', '10'))
    max_overflow: int = int(os.getenv('DB_MAX_OVERFLOW', '20'))
    
    @property
    def connection_string(self) -> str:
        """Genera la cadena de conexión PostgreSQL."""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


@dataclass
class OpenAIConfig:
    """Configuración para la API de OpenAI."""
    api_key: str = os.getenv('OPENAI_API_KEY', '')
    model: str = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')  # Modelo más rápido
    max_tokens: int = int(os.getenv('OPENAI_MAX_TOKENS', '100'))  # Suficiente para JSON completo
    temperature: float = float(os.getenv('OPENAI_TEMPERATURE', '0.0'))  # Completamente determinístico
    
    def validate(self) -> bool:
        """Valida que la configuración de OpenAI esté completa."""
        return bool(self.api_key and self.api_key.startswith('sk-'))


@dataclass
class GoogleTTSConfig:
    """Configuración para Google Cloud Text-to-Speech."""
    credentials_path: str = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '')
    project_id: str = os.getenv('GOOGLE_CLOUD_PROJECT', '')
    
    # Configuraciones de voz
    language_code: str = os.getenv('TTS_LANGUAGE_CODE', 'es-US')
    voice_name: str = os.getenv('TTS_VOICE_NAME', 'es-US-Chirp3-HD-Achernar')
    
    # Configuraciones de audio
    audio_encoding: str = os.getenv('TTS_AUDIO_ENCODING', 'LINEAR16')
    sample_rate: int = int(os.getenv('TTS_SAMPLE_RATE', '8000'))
    
    # Configuraciones de síntesis
    speaking_rate: float = float(os.getenv('TTS_SPEAKING_RATE', '1.0'))
    pitch: float = float(os.getenv('TTS_PITCH', '0.0'))
    volume_gain_db: float = float(os.getenv('TTS_VOLUME_GAIN_DB', '0.0'))
    
    def validate(self) -> bool:
        """Valida que la configuración de Google TTS esté completa."""
        return bool(self.credentials_path or self.project_id)


@dataclass
class DeepgramConfig:
    """Configuración para la API de Deepgram (ASR Streaming)."""
    api_key: str = os.getenv('DEEPGRAM_API_KEY', '')
    model: str = os.getenv('DEEPGRAM_MODEL', 'nova-2')
    language: str = os.getenv('DEEPGRAM_LANGUAGE', 'es')
    encoding: str = os.getenv('DEEPGRAM_ENCODING', 'linear16')
    sample_rate: int = int(os.getenv('DEEPGRAM_SAMPLE_RATE', '8000'))
    
    # Configuraciones básicas para streaming (COMPATIBLES)
    interim_results: bool = True
    punctuate: bool = False  # Sin puntuación para velocidad
    profanity_filter: bool = False
    redact: list = None
    
    def __post_init__(self):
        """Inicialización posterior para configuraciones por defecto."""
        if self.redact is None:
            self.redact = ['pci', 'numbers']
    
    def validate(self) -> bool:
        """Valida que la configuración de Deepgram esté completa."""
        return bool(self.api_key)
    
    @property
    def streaming_options(self) -> dict:
        """Retorna las opciones básicas de configuración para streaming."""
        return {
            'model': self.model,
            'language': self.language,
            'encoding': self.encoding,
            'sample_rate': self.sample_rate,
            'interim_results': self.interim_results,
            'punctuate': self.punctuate,
            'profanity_filter': self.profanity_filter
        }


@dataclass
class AudioSocketConfig:
    """Configuración para el servidor AudioSocket."""
    host: str = os.getenv('AUDIOSOCKET_HOST', '127.0.0.1')
    port: int = int(os.getenv('AUDIOSOCKET_PORT', '5001'))
    buffer_size: int = int(os.getenv('AUDIOSOCKET_BUFFER_SIZE', '1024'))  # Reducido para mejor estabilidad
    sample_rate: int = int(os.getenv('AUDIOSOCKET_SAMPLE_RATE', '8000'))
    channels: int = int(os.getenv('AUDIOSOCKET_CHANNELS', '1'))

    # Configuraciones de timeout y reintentos
    chunk_timeout: float = float(os.getenv('AUDIOSOCKET_CHUNK_TIMEOUT', '2.0'))  # Timeout por chunk
    max_retries: int = int(os.getenv('AUDIOSOCKET_MAX_RETRIES', '3'))  # Reintentos máximos
    retry_delay: float = float(os.getenv('AUDIOSOCKET_RETRY_DELAY', '0.1'))  # Delay entre reintentos
    connection_timeout: float = float(os.getenv('AUDIOSOCKET_CONNECTION_TIMEOUT', '10.0'))  # Timeout de conexión

    # Configuraciones de optimización
    chunk_pause_interval: int = int(os.getenv('AUDIOSOCKET_CHUNK_PAUSE_INTERVAL', '10'))  # Pausa cada N chunks
    chunk_pause_duration: float = float(os.getenv('AUDIOSOCKET_CHUNK_PAUSE_DURATION', '0.001'))  # Duración de pausa

    @property
    def address(self) -> tuple:
        """Retorna la dirección como tupla (host, port)."""
        return (self.host, self.port)


@dataclass
class AudioConfig:
    """Configuración para procesamiento de audio."""
    chunk_size: int = int(os.getenv('AUDIO_CHUNK_SIZE', '4096'))  # Aumentado para mejor calidad
    min_chunk_size: int = int(os.getenv('AUDIO_MIN_CHUNK_SIZE', '2048'))  # Aumentado para mejor calidad
    format: int = int(os.getenv('AUDIO_FORMAT', '16'))
    rate: int = int(os.getenv('AUDIO_RATE', '8000'))
    channels: int = int(os.getenv('AUDIO_CHANNELS', '1'))
    vad_aggressiveness: int = int(os.getenv('VAD_AGGRESSIVENESS', '2'))


@dataclass
class ConversationConfig:
    """Configuración para el manejo de conversaciones."""
    timeout: int = int(os.getenv('CONVERSATION_TIMEOUT', '300'))
    max_turns: int = int(os.getenv('MAX_CONVERSATION_TURNS', '20'))
    silence_timeout: int = int(os.getenv('SILENCE_TIMEOUT', '5'))
    interruption_threshold: float = float(os.getenv('INTERRUPTION_THRESHOLD', '0.5'))


@dataclass
class BusinessConfig:
    """Configuración de reglas de negocio para cobranza."""
    max_debt_amount: float = float(os.getenv('MAX_DEBT_AMOUNT', '50000.00'))
    min_payment_amount: float = float(os.getenv('MIN_PAYMENT_AMOUNT', '100.00'))
    max_payment_days: int = int(os.getenv('MAX_PAYMENT_DAYS', '90'))
    default_payment_days: int = int(os.getenv('DEFAULT_PAYMENT_DAYS', '30'))


@dataclass
class SecurityConfig:
    """Configuración de seguridad."""
    secret_key: str = os.getenv('SECRET_KEY', '')
    encryption_algorithm: str = os.getenv('ENCRYPTION_ALGORITHM', 'AES-256')
    session_timeout: int = int(os.getenv('SESSION_TIMEOUT', '1800'))


@dataclass
class AsteriskConfig:
    """Configuración para conexión con Asterisk AMI."""
    host: str = os.getenv('ASTERISK_HOST', 'localhost')
    ami_port: int = int(os.getenv('ASTERISK_AMI_PORT', '5038'))
    ami_user: str = os.getenv('ASTERISK_AMI_USER', 'avr')
    ami_secret: str = os.getenv('ASTERISK_AMI_SECRET', 'avr')
    timeout: int = int(os.getenv('ASTERISK_AMI_TIMEOUT', '10'))

    def validate(self) -> bool:
        """Valida que la configuración AMI esté completa."""
        return bool(self.host and self.ami_user and self.ami_secret)


@dataclass
class PerformanceConfig:
    """Configuración de rendimiento."""
    max_concurrent_calls: int = int(os.getenv('MAX_CONCURRENT_CALLS', '10'))
    connection_pool_size: int = int(os.getenv('CONNECTION_POOL_SIZE', '20'))
    request_timeout: int = int(os.getenv('REQUEST_TIMEOUT', '30'))
    retry_attempts: int = int(os.getenv('RETRY_ATTEMPTS', '3'))
    retry_delay: int = int(os.getenv('RETRY_DELAY', '1'))


class LoggingConfig:
    """Configuración del sistema de logging."""
    
    def __init__(self):
        self.level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.file = os.getenv('LOG_FILE', '/var/log/cobranza-bot/app.log')
        self.max_size = os.getenv('LOG_MAX_SIZE', '10MB')
        self.backup_count = int(os.getenv('LOG_BACKUP_COUNT', '5'))
        self.format = os.getenv(
            'LOG_FORMAT', 
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def setup_logging(self) -> None:
        """Configura el sistema de logging."""
        # Crear directorio de logs si no existe
        log_dir = Path(self.file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configurar logging
        logging.basicConfig(
            level=getattr(logging, self.level),
            format=self.format,
            handlers=[
                logging.FileHandler(self.file),
                logging.StreamHandler()
            ]
        )


class Config:
    """Configuración principal del sistema."""
    
    def __init__(self):
        # Configuraciones de desarrollo
        self.debug = os.getenv('DEBUG', 'False').lower() == 'true'
        self.testing = os.getenv('TESTING', 'False').lower() == 'true'
        self.development_mode = os.getenv('DEVELOPMENT_MODE', 'False').lower() == 'true'
        
        # Ruta del entorno virtual
        self.venv_path = os.getenv('VENV_PATH', '/opt/cobranza-bot/venv')
        
        # Inicializar configuraciones
        self.database = DatabaseConfig()
        self.openai = OpenAIConfig()
        self.google_tts = GoogleTTSConfig()
        self.deepgram = DeepgramConfig()
        self.audiosocket = AudioSocketConfig()
        self.audio = AudioConfig()
        self.conversation = ConversationConfig()
        self.business = BusinessConfig()
        self.security = SecurityConfig()
        self.asterisk = AsteriskConfig()
        self.performance = PerformanceConfig()
        self.logging = LoggingConfig()
        
        # Configurar logging
        self.logging.setup_logging()
        
        # Validar configuraciones críticas
        self._validate_config()
    
    def validate(self) -> bool:
        """Valida configuraciones críticas del sistema."""
        try:
            self._validate_config()
            return True
        except ValueError:
            return False
    
    def _validate_config(self) -> None:
        """Valida configuraciones críticas del sistema."""
        errors = []
        
        if not self.openai.validate():
            errors.append("OpenAI API key no configurada o inválida")
        
        if not self.google_tts.validate():
            errors.append("Google Cloud TTS credentials no configuradas")
        
        if not self.deepgram.validate():
            errors.append("Deepgram API key no configurada")
        
        if not self.database.password:
            errors.append("Password de base de datos no configurada")
        
        if not self.security.secret_key:
            errors.append("Secret key de seguridad no configurada")
        
        if errors:
            error_msg = "Errores de configuración:\n" + "\n".join(f"- {error}" for error in errors)
            raise ValueError(error_msg)
    
    def get_summary(self) -> Dict[str, Any]:
        """Retorna un resumen de la configuración (sin datos sensibles)."""
        return {
            'debug': self.debug,
            'testing': self.testing,
            'development_mode': self.development_mode,
            'venv_path': self.venv_path,
            'database_host': self.database.host,
            'database_name': self.database.name,
            'audiosocket_address': f"{self.audiosocket.host}:{self.audiosocket.port}",
            'openai_model': self.openai.model,
            'google_tts_voice': self.google_tts.voice_name,
            'max_concurrent_calls': self.performance.max_concurrent_calls,
            'log_level': self.logging.level
        }


# Instancia global de configuración
config = Config()


def get_config() -> Config:
    """Retorna la instancia global de configuración."""
    return config


def load_config() -> Config:
    """Carga y retorna una nueva instancia de configuración."""
    return Config()


if __name__ == "__main__":
    # Mostrar resumen de configuración para debugging
    import json
    
    print("=" * 50)
    print("CONFIGURACIÓN DEL BOT DE COBRANZA")
    print("=" * 50)
    
    try:
        cfg = get_config()
        summary = cfg.get_summary()
        print(json.dumps(summary, indent=2, ensure_ascii=False))
        print("\n✅ Configuración cargada exitosamente")
    except Exception as e:
        print(f"❌ Error en configuración: {e}")
        exit(1)