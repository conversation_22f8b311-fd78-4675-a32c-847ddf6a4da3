#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Manejador de Acciones
==============================================

Este módulo maneja la ejecución de acciones específicas durante la conversación.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import logging
from typing import List

from conversation_models import ConversationContext, IntentType
from database import DatabaseManager


class ActionHandler:
    """Manejador de acciones de conversación."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    async def execute_actions(self, call_id: str, context: ConversationContext, actions: List[str]) -> None:
        """Ejecuta acciones específicas basadas en la respuesta de IA."""
        try:
            for action in actions:
                if action == "registrar_compromiso":
                    await self._registrar_compromiso(context)
                elif action == "actualizar_cliente":
                    await self._actualizar_info_cliente(context)
                elif action == "enviar_recordatorio":
                    await self._programar_recordatorio(context)
                elif action == "escalar_caso":
                    await self._escalar_caso(context)
                elif action == "esperar_respuesta_cliente":
                    # Acción de espera - no requiere implementación específica
                    self.logger.debug(f"Esperando respuesta del cliente")
                elif action == "verificar_identidad_cliente":
                    # Acción de verificación - ya manejada por el flujo de intenciones
                    self.logger.debug(f"Verificando identidad del cliente")
                elif action == "esperar_confirmacion_nombre":
                    # Acción de espera de confirmación - no requiere implementación específica
                    self.logger.debug(f"Esperando confirmación de nombre")
                elif action == "registrar_preferencia_contacto":
                    # Acción de registro de preferencias - implementación futura
                    self.logger.debug(f"Registrando preferencia de contacto")
                elif action == "esperar_confirmacion_nombre_cliente":
                    # Acción de espera de confirmación de nombre - no requiere implementación específica
                    self.logger.debug(f"Esperando confirmación de nombre del cliente")
                elif action == "verificar_datos_cliente":
                    # Acción de verificación de datos del cliente
                    self.logger.debug(f"Verificando datos del cliente")
                elif action == "solicitar_datos_cliente":
                    # Acción de solicitud de datos del cliente
                    self.logger.debug(f"Solicitando datos del cliente")
                elif action == "validar_datos_ingresados":
                    # Acción de validación de datos ingresados
                    self.logger.debug(f"Validando datos ingresados")
                elif action == "esperar respuesta del cliente sobre disponibilidad de llamada":
                    # Acción de espera de respuesta sobre disponibilidad
                    self.logger.debug(f"Esperando respuesta sobre disponibilidad de llamada")
                elif action == "registrar preferencia de horario si aplica":
                    # Acción de registro de preferencia de horario
                    self.logger.debug(f"Registrando preferencia de horario")
                elif action == "esperar_confirmacion_datos_cliente":
                    # Acción de espera de confirmación de datos
                    self.logger.debug(f"Esperando confirmación de datos del cliente")
                elif action == "verificar_cuenta_en_sistema":
                    # Acción de verificación de cuenta en sistema
                    self.logger.debug(f"Verificando cuenta en sistema")
                else:
                    self.logger.warning(f"Acción desconocida: {action}")
                
        except Exception as e:
            self.logger.error(f"Error ejecutando acciones: {e}")
    
    async def update_context_by_intent(self, context: ConversationContext, intent: IntentType, 
                                     intent_metadata: dict, stats: dict) -> None:
        """Actualiza el contexto basado en la intención detectada."""
        try:
            # Procesar intenciones específicas
            if intent == IntentType.CONFIRMACION_IDENTIDAD:
                context.intentos_identificacion = 0
                if not context.cliente_id:
                    # Buscar cliente nuevamente si no estaba identificado
                    cliente_info = await self._get_cliente_info(context.telefono)
                    if cliente_info:
                        context.cliente_id = cliente_info['id']
                        context.nombre_cliente = cliente_info['nombre']
                        context.deuda_total = cliente_info['deuda_total']
                        context.deudas_activas = cliente_info['deudas']
                        stats['successful_identifications'] += 1
            
            elif intent == IntentType.NEGACION_IDENTIDAD:
                context.intentos_identificacion += 1
            
            elif intent == IntentType.COMPROMISO_PAGO:
                stats['payment_commitments'] += 1
            
            # Agregar notas usando el método del contexto
            reasoning = intent_metadata.get('reasoning', '')
            context.add_note(intent, reasoning)
            
        except Exception as e:
            self.logger.error(f"Error actualizando contexto: {e}")
    
    async def _get_cliente_info(self, telefono: str) -> dict:
        """Obtiene información del cliente desde la base de datos."""
        try:
            # Nota: La búsqueda por teléfono ha sido eliminada por requerimiento del usuario
            # Esta función ahora retorna información vacía ya que no se buscan clientes por teléfono
            self.logger.info(f"Búsqueda de cliente por teléfono {telefono} omitida por configuración")
            return {}
            
            # TODO: Implementar búsqueda alternativa por otros medios si es necesario
            # Por ejemplo: por ID de cliente, documento, etc.
            
        except Exception as e:
            self.logger.error(f"Error obteniendo info del cliente: {e}")
            return {}
    
    async def _registrar_compromiso(self, context: ConversationContext) -> None:
        """Registra un compromiso de pago en la base de datos."""
        try:
            # Extraer información del compromiso desde el contexto
            # Esto debería venir de la conversación con el cliente
            if hasattr(context, 'compromiso_info') and context.compromiso_info:
                from database import CompromisoPago
                from datetime import date, datetime
                
                compromiso = CompromisoPago(
                    deuda_id=context.compromiso_info.get('deuda_id'),
                    fecha_compromiso=context.compromiso_info.get('fecha_compromiso', date.today()),
                    monto_acordado=context.compromiso_info.get('monto_acordado', 0.0)
                )
                
                compromiso_id = await self.db_manager.crear_compromiso_pago(compromiso)
                if compromiso_id:
                    self.logger.info(f"Compromiso de pago creado exitosamente: ID {compromiso_id}")
                    context.add_note("compromiso_registrado", f"Compromiso ID: {compromiso_id}")
                else:
                    self.logger.error("Error creando compromiso de pago")
            else:
                self.logger.warning("No hay información de compromiso disponible para registrar")
                
        except Exception as e:
            self.logger.error(f"Error registrando compromiso: {e}")
    
    async def _actualizar_info_cliente(self, context: ConversationContext) -> None:
        """Actualiza información del cliente."""
        try:
            if context.cliente_id:
                self.logger.info(f"Actualizando info del cliente {context.cliente_id}")
                
        except Exception as e:
            self.logger.error(f"Error actualizando info del cliente: {e}")
    
    async def _programar_recordatorio(self, context: ConversationContext) -> None:
        """Programa un recordatorio para el cliente."""
        try:
            if context.cliente_id:
                self.logger.info(f"Programando recordatorio para cliente {context.cliente_id}")
                
        except Exception as e:
            self.logger.error(f"Error programando recordatorio: {e}")
    
    async def _escalar_caso(self, context: ConversationContext) -> None:
        """Escala el caso a un supervisor."""
        try:
            if context.cliente_id:
                self.logger.info(f"Escalando caso del cliente {context.cliente_id}")
                
        except Exception as e:
            self.logger.error(f"Error escalando caso: {e}")