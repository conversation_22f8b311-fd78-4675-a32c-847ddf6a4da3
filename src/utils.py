#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Utilidades del Sistema
===============================================

Este módulo contiene funciones de utilidad para:
- Validación de datos
- Formateo de números y fechas
- Limpieza de texto
- Funciones de logging
- Helpers para audio
- Validaciones de negocio

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import re
import logging
import asyncio
from typing import Optional, Dict, Any, List, Union
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation
import unicodedata
import json


class ValidationError(Exception):
    """Excepción para errores de validación."""
    pass


class PhoneValidator:
    """Validador de números telefónicos."""
    
    # Patrones para diferentes formatos de teléfono
    PATTERNS = {
        'chile_mobile': re.compile(r'^\+?56\s?9\s?[0-9]{8}$'),
        'chile_fixed': re.compile(r'^\+?56\s?[2-9]\s?[0-9]{8}$'),
        'international': re.compile(r'^\+[1-9]\d{1,14}$'),
        'local': re.compile(r'^[0-9]{8,9}$')
    }
    
    @classmethod
    def clean_phone(cls, phone: str) -> str:
        """Limpia y normaliza un número telefónico."""
        if not phone:
            return ""
        
        # Remover espacios, guiones y paréntesis
        cleaned = re.sub(r'[\s\-\(\)\.]', '', phone)
        
        # Normalizar código de país para Chile
        if cleaned.startswith('56') and len(cleaned) >= 10:
            cleaned = '+' + cleaned
        elif cleaned.startswith('9') and len(cleaned) == 9:
            cleaned = '+569' + cleaned[1:]
        elif not cleaned.startswith('+') and len(cleaned) >= 8:
            # Asumir número local chileno
            if cleaned.startswith('9'):
                cleaned = '+56' + cleaned
            else:
                cleaned = '+56' + cleaned
        
        return cleaned
    
    @classmethod
    def validate_phone(cls, phone: str) -> bool:
        """Valida si un número telefónico es válido."""
        if not phone:
            return False
        
        cleaned = cls.clean_phone(phone)
        
        for pattern in cls.PATTERNS.values():
            if pattern.match(cleaned):
                return True
        
        return False
    
    @classmethod
    def format_phone(cls, phone: str, format_type: str = 'international') -> str:
        """Formatea un número telefónico."""
        cleaned = cls.clean_phone(phone)
        
        if not cls.validate_phone(cleaned):
            return phone  # Retornar original si no es válido
        
        if format_type == 'international':
            return cleaned
        elif format_type == 'national' and cleaned.startswith('+56'):
            return cleaned[3:]  # Remover +56
        elif format_type == 'display' and cleaned.startswith('+56'):
            number = cleaned[3:]
            if len(number) == 9 and number.startswith('9'):
                return f"+56 9 {number[1:5]} {number[5:]}"
            else:
                return f"+56 {number[:1]} {number[1:5]} {number[5:]}"
        
        return cleaned


class CurrencyFormatter:
    """Formateador de moneda y números."""
    
    @staticmethod
    def format_currency(amount: Union[float, Decimal, int], currency: str = 'CLP') -> str:
        """Formatea un monto como moneda."""
        try:
            if isinstance(amount, str):
                amount = Decimal(amount)
            elif isinstance(amount, float):
                amount = Decimal(str(amount))
            elif isinstance(amount, int):
                amount = Decimal(amount)
            
            # Formatear con separadores de miles
            formatted = f"{amount:,.0f}"
            
            if currency == 'CLP':
                return f"${formatted}"
            else:
                return f"{formatted} {currency}"
                
        except (InvalidOperation, ValueError):
            return str(amount)
    
    @staticmethod
    def parse_currency(amount_str: str) -> Optional[Decimal]:
        """Parsea una cadena de moneda a Decimal."""
        try:
            # Remover símbolos de moneda y espacios
            cleaned = re.sub(r'[^\d,.-]', '', amount_str)
            
            # Manejar separadores de miles y decimales
            if ',' in cleaned and '.' in cleaned:
                # Formato: 1,234.56
                cleaned = cleaned.replace(',', '')
            elif ',' in cleaned:
                # Podría ser separador de miles o decimal
                parts = cleaned.split(',')
                if len(parts) == 2 and len(parts[1]) <= 2:
                    # Separador decimal
                    cleaned = cleaned.replace(',', '.')
                else:
                    # Separador de miles
                    cleaned = cleaned.replace(',', '')
            
            return Decimal(cleaned)
            
        except (InvalidOperation, ValueError):
            return None


class TextCleaner:
    """Limpiador y normalizador de texto."""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Limpia y normaliza texto."""
        if not text:
            return ""
        
        # Normalizar unicode
        text = unicodedata.normalize('NFKD', text)
        
        # Remover caracteres de control
        text = ''.join(char for char in text if unicodedata.category(char) != 'Cc')
        
        # Normalizar espacios
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    @staticmethod
    def remove_accents(text: str) -> str:
        """Remueve acentos del texto."""
        if not text:
            return ""
        
        # Normalizar y remover acentos
        nfkd_form = unicodedata.normalize('NFD', text)
        return ''.join(char for char in nfkd_form if unicodedata.category(char) != 'Mn')
    
    @staticmethod
    def extract_names(text: str) -> List[str]:
        """Extrae nombres propios del texto."""
        if not text:
            return []
        
        # Patrón para nombres (palabras que empiezan con mayúscula)
        pattern = r'\b[A-ZÁÉÍÓÚÑ][a-záéíóúñ]+\b'
        names = re.findall(pattern, text)
        
        # Filtrar palabras comunes que no son nombres
        common_words = {'Señor', 'Señora', 'Don', 'Doña', 'Doctor', 'Doctora'}
        names = [name for name in names if name not in common_words]
        
        return names


class DateTimeHelper:
    """Helper para manejo de fechas y horas."""
    
    @staticmethod
    def parse_date_string(date_str: str) -> Optional[datetime]:
        """Parsea una cadena de fecha en varios formatos."""
        if not date_str:
            return None
        
        formats = [
            '%Y-%m-%d',
            '%d/%m/%Y',
            '%d-%m-%Y',
            '%Y-%m-%d %H:%M:%S',
            '%d/%m/%Y %H:%M',
            '%d de %B de %Y',
            '%d de %b de %Y'
        ]
        
        # Normalizar texto de meses en español
        months_es = {
            'enero': 'January', 'febrero': 'February', 'marzo': 'March',
            'abril': 'April', 'mayo': 'May', 'junio': 'June',
            'julio': 'July', 'agosto': 'August', 'septiembre': 'September',
            'octubre': 'October', 'noviembre': 'November', 'diciembre': 'December',
            'ene': 'Jan', 'feb': 'Feb', 'mar': 'Mar', 'abr': 'Apr',
            'may': 'May', 'jun': 'Jun', 'jul': 'Jul', 'ago': 'Aug',
            'sep': 'Sep', 'oct': 'Oct', 'nov': 'Nov', 'dic': 'Dec'
        }
        
        date_str_normalized = date_str.lower()
        for es_month, en_month in months_es.items():
            date_str_normalized = date_str_normalized.replace(es_month, en_month)
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str_normalized, fmt)
            except ValueError:
                continue
        
        return None
    
    @staticmethod
    def format_date_spanish(date: datetime, include_time: bool = False) -> str:
        """Formatea una fecha en español."""
        months_es = {
            1: 'enero', 2: 'febrero', 3: 'marzo', 4: 'abril',
            5: 'mayo', 6: 'junio', 7: 'julio', 8: 'agosto',
            9: 'septiembre', 10: 'octubre', 11: 'noviembre', 12: 'diciembre'
        }
        
        day = date.day
        month = months_es[date.month]
        year = date.year
        
        if include_time:
            return f"{day} de {month} de {year} a las {date.strftime('%H:%M')}"
        else:
            return f"{day} de {month} de {year}"
    
    @staticmethod
    def get_business_days_until(target_date: datetime) -> int:
        """Calcula días hábiles hasta una fecha."""
        today = datetime.now().date()
        target = target_date.date()
        
        if target <= today:
            return 0
        
        business_days = 0
        current = today
        
        while current < target:
            current += timedelta(days=1)
            # Lunes=0, Domingo=6
            if current.weekday() < 5:  # Lunes a Viernes
                business_days += 1
        
        return business_days


class AudioHelper:
    """Helper para procesamiento de audio."""
    
    @staticmethod
    def calculate_audio_duration(audio_data: bytes, sample_rate: int = 16000, 
                               channels: int = 1, sample_width: int = 2) -> float:
        """Calcula la duración de audio en segundos."""
        try:
            # Calcular número de samples
            bytes_per_sample = sample_width * channels
            total_samples = len(audio_data) // bytes_per_sample
            
            # Calcular duración
            duration = total_samples / sample_rate
            return duration
            
        except Exception:
            return 0.0
    
    @staticmethod
    def validate_audio_format(audio_data: bytes, expected_format: str = 'wav') -> bool:
        """Valida el formato de audio."""
        if not audio_data:
            return False
        
        # Verificar headers básicos
        if expected_format.lower() == 'wav':
            return audio_data.startswith(b'RIFF') and b'WAVE' in audio_data[:12]
        elif expected_format.lower() == 'mp3':
            return audio_data.startswith(b'ID3') or audio_data.startswith(b'\xff\xfb')
        
        return True  # Asumir válido para otros formatos


class BusinessRuleValidator:
    """Validador de reglas de negocio."""
    
    @staticmethod
    def validate_payment_amount(amount: Decimal, debt_amount: Decimal, 
                              min_percentage: float = 0.1) -> bool:
        """Valida que el monto de pago sea razonable."""
        try:
            if amount <= 0:
                return False
            
            # No puede ser mayor que la deuda
            if amount > debt_amount:
                return False
            
            # Debe ser al menos un porcentaje mínimo de la deuda
            min_amount = debt_amount * Decimal(str(min_percentage))
            if amount < min_amount:
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def validate_payment_date(payment_date: datetime, max_days_ahead: int = 90) -> bool:
        """Valida que la fecha de pago sea razonable."""
        try:
            today = datetime.now().date()
            target = payment_date.date()
            
            # No puede ser en el pasado
            if target < today:
                return False
            
            # No puede ser muy lejos en el futuro
            max_date = today + timedelta(days=max_days_ahead)
            if target > max_date:
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def calculate_payment_score(amount: Decimal, debt_amount: Decimal, 
                              payment_date: datetime) -> float:
        """Calcula un score para una propuesta de pago."""
        try:
            score = 0.0
            
            # Score por porcentaje de la deuda (0-40 puntos)
            percentage = float(amount / debt_amount)
            score += min(40, percentage * 40)
            
            # Score por proximidad de fecha (0-30 puntos)
            days_until = (payment_date.date() - datetime.now().date()).days
            if days_until <= 7:
                score += 30
            elif days_until <= 30:
                score += 20
            elif days_until <= 60:
                score += 10
            
            # Score por monto absoluto (0-30 puntos)
            if amount >= debt_amount:
                score += 30
            elif amount >= debt_amount * Decimal('0.5'):
                score += 20
            elif amount >= debt_amount * Decimal('0.25'):
                score += 10
            
            return min(100.0, score)
            
        except Exception:
            return 0.0


class LoggingHelper:
    """Helper para logging estructurado."""
    
    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """Obtiene un logger configurado."""
        logger = logging.getLogger(name)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @staticmethod
    def log_call_event(logger: logging.Logger, event_type: str, call_id: str, 
                      data: Dict[str, Any]) -> None:
        """Registra eventos de llamada de manera estructurada."""
        try:
            log_data = {
                'event_type': event_type,
                'call_id': call_id,
                'timestamp': datetime.now().isoformat(),
                **data
            }
            
            logger.info(f"CALL_EVENT: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            logger.error(f"Error logging call event: {e}")
    
    @staticmethod
    def log_ai_interaction(logger: logging.Logger, call_id: str, 
                          user_input: str, ai_response: str, 
                          intent: str, confidence: float) -> None:
        """Registra interacciones de IA."""
        try:
            log_data = {
                'call_id': call_id,
                'user_input': user_input[:200],  # Truncar para logs
                'ai_response': ai_response[:200],
                'intent': intent,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"AI_INTERACTION: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            logger.error(f"Error logging AI interaction: {e}")


class ConfigValidator:
    """Validador de configuraciones."""
    
    @staticmethod
    def validate_api_key(api_key: str, service_name: str) -> bool:
        """Valida formato de API key."""
        if not api_key or not isinstance(api_key, str):
            return False
        
        # Validaciones específicas por servicio
        if service_name.lower() == 'openai':
            return api_key.startswith('sk-') and len(api_key) > 20
        elif service_name.lower() == 'deepgram':
            return len(api_key) > 10  # Formato variable
        elif service_name.lower() == 'elevenlabs':
            return len(api_key) > 10  # Formato variable
        
        return len(api_key) > 5  # Validación genérica
    
    @staticmethod
    def validate_database_config(config: Dict[str, Any]) -> List[str]:
        """Valida configuración de base de datos."""
        errors = []
        
        required_fields = ['host', 'port', 'database', 'user', 'password']
        for field in required_fields:
            if not config.get(field):
                errors.append(f"Campo requerido faltante: {field}")
        
        # Validar puerto
        try:
            port = int(config.get('port', 0))
            if port < 1 or port > 65535:
                errors.append("Puerto debe estar entre 1 y 65535")
        except ValueError:
            errors.append("Puerto debe ser un número")
        
        return errors


# Funciones de utilidad async
async def retry_async(func, max_retries: int = 3, delay: float = 1.0, 
                     backoff: float = 2.0):
    """Reintenta una función async con backoff exponencial."""
    last_exception = None
    
    for attempt in range(max_retries):
        try:
            return await func()
        except Exception as e:
            last_exception = e
            if attempt < max_retries - 1:
                await asyncio.sleep(delay * (backoff ** attempt))
            else:
                break
    
    raise last_exception


async def timeout_async(func, timeout_seconds: float):
    """Ejecuta una función async con timeout."""
    try:
        return await asyncio.wait_for(func(), timeout=timeout_seconds)
    except asyncio.TimeoutError:
        raise TimeoutError(f"Operación excedió timeout de {timeout_seconds} segundos")


# Función de testing
def test_utilities():
    """Prueba las utilidades del sistema."""
    print("Probando utilidades del sistema...")
    
    # Probar validador de teléfonos
    phone = "+56987654321"
    print(f"Teléfono válido: {PhoneValidator.validate_phone(phone)}")
    print(f"Teléfono formateado: {PhoneValidator.format_phone(phone, 'display')}")
    
    # Probar formateador de moneda
    amount = 1234567.89
    print(f"Moneda formateada: {CurrencyFormatter.format_currency(amount)}")
    
    # Probar limpiador de texto
    text = "  Hola   Mundo  con  acentos  áéíóú  "
    print(f"Texto limpio: '{TextCleaner.clean_text(text)}'")
    print(f"Sin acentos: '{TextCleaner.remove_accents(text)}'")
    
    # Probar helper de fechas
    date = datetime.now()
    print(f"Fecha en español: {DateTimeHelper.format_date_spanish(date, True)}")
    
    print("Pruebas completadas")


if __name__ == "__main__":
    test_utilities()