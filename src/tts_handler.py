#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Manejador de TTS (Text-to-Speech)
==========================================================

Este módulo implementa el manejador de síntesis de voz usando ElevenLabs
para convertir texto a audio en tiempo real durante las llamadas.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import logging
import io
from typing import Optional, Dict, Any, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime

try:
    from elevenlabs import VoiceSettings, Voice, play, stream
    from elevenlabs.client import ElevenLabs
except ImportError:
    raise ImportError("elevenlabs no está instalado. Ejecute: pip install elevenlabs")

from config import ElevenLabsConfig


@dataclass
class TTSRequest:
    """Solicitud de síntesis de voz."""
    text: str
    voice_id: str
    model_id: str
    voice_settings: Optional[Dict[str, float]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now()


@dataclass
class TTSResult:
    """Resultado de síntesis de voz."""
    audio_data: bytes
    text: str
    duration_ms: int
    voice_id: str
    timestamp: datetime
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now()


class TTSHandler:
    """Manejador de síntesis de texto a voz usando ElevenLabs."""
    
    def __init__(self, config: ElevenLabsConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Cliente ElevenLabs
        self.client: Optional[ElevenLabs] = None
        
        # Estado del handler
        self.is_initialized = False
        
        # Cache de voces disponibles
        self.available_voices: Dict[str, Voice] = {}
        self.current_voice: Optional[Voice] = None
        
        # Configuraciones de voz
        self.voice_settings = VoiceSettings(
            stability=self.config.tts_stability,
            similarity_boost=self.config.tts_clarity,
            style=0.0,
            use_speaker_boost=True
        )
        
        # Estadísticas
        self.stats = {
            'total_requests': 0,
            'total_audio_generated': 0,
            'total_characters_processed': 0,
            'average_generation_time': 0.0,
            'errors': 0
        }
    
    async def initialize(self) -> None:
        """Inicializa el cliente ElevenLabs y carga las voces."""
        try:
            if not self.config.validate():
                raise ValueError("Configuración de ElevenLabs inválida")
            
            # Crear cliente ElevenLabs
            self.client = ElevenLabs(api_key=self.config.api_key)
            
            # Cargar voces disponibles
            await self._load_available_voices()
            
            # Configurar voz por defecto
            await self._set_default_voice()
            
            self.is_initialized = True
            self.logger.info("TTS Handler inicializado con ElevenLabs")
            
        except Exception as e:
            self.logger.error(f"Error inicializando TTS Handler: {e}")
            raise
    
    async def _load_available_voices(self) -> None:
        """Carga las voces disponibles desde ElevenLabs."""
        try:
            # Ejecutar en thread pool para evitar bloqueo
            loop = asyncio.get_event_loop()
            voices_response = await loop.run_in_executor(None, self.client.voices.get_all)
            
            # Procesar voces
            for voice in voices_response.voices:
                self.available_voices[voice.voice_id] = voice
                self.logger.debug(f"Voz cargada: {voice.name} ({voice.voice_id})")
            
            self.logger.info(f"Cargadas {len(self.available_voices)} voces disponibles")
            
        except Exception as e:
            self.logger.error(f"Error cargando voces: {e}")
            raise
    
    async def _set_default_voice(self) -> None:
        """Configura la voz por defecto."""
        try:
            if self.config.voice_id in self.available_voices:
                self.current_voice = self.available_voices[self.config.voice_id]
                self.logger.info(f"Voz configurada: {self.current_voice.name}")
            else:
                # Usar primera voz disponible como fallback
                if self.available_voices:
                    first_voice_id = list(self.available_voices.keys())[0]
                    self.current_voice = self.available_voices[first_voice_id]
                    self.logger.warning(f"Voz por defecto no encontrada, usando: {self.current_voice.name}")
                else:
                    raise ValueError("No hay voces disponibles")
                    
        except Exception as e:
            self.logger.error(f"Error configurando voz por defecto: {e}")
            raise
    
    async def text_to_speech(self, text: str, voice_id: Optional[str] = None) -> bytes:
        """Convierte texto a audio usando síntesis de voz."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not text or not text.strip():
                raise ValueError("Texto vacío para síntesis")
            
            # Usar voz especificada o por defecto
            target_voice_id = voice_id or self.config.voice_id
            if target_voice_id not in self.available_voices:
                target_voice_id = self.current_voice.voice_id
            
            # Crear solicitud
            request = TTSRequest(
                text=text.strip(),
                voice_id=target_voice_id,
                model_id=self.config.model_id,
                voice_settings={
                    'stability': self.config.tts_stability,
                    'similarity_boost': self.config.tts_clarity
                }
            )
            
            # Generar audio
            start_time = datetime.now()
            audio_data = await self._generate_audio(request)
            generation_time = (datetime.now() - start_time).total_seconds()
            
            # Actualizar estadísticas
            self._update_stats(request, len(audio_data), generation_time)
            
            self.logger.info(f"Audio generado: {len(text)} caracteres -> {len(audio_data)} bytes")
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error en síntesis de voz: {e}")
            self.stats['errors'] += 1
            
            # Verificar si es un error de cuota
            if hasattr(e, 'status_code') and e.status_code == 401:
                error_body = getattr(e, 'body', {})
                if isinstance(error_body, dict) and error_body.get('detail', {}).get('status') == 'quota_exceeded':
                    self.logger.warning("Cuota de ElevenLabs excedida. Devolviendo audio silencioso.")
                    # Devolver audio silencioso como fallback
                    return self._generate_silence_audio(len(text))
            
            raise
    
    async def _generate_audio(self, request: TTSRequest) -> bytes:
        """Genera audio usando la API de ElevenLabs."""
        try:
            # Ejecutar generación en thread pool
            loop = asyncio.get_event_loop()
            
            def _generate():
                return self.client.text_to_speech.convert(
                    text=request.text,
                    voice_id=request.voice_id,
                    model_id=request.model_id,
                    voice_settings=self.voice_settings,
                    output_format="pcm_8000"  # Formato compatible con AudioSocket 8kHz
                )
            
            audio_generator = await loop.run_in_executor(None, _generate)
            
            # Convertir generator a bytes
            audio_data = b''
            for chunk in audio_generator:
                audio_data += chunk
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error generando audio: {e}")
            
            # Verificar si es un error de cuota específicamente
            if hasattr(e, 'status_code') and e.status_code == 401:
                error_body = getattr(e, 'body', {})
                if isinstance(error_body, dict) and error_body.get('detail', {}).get('status') == 'quota_exceeded':
                    self.logger.warning("Cuota de ElevenLabs excedida en generación")
                    # Devolver audio silencioso
                    return self._generate_silence_audio(len(request.text))
            
            raise
    
    async def text_to_speech_stream(self, text: str, voice_id: Optional[str] = None) -> AsyncGenerator[bytes, None]:
        """Convierte texto a audio con streaming para respuesta en tiempo real."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not text or not text.strip():
                return
            
            # Usar voz especificada o por defecto
            target_voice_id = voice_id or self.config.voice_id
            if target_voice_id not in self.available_voices:
                target_voice_id = self.current_voice.voice_id
            
            # Generar audio en streaming
            loop = asyncio.get_event_loop()
            
            def _stream_generate():
                return self.client.text_to_speech.stream(
                    text=text.strip(),
                    voice_id=target_voice_id,
                    model_id=self.config.model_id,
                    voice_settings=self.voice_settings,
                    output_format="pcm_8000"  # Formato compatible con AudioSocket 8kHz
                )
            
            audio_stream = await loop.run_in_executor(None, _stream_generate)
            
            # Yield chunks de audio
            total_bytes = 0
            for chunk in audio_stream:
                total_bytes += len(chunk)
                yield chunk
            
            # Actualizar estadísticas
            self.stats['total_requests'] += 1
            self.stats['total_audio_generated'] += total_bytes
            self.stats['total_characters_processed'] += len(text)
            
            self.logger.info(f"Audio streaming generado: {len(text)} caracteres -> {total_bytes} bytes")
            
        except Exception as e:
            self.logger.error(f"Error en streaming TTS: {e}")
            self.stats['errors'] += 1
    
    async def get_available_voices(self) -> Dict[str, Dict[str, Any]]:
        """Retorna información de las voces disponibles."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            voices_info = {}
            for voice_id, voice in self.available_voices.items():
                voices_info[voice_id] = {
                    'name': voice.name,
                    'category': getattr(voice, 'category', 'unknown'),
                    'description': getattr(voice, 'description', ''),
                    'labels': getattr(voice, 'labels', {}),
                    'preview_url': getattr(voice, 'preview_url', '')
                }
            
            return voices_info
            
        except Exception as e:
            self.logger.error(f"Error obteniendo voces: {e}")
            return {}
    
    async def set_voice(self, voice_id: str) -> bool:
        """Cambia la voz activa."""
        try:
            if voice_id in self.available_voices:
                self.current_voice = self.available_voices[voice_id]
                self.config.voice_id = voice_id
                self.logger.info(f"Voz cambiada a: {self.current_voice.name}")
                return True
            else:
                self.logger.warning(f"Voz no encontrada: {voice_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error cambiando voz: {e}")
            return False
    
    async def update_voice_settings(self, stability: Optional[float] = None, 
                                  clarity: Optional[float] = None) -> None:
        """Actualiza configuraciones de voz."""
        try:
            if stability is not None:
                self.config.tts_stability = max(0.0, min(1.0, stability))
                
            if clarity is not None:
                self.config.tts_clarity = max(0.0, min(1.0, clarity))
            
            # Actualizar voice_settings
            self.voice_settings = VoiceSettings(
                stability=self.config.tts_stability,
                similarity_boost=self.config.tts_clarity,
                style=0.0,
                use_speaker_boost=True
            )
            
            self.logger.info(f"Configuraciones de voz actualizadas: stability={self.config.tts_stability}, clarity={self.config.tts_clarity}")
            
        except Exception as e:
            self.logger.error(f"Error actualizando configuraciones de voz: {e}")
    
    def _update_stats(self, request: TTSRequest, audio_size: int, generation_time: float) -> None:
        """Actualiza estadísticas del handler."""
        try:
            self.stats['total_requests'] += 1
            self.stats['total_audio_generated'] += audio_size
            self.stats['total_characters_processed'] += len(request.text)
            
            # Calcular tiempo promedio de generación
            current_avg = self.stats['average_generation_time']
            total_requests = self.stats['total_requests']
            
            if total_requests == 1:
                self.stats['average_generation_time'] = generation_time
            else:
                self.stats['average_generation_time'] = (
                    (current_avg * (total_requests - 1) + generation_time) / total_requests
                )
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estadísticas del handler."""
        return {
            **self.stats,
            'is_initialized': self.is_initialized,
            'current_voice': self.current_voice.name if self.current_voice else None,
            'available_voices_count': len(self.available_voices),
            'voice_settings': {
                'stability': self.config.tts_stability,
                'clarity': self.config.tts_clarity
            }
        }
    
    def _generate_silence_audio(self, text_length: int) -> bytes:
        """Genera audio silencioso como fallback cuando hay errores de cuota."""
        try:
            # Calcular duración aproximada basada en longitud del texto
            # Asumiendo ~150 palabras por minuto, ~5 caracteres por palabra
            estimated_duration_seconds = max(1, text_length / (150 * 5 / 60))
            
            # Generar audio silencioso en formato PCM 8kHz, 16-bit, mono
            sample_rate = 8000
            samples_needed = int(sample_rate * estimated_duration_seconds)
            
            # Crear audio silencioso (todos los valores en 0)
            silence_audio = b'\x00\x00' * samples_needed
            
            self.logger.info(f"Generado audio silencioso de {estimated_duration_seconds:.1f}s para texto de {text_length} caracteres")
            return silence_audio
            
        except Exception as e:
            self.logger.error(f"Error generando audio silencioso: {e}")
            # Fallback mínimo: 1 segundo de silencio
            return b'\x00\x00' * 8000
    
    async def cleanup(self) -> None:
        """Limpia recursos del handler."""
        try:
            self.client = None
            self.available_voices.clear()
            self.current_voice = None
            self.is_initialized = False
            
            self.logger.info("TTS Handler limpiado")
            
        except Exception as e:
            self.logger.error(f"Error limpiando TTS Handler: {e}")


# Función de utilidad para testing
async def test_tts_handler():
    """Función de prueba para el TTS Handler."""
    from config import Config
    
    config = Config()
    handler = TTSHandler(config.elevenlabs)
    
    try:
        await handler.initialize()
        print("TTS Handler inicializado correctamente")
        
        # Probar síntesis básica
        test_text = "Hola, este es un mensaje de prueba del sistema de cobranza."
        audio_data = await handler.text_to_speech(test_text)
        print(f"Audio generado: {len(audio_data)} bytes")
        
        # Mostrar voces disponibles
        voices = await handler.get_available_voices()
        print(f"Voces disponibles: {len(voices)}")
        
        # Mostrar estadísticas
        stats = handler.get_stats()
        print(f"Estadísticas: {stats}")
        
    except Exception as e:
        print(f"Error en prueba: {e}")
    finally:
        await handler.cleanup()


if __name__ == "__main__":
    asyncio.run(test_tts_handler())