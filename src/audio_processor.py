#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Audio Processor
===============

Maneja el procesamiento de audio entrante y saliente para AudioSocket.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import struct
from datetime import datetime
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from call_session import CallSession

class AudioProcessor:
    """Procesador de audio para AudioSocket."""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
    
    async def process_incoming_audio(self, session: 'CallSession', audio_chunk: bytes, writer: asyncio.StreamWriter) -> None:
        """Procesa audio entrante del cliente, gestionando interrupciones."""
        self.logger.debug(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): Recibido chunk de {len(audio_chunk)} bytes. Estado: audio_playing={session.audio_playing}, audio_interrupted={session.audio_interrupted}")
        
        try:
            # CRÍTICO: Cancelar silencio continuo cuando llega audio del usuario
            if session.silence_task and not session.silence_task.done():
                session.silence_task.cancel()
                self.logger.info(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): Silencio continuo cancelado por audio entrante")
                try:
                    await session.silence_task
                except asyncio.CancelledError:
                    pass
                session.silence_task = None

            # 🚀 OPTIMIZACIÓN: INTERRUPCIÓN INMEDIATA - Si el bot está hablando, interrumpir INMEDIATAMENTE
            if session.audio_playing and not session.audio_interrupted:
                self.logger.warning(f"🔥 INTERRUPCIÓN DETECTADA (Sesión {session.session_id}): Cliente habla mientras bot reproduce audio. INTERRUMPIENDO INMEDIATAMENTE.")
                session.audio_interrupted = True
                # Cancelar cualquier tarea de envío de audio en curso
                if hasattr(session, 'audio_send_task') and session.audio_send_task and not session.audio_send_task.done():
                    session.audio_send_task.cancel()
                    self.logger.info(f"🔥 Tarea de envío de audio cancelada para sesión {session.session_id}")
                # NO procesar este audio como respuesta, solo marcar interrupción
                return
            
            # Verificar si audio_playing lleva demasiado tiempo activo (timeout de seguridad)
            if hasattr(session, 'audio_playing_start_time') and session.audio_playing_start_time:
                time_playing = datetime.now() - session.audio_playing_start_time
                if time_playing.total_seconds() > 30:  # 30 segundos máximo
                    self.logger.warning(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): audio_playing activo por {time_playing.total_seconds():.1f}s. Forzando reset.")
                    session.audio_playing = False
                    session.audio_playing_start_time = None

            # Esta lógica ya se maneja arriba - eliminar duplicación

            # Si el bot no está hablando, procesar audio para ASR
            if session.audio_interrupted:
                self.logger.info(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): Reseteando audio_interrupted de True a False.")
                session.audio_interrupted = False

            self.logger.debug(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): Bot no está hablando. Enviando chunk a ASR.")
            text = await session.asr_handler.process_audio(audio_chunk)

            if text and text.strip():
                self.logger.info(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): Texto reconocido por ASR: '{text}'")

                # CRÍTICO: Control de cola para evitar desincronización
                if session.processing_user_input:
                    self.logger.warning(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): Ya procesando entrada anterior. Agregando a cola: '{text}'")
                    session.pending_user_inputs.append(text)
                    return

                # Marcar como procesando
                session.processing_user_input = True

                try:
                    await self._process_user_text(session, text, writer)
                finally:
                    # Procesar siguiente en cola si existe
                    session.processing_user_input = False
                    if session.pending_user_inputs:
                        next_text = session.pending_user_inputs.pop(0)
                        self.logger.info(f"PROCESS_INCOMING_AUDIO (Sesión {session.session_id}): Procesando siguiente en cola: '{next_text}'")
                        # Procesar de forma asíncrona para no bloquear
                        asyncio.create_task(self._process_user_text(session, next_text, writer))

        except Exception as e:
            self.logger.error(f"Error en _process_incoming_audio (sesión {session.session_id}): {e}", exc_info=True)
            session.audio_interrupted = True # Marcar como interrumpido en caso de error para evitar bucles

    async def _process_user_text(self, session: 'CallSession', text: str, writer) -> None:
        """Procesa texto del usuario de forma sincronizada."""
        try:
            self.logger.info(f"PROCESSING_USER_TEXT (Sesión {session.session_id}): Procesando '{text}'")

            # Procesar con IA
            ai_response = await session.ai_engine.process_user_input(session.session_id, text)

            if ai_response and ai_response.text:
                self.logger.info(f"PROCESSING_USER_TEXT (Sesión {session.session_id}): AI generó respuesta: '{ai_response.text}'")

                # Verificar si fue interrumpido durante el procesamiento
                if session.audio_interrupted:
                    self.logger.warning(f"PROCESSING_USER_TEXT (Sesión {session.session_id}): Interrupción detectada durante procesamiento. Cancelando TTS.")
                    return

                # Generar TTS
                audio_data = await session.tts_handler.text_to_speech(ai_response.text)

                # 🚀 OPTIMIZACIÓN: Hacer envío de audio como tarea cancelable
                if not session.audio_interrupted:
                    session.audio_send_task = asyncio.create_task(
                        self.send_audio_to_asterisk(audio_data, writer, session)
                    )
                    try:
                        await session.audio_send_task

                        # 🎉 VERIFICAR SI DEBE FINALIZAR LLAMADA DESPUÉS DEL AUDIO
                        await self._check_call_termination(session, ai_response)

                    except asyncio.CancelledError:
                        self.logger.info(f"PROCESSING_USER_TEXT (Sesión {session.session_id}): Envío de audio cancelado por interrupción")
                else:
                    self.logger.warning(f"PROCESSING_USER_TEXT (Sesión {session.session_id}): Interrupción detectada antes de enviar audio. Cancelando.")
            else:
                self.logger.info(f"PROCESSING_USER_TEXT (Sesión {session.session_id}): AI no generó respuesta de texto.")

        except Exception as e:
            self.logger.error(f"Error procesando texto del usuario: {e}", exc_info=True)
    
    async def send_audio_to_asterisk(self, audio_data: bytes, writer: asyncio.StreamWriter, session: 'CallSession' = None) -> None:
        """Envía datos de audio a Asterisk con manejo robusto de conexiones y reintentos."""
        if not session:
            self.logger.error("Error: send_audio_to_asterisk llamado sin sesión.")
            return

        # session.audio_interrupted NO se resetea aquí.
        # Se resetea en process_incoming_audio cuando se recibe una nueva elocución completa del usuario.
        self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Entrando a la función. audio_playing={session.audio_playing}, audio_interrupted={session.audio_interrupted}")
        session.audio_playing = True
        session.audio_playing_start_time = datetime.now()  # Timestamp para timeout de seguridad
        self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Establecido audio_playing = True. Estado audio_interrupted: {session.audio_interrupted}")

        # Configuración para reintentos y chunks desde config
        max_retries = self.config.audiosocket.max_retries
        retry_delay = self.config.audiosocket.retry_delay
        chunk_timeout = self.config.audiosocket.chunk_timeout
        # Usar chunk size que funcionaba bien
        chunk_size = 320  # 320 bytes como infra_avr

        try:
            for attempt in range(max_retries):
                try:
                    self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Intento {attempt + 1}/{max_retries}.")

                    # Verificar si la conexión sigue activa
                    if not self._is_connection_healthy(writer):
                        self.logger.warning(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Conexión no saludable en intento {attempt + 1}.")
                        if attempt < max_retries - 1:
                            await asyncio.sleep(retry_delay)
                            continue
                        else:
                            session.audio_interrupted = True
                            return

                    total_chunks = (len(audio_data) + chunk_size - 1) // chunk_size
                    chunks_sent = 0

                    self.logger.info(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Iniciando envío de {len(audio_data)} bytes en {total_chunks} chunks. Tamaño de chunk: {chunk_size}. audio_interrupted: {session.audio_interrupted}")

                    # Envío directo sin comandos adicionales
                    for i in range(0, len(audio_data), chunk_size):
                        self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Loop iteración {chunks_sent + 1}/{total_chunks}.")

                        if session.audio_interrupted:
                            self.logger.warning(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): ⚠️ INTERRUPCIÓN DETECTADA ANTES de enviar chunk {chunks_sent + 1}/{total_chunks}. DETENIENDO INMEDIATAMENTE.")
                            session.audio_playing = False  # Marcar que ya no está reproduciendo
                            return  # Salir inmediatamente de toda la función

                        chunk = audio_data[i:i + chunk_size]

                        # Si el chunk es menor que 320 bytes, rellenar con 0xFF (como infra_avr)
                        if len(chunk) < chunk_size:
                            padding_needed = chunk_size - len(chunk)
                            chunk = chunk + b'\xff' * padding_needed

                        header = struct.pack('>BH', 0x10, len(chunk))

                        # Envío con timeout
                        try:
                            self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Preparado chunk {chunks_sent + 1} ({len(chunk)} bytes). Antes de writer.write.")
                            writer.write(header + chunk)

                            # Timeout configurable para drain
                            await asyncio.wait_for(writer.drain(), timeout=chunk_timeout)
                            self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Chunk {chunks_sent + 1} enviado exitosamente.")
                            chunks_sent += 1

                            # Timing EXACTO de infra_avr: 21.4ms (datos reales)
                            await asyncio.sleep(0.0214)  # 21.4ms - timing exacto de infra_avr

                        except asyncio.TimeoutError:
                            self.logger.warning(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Timeout enviando chunk {chunks_sent + 1}.")
                            raise ConnectionResetError("Timeout durante envío de chunk")

                    self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Bucle de envío finalizado. Chunks enviados: {chunks_sent}/{total_chunks}.")
                    if session.audio_interrupted:
                        self.logger.info(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Envío finalizado prematuramente debido a interrupción (estado verificado después del bucle). {chunks_sent}/{total_chunks} chunks enviados.")
                    else:
                        self.logger.info(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Envío completado exitosamente - {chunks_sent}/{total_chunks} chunks.")

                    # CRÍTICO: Iniciar silencio continuo SIEMPRE después del audio (interrumpido o no)
                    from silence_manager import SilenceManager
                    silence_manager = SilenceManager(self.config, self.logger)
                    session.silence_task = asyncio.create_task(silence_manager.send_continuous_silence(writer, session))
                    self.logger.info(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Silencio continuo iniciado")

                    # Si llegamos aquí, el envío fue exitoso
                    return

                except asyncio.CancelledError:
                    self.logger.info(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): EXCEPCIÓN - Envío de audio cancelado (asyncio.CancelledError).")
                    session.audio_interrupted = True # Considerar interrumpido si la tarea se cancela
                    return

                except (ConnectionResetError, ConnectionAbortedError, BrokenPipeError, OSError) as conn_e:
                    self.logger.warning(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): EXCEPCIÓN - Error de conexión en intento {attempt + 1}/{max_retries}: {conn_e}")

                    if attempt < max_retries - 1:
                        self.logger.info(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Reintentando en {retry_delay}s...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # Backoff exponencial
                    else:
                        self.logger.error(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): Todos los reintentos fallaron. Marcando audio_interrupted = True.")
                        session.audio_interrupted = True
                        return

                except Exception as e:
                    self.logger.error(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): EXCEPCIÓN - Error inesperado en intento {attempt + 1}: {e}", exc_info=True)
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2
                    else:
                        session.audio_interrupted = True
                        return

            # Si llegamos aquí, todos los reintentos fallaron
            session.audio_interrupted = True

        finally:
            # CRÍTICO: Siempre resetear audio_playing al final
            session.audio_playing = False
            session.audio_playing_start_time = None  # Limpiar timestamp
            self.logger.debug(f"SEND_AUDIO_TO_ASTERISK (Sesión {session.session_id}): FINALLY - Establecido audio_playing = False. Estado final audio_interrupted: {session.audio_interrupted}.")
    
    def _is_connection_healthy(self, writer: asyncio.StreamWriter) -> bool:
        """Verifica si la conexión está saludable."""
        try:
            if writer.is_closing():
                return False

            transport = writer.get_extra_info('socket')
            if not transport:
                return False

            # Verificar si el socket está conectado
            try:
                transport.getpeername()
                return True
            except (OSError, AttributeError):
                return False

        except Exception:
            return False

    async def _check_call_termination(self, session: 'CallSession', ai_response) -> None:
        """Verifica si la llamada debe terminar y ejecuta la finalización."""
        try:
            # Obtener contexto de conversación
            context = None
            if hasattr(session, 'ai_engine') and session.ai_engine:
                context = session.ai_engine.get_conversation_context(session.session_id)

            if not context:
                return

            # Verificar si debe finalizar por compromiso exitoso
            if getattr(context, 'should_end_call', False) and getattr(context, 'commitment_data', None):
                self.logger.info(f"🎉 FINALIZANDO LLAMADA POR COMPROMISO EXITOSO (Sesión {session.session_id})")

                # Importar CallManager con importación absoluta
                try:
                    from call_manager import CallManager
                except ImportError:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(__file__))
                    from call_manager import CallManager

                call_manager = CallManager(self.config, session.ai_engine.db_manager, self.logger)

                # Agregar información del cliente al contexto de sesión
                session.client_name = getattr(context, 'nombre_cliente', 'cliente')

                # Manejar compromiso exitoso y corte de llamada
                await call_manager.handle_commitment_success(session, context.commitment_data)
                return

            # Verificar si debe finalizar por despedida del usuario (metadata)
            if (ai_response and hasattr(ai_response, 'metadata') and
                ai_response.metadata.get('user_goodbye', False)):
                self.logger.info(f"👋 FINALIZANDO LLAMADA POR DESPEDIDA DEL USUARIO (Sesión {session.session_id})")

                try:
                    from call_manager import CallManager, CallEndReason
                except ImportError:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(__file__))
                    from call_manager import CallManager, CallEndReason

                call_manager = CallManager(self.config, session.ai_engine.db_manager, self.logger)

                # Agregar información del cliente
                session.client_name = getattr(context, 'nombre_cliente', 'cliente')

                # Manejar final de conversación por despedida del usuario
                await call_manager.handle_conversation_end(session, CallEndReason.DESPEDIDA_NORMAL)
                return

            # Verificar si debe finalizar por despedida en la respuesta
            if ai_response and hasattr(ai_response, 'text'):
                try:
                    from call_manager import CallManager, CallEndReason
                except ImportError:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(__file__))
                    from call_manager import CallManager, CallEndReason

                call_manager = CallManager(self.config, session.ai_engine.db_manager, self.logger)

                if call_manager.should_end_conversation(ai_response.text):
                    self.logger.info(f"👋 FINALIZANDO LLAMADA POR DESPEDIDA (Sesión {session.session_id})")

                    # Agregar información del cliente
                    session.client_name = getattr(context, 'nombre_cliente', 'cliente')

                    # Manejar final de conversación
                    await call_manager.handle_conversation_end(session, CallEndReason.DESPEDIDA_NORMAL)
                    return

        except Exception as e:
            self.logger.error(f"Error verificando terminación de llamada: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
