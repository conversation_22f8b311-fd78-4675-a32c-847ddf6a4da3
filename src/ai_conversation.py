#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Motor de Conversación
==============================================

Este módulo maneja la lógica de conversación con IA usando OpenAI GPT.
Procesa las transcripciones de audio, mantiene el contexto de la conversación
y genera respuestas apropiadas para el bot de cobranza.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

try:
    from openai import AsyncOpenAI
except ImportError:
    raise ImportError("openai no está instalado. Ejecute: pip install openai")

from config import OpenAIConfig
from database import DatabaseManager
from utils import LoggingHelper
from conversation_models import (
    ConversationState, IntentType, ConversationContext, 
    ConversationMessage, AIResponse
)
from conversation_prompts import ConversationPrompts
from intent_processor import IntentProcessor
from action_handler import ActionHandler
from debt_service import DebtService
from commitment_service import CommitmentService


class AIConversationEngine:
    """Motor de conversación con IA para el bot de cobranza."""
    
    def __init__(self, config: OpenAIConfig, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.client = AsyncOpenAI(api_key=config.api_key)
        self.logger = LoggingHelper.get_logger(__name__)
        
        # Contextos activos de conversación
        self.active_contexts: Dict[str, ConversationContext] = {}
        
        # Historial de mensajes por llamada
        self.conversation_history: Dict[str, List[ConversationMessage]] = {}
        
        # Estadísticas
        self.stats = {
            'total_calls': 0,
            'successful_identifications': 0,
            'payment_commitments': 0,
            'average_call_duration': 0.0
        }
        
        # Inicializar componentes
        self.prompts = ConversationPrompts()
        self.intent_processor = IntentProcessor(config, self.client)
        self.action_handler = ActionHandler(db_manager)
        self.debt_service = DebtService(db_manager)
        self.commitment_service = CommitmentService(db_manager)
    
    async def initialize(self) -> None:
        """Inicializa el motor de conversación."""
        try:
            self.logger.info("Inicializando motor de conversación con IA")
            
            # Verificar conexión con OpenAI
            test_response = await self.client.chat.completions.create(
                model=self.config.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=5
            )
            
            self.logger.info("Motor de conversación inicializado correctamente")
            
        except Exception as e:
            self.logger.error(f"Error inicializando motor de conversación: {e}")
            raise
    
    async def start_conversation(self, call_id: str, telefono: str) -> ConversationContext:
        """Inicia una nueva conversación."""
        try:
            # Crear contexto de conversación
            context = ConversationContext(
                call_id=call_id,
                telefono=telefono,
                estado=ConversationState.INICIANDO
            )
            
            # Buscar información del cliente
            cliente_info = await self.action_handler._get_cliente_info(telefono)
            if cliente_info:
                context.cliente_id = cliente_info.get('id')
                context.nombre_cliente = cliente_info.get('nombre')
                context.deuda_total = cliente_info.get('deuda_total', 0.0)
                context.deudas_activas = cliente_info.get('deudas', [])
                context.compromisos_previos = cliente_info.get('compromisos', [])
            
            # Guardar contexto
            self.active_contexts[call_id] = context
            
            # Inicializar historial de conversación
            self.conversation_history[call_id] = []
            
            # Agregar mensaje del sistema
            system_prompt = self.prompts.build_system_prompt(context)
            system_message = ConversationMessage(
                role='system',
                content=system_prompt,
                timestamp=datetime.now()
            )
            self.conversation_history[call_id].append(system_message)
            
            # Actualizar estadísticas
            self.stats['total_calls'] += 1
            
            self.logger.info(f"Conversación iniciada para {call_id}")
            return context
            
        except Exception as e:
            self.logger.error(f"Error iniciando conversación: {e}")
            raise
    
    async def process_user_input(self, call_id: str, text: str) -> AIResponse:
        """Procesa la entrada del usuario y genera una respuesta."""
        try:
            # Obtener contexto
            context = self.active_contexts.get(call_id)
            if not context:
                raise ValueError(f"No se encontró contexto para la llamada {call_id}")

            # 👋 VERIFICAR SI USUARIO DICE ADIÓS ANTES DE PROCESAR
            if self._user_says_goodbye(text):
                context.should_end_call = True
                self.logger.info(f"👋 USUARIO DICE ADIÓS (Sesión {call_id}): '{text}' - Generando despedida inmediata")

                # Generar respuesta de despedida inmediata
                client_name = context.nombre_cliente.split()[0] if context.nombre_cliente else "cliente"
                farewell_text = f"Entiendo, {client_name}. Gracias por su tiempo. Que tenga buen día."

                # Agregar mensaje del usuario al historial
                conversation_history = self.conversation_history.get(call_id, [])
                user_message = ConversationMessage(
                    role='user',
                    content=text,
                    timestamp=datetime.now()
                )
                conversation_history.append(user_message)

                # Agregar respuesta de despedida al historial
                conversation_history.append(ConversationMessage(
                    role='assistant',
                    content=farewell_text,
                    timestamp=datetime.now(),
                    intent=IntentType.OTRO,
                    confidence=1.0
                ))

                # Retornar respuesta de despedida directamente
                return AIResponse(
                    text=farewell_text,
                    intent=IntentType.OTRO,
                    confidence=1.0,
                    next_state=ConversationState.TERMINADA,
                    actions=[],
                    metadata={'user_goodbye': True, 'should_end_call': True}
                )

            # Obtener historial de conversación
            conversation_history = self.conversation_history.get(call_id, [])
            
            # Agregar mensaje del usuario al historial
            user_message = ConversationMessage(
                role='user',
                content=text,
                timestamp=datetime.now()
            )
            conversation_history.append(user_message)
            
            # Detectar intención del usuario
            intent_result = await self._detect_intent(text, context)
            
            # Actualizar contexto basado en la intención
            await self._update_context(context, intent_result.intent, intent_result.metadata)
            
            # Generar respuesta
            ai_response = await self._generate_response(call_id, context, intent_result, conversation_history, text)
            
            # Ejecutar acciones si las hay
            if ai_response.actions:
                await self._execute_actions(call_id, context, ai_response.actions)
            
            # Actualizar estado si cambió
            if ai_response.next_state != context.estado:
                context.estado = ai_response.next_state
            
            # Agregar respuesta al historial
            conversation_history.append(ConversationMessage(
                role='assistant',
                content=ai_response.text,
                timestamp=datetime.now(),
                intent=intent_result.intent,
                confidence=intent_result.confidence
            ))
            
            return ai_response
            
        except Exception as e:
            self.logger.error(f"Error procesando entrada del usuario: {e}")
            # Respuesta de fallback
            return AIResponse(
                text="Disculpe, tuve un problema técnico. ¿Podría repetir lo que me dijo?",
                intent=IntentType.OTRO,
                confidence=0.5,
                next_state=ConversationState.ERROR,
                actions=[],
                metadata={'error': str(e)}
            )
    
    async def _detect_intent(self, text: str, context: ConversationContext) -> AIResponse:
        """Detecta la intención del usuario usando IA."""
        return await self.intent_processor.detect_intent(text, context)
    
    async def _generate_response(self, call_id: str, context: ConversationContext,
                               intent_result: AIResponse, conversation_history: List[ConversationMessage],
                               original_text: str = "") -> AIResponse:
        """Genera respuesta usando el modelo de IA con validación estricta."""

        # 🚨 VALIDACIÓN CRÍTICA DE SEGURIDAD: Si no hay cliente identificado, NO dar información de deuda
        if not context.cliente_id:
            # Lista de intents que requieren identificación
            intents_que_requieren_identificacion = [
                IntentType.SOLICITUD_INFORMACION,
                IntentType.RECONOCIMIENTO_DEUDA,
                IntentType.NEGACION_DEUDA,
                IntentType.PROPUESTA_PAGO,
                IntentType.COMPROMISO_PAGO,
                IntentType.SOLICITUD_DESCUENTO
            ]

            # Si pide información de deuda sin estar identificado
            if (intent_result.intent in intents_que_requieren_identificacion or
                "deuda" in intent_result.text.lower() or
                "debe" in intent_result.text.lower() or
                "pagar" in intent_result.text.lower()):

                self.logger.warning(f"🚨 SEGURIDAD: Intento de obtener info de deuda sin identificación - Intent: {intent_result.intent}, Texto: '{intent_result.text}'")
                return AIResponse(
                    text="Para consultar información de deuda necesito verificar su identidad. ¿Podría proporcionarme su número de documento de 8 dígitos?",
                    intent=IntentType.CONFIRMACION_IDENTIDAD,
                    confidence=1.0,
                    next_state=ConversationState.IDENTIFICANDO,
                    actions=[],
                    metadata={'security_block': True}
                )

        # NUEVA FUNCIONALIDAD: Procesar DNI SOLO cuando el contexto lo requiere
        # Definir texto_a_analizar SIEMPRE para evitar errores de variable
        texto_a_analizar = original_text if original_text else intent_result.text

        # SOLO buscar DNI si el cliente NO está identificado Y estamos en estado de identificación
        if not context.cliente_id and context.estado in [ConversationState.INICIANDO, ConversationState.IDENTIFICANDO]:
            self.logger.info(f"🔍 CHECKING_DNI: Verificando si hay DNI en texto: '{texto_a_analizar}' - Estado: {context.estado}")

            dni = self._extract_dni_from_text(texto_a_analizar)
            if dni:
                self.logger.info(f"✅ DNI EXTRAÍDO: {dni} - Procesando validación")
                return await self._process_dni_validation(dni, context)
            else:
                self.logger.info(f"❌ No se encontró DNI válido en el texto: '{texto_a_analizar}'")
        elif context.cliente_id:
            self.logger.info(f"Cliente ya identificado ({context.cliente_id}) - No buscar DNI")
            # Si el cliente ya está identificado y menciona números, puede ser un compromiso o consulta
            if any(palabra in texto_a_analizar.lower() for palabra in ['pagar', 'puedo', 'acepto', 'compromiso']):
                self.logger.info(f"Cliente identificado mencionó compromiso: '{texto_a_analizar}'")
                # Procesar como posible compromiso
                if intent_result.intent in [IntentType.PROPUESTA_PAGO, IntentType.COMPROMISO_PAGO]:
                    await self._process_commitment(context, texto_a_analizar)

            # Si el cliente pide información de deuda, dar respuesta directa
            if any(palabra in texto_a_analizar.lower() for palabra in ['deuda', 'debe', 'detalle', 'información']):
                if context.deuda_total and context.deuda_total > 0:
                    nombre = context.nombre_cliente.split()[0] if context.nombre_cliente else "Cliente"
                    return AIResponse(
                        text=f"{nombre}, usted debe {context.deuda_total:.0f} soles. ¿Puede realizar el pago?",
                        intent=IntentType.SOLICITUD_INFORMACION,
                        confidence=1.0,
                        next_state=ConversationState.NEGOCIANDO,
                        actions=[],
                        metadata={'cliente_ya_identificado_respuesta_directa': True}
                    )
        else:
            self.logger.info(f"Estado {context.estado} - No buscar DNI automáticamente")

        # NUEVA FUNCIONALIDAD: Detectar y guardar compromisos
        if context.cliente_id and intent_result.intent == IntentType.COMPROMISO_PAGO:
            await self._process_commitment(context, texto_a_analizar)

        return await self.intent_processor.generate_response(
            call_id, context, intent_result, conversation_history
        )
    
    async def _update_context(self, context: ConversationContext, intent: IntentType, 
                            intent_metadata: dict) -> None:
        """Actualiza el contexto basado en la intención detectada."""
        await self.action_handler.update_context_by_intent(
            context, intent, intent_metadata, self.stats
        )
    
    async def _execute_actions(self, call_id: str, context: ConversationContext, actions: List[str]) -> None:
        """Ejecuta acciones específicas basadas en la respuesta de IA."""
        await self.action_handler.execute_actions(call_id, context, actions)

    def _extract_dni_from_text(self, text: str) -> Optional[str]:
        """Extrae DNI del texto del usuario."""
        import re

        self.logger.info(f"🔍 EXTRACT_DNI: Analizando texto: '{text}'")

        # Convertir números en palabras a dígitos
        text_normalizado = self._convert_words_to_numbers(text)
        self.logger.info(f"EXTRACT_DNI: Texto normalizado: '{text_normalizado}'")

        # MÉTODO 1: Buscar secuencias de 8 dígitos juntos
        dni_pattern = r'\b\d{8}\b'
        matches = re.findall(dni_pattern, text_normalizado)

        if matches:
            self.logger.info(f"✅ EXTRACT_DNI: Encontrado DNI completo: {matches[0]}")
            return matches[0]

        # MÉTODO 2: Buscar números con espacios o guiones (12 345 678)
        dni_pattern_spaced = r'\b\d{2}[\s\-]?\d{3}[\s\-]?\d{3}\b'
        matches = re.findall(dni_pattern_spaced, text_normalizado)

        if matches:
            dni = ''.join(filter(str.isdigit, matches[0]))
            self.logger.info(f"✅ EXTRACT_DNI: Encontrado DNI con espacios: {matches[0]} -> {dni}")
            return dni

        # MÉTODO 3: Buscar dígitos individuales separados por espacios (1 2 3 4 5 6 7 8)
        individual_digits = re.findall(r'\b\d\b', text_normalizado)
        self.logger.info(f"EXTRACT_DNI: Dígitos individuales encontrados: {individual_digits}")

        if len(individual_digits) == 8:
            dni = ''.join(individual_digits)
            self.logger.info(f"✅ EXTRACT_DNI: DNI formado de dígitos individuales: {dni}")
            return dni

        # MÉTODO 4: Buscar cualquier secuencia de EXACTAMENTE 8 números (incluso con espacios)
        all_digits = re.findall(r'\d', text_normalizado)
        if len(all_digits) == 8:
            dni = ''.join(all_digits)
            self.logger.info(f"✅ EXTRACT_DNI: DNI formado de todos los dígitos: {dni}")
            return dni
        elif len(all_digits) > 0:
            self.logger.warning(f"❌ EXTRACT_DNI: Encontrados {len(all_digits)} dígitos, se requieren exactamente 8 para DNI")

        self.logger.warning(f"❌ EXTRACT_DNI: No se pudo extraer DNI válido de '{text}' - Se requieren exactamente 8 dígitos")
        return None

    def _convert_words_to_numbers(self, text: str) -> str:
        """Convierte números en palabras a dígitos."""
        word_to_num = {
            'cero': '0', 'uno': '1', 'dos': '2', 'tres': '3', 'cuatro': '4',
            'cinco': '5', 'seis': '6', 'siete': '7', 'ocho': '8', 'nueve': '9'
        }

        text_lower = text.lower()
        for word, digit in word_to_num.items():
            text_lower = text_lower.replace(word, digit)

        return text_lower

    async def _process_dni_validation(self, dni: str, context: ConversationContext) -> AIResponse:
        """Procesa la validación del DNI y consulta la deuda."""
        try:
            self.logger.info(f"🔍 PROCESANDO DNI: {dni} - Iniciando consulta en BD")

            # Consultar información del cliente y deudas
            info = await self.debt_service.validar_dni_y_obtener_info(dni)

            self.logger.info(f"📊 RESULTADO CONSULTA DNI {dni}: encontrado={info['encontrado']}")

            if info['encontrado']:
                self.logger.info(f"✅ CLIENTE ENCONTRADO: {info['cliente']['nombre']} - Deuda total: {info['resumen']['deuda_total']}")
            else:
                self.logger.warning(f"❌ CLIENTE NO ENCONTRADO para DNI: {dni}")

            if not info['encontrado']:
                return AIResponse(
                    text=f"No encontré ningún cliente con DNI {dni} en nuestro sistema. ¿Podría verificar el número?",
                    intent=IntentType.CONFIRMACION_IDENTIDAD,
                    confidence=1.0,
                    next_state=ConversationState.IDENTIFICANDO,
                    actions=[],
                    metadata={'dni_invalido': dni}
                )

            # Actualizar contexto con información del cliente
            cliente = info['cliente']
            context.cliente_id = cliente['id']
            context.nombre_cliente = cliente['nombre']
            context.deuda_total = info['resumen']['deuda_total']
            context.deudas_activas = info['deudas']

            # Formatear respuesta con información de deuda
            respuesta_texto = self.debt_service.formatear_resumen_deuda(info)

            return AIResponse(
                text=respuesta_texto,
                intent=IntentType.SOLICITUD_INFORMACION,
                confidence=1.0,
                next_state=ConversationState.NEGOCIANDO,
                actions=[],
                metadata={
                    'cliente_identificado': True,
                    'dni': dni,
                    'cliente_info': cliente,
                    'resumen_deuda': info['resumen']
                }
            )

        except Exception as e:
            self.logger.error(f"Error procesando DNI {dni}: {e}")
            return AIResponse(
                text="Disculpe, hubo un error al consultar su información. ¿Podría repetir su DNI?",
                intent=IntentType.CONFIRMACION_IDENTIDAD,
                confidence=0.5,
                next_state=ConversationState.ERROR,
                actions=[],
                metadata={'error': str(e)}
            )

    async def _process_commitment(self, context: ConversationContext, texto_compromiso: str) -> None:
        """Procesa y guarda un compromiso de pago."""
        try:
            self.logger.info(f"PROCESANDO COMPROMISO (Cliente {context.cliente_id}): '{texto_compromiso}'")

            resultado = await self.commitment_service.procesar_compromiso_desde_conversacion(
                context.cliente_id, texto_compromiso
            )

            if resultado['success']:
                self.logger.info(f"COMPROMISO GUARDADO: ID {resultado['compromiso_id']}, Monto {resultado['monto_acordado']}, Fecha {resultado['fecha_compromiso']}")

                # 🎉 MARCAR PARA FINALIZAR LLAMADA DESPUÉS DE RESPUESTA
                context.should_end_call = True
                context.commitment_data = resultado
                context.estado = ConversationState.COMPROMETIENDO

                # Actualizar estadísticas
                self.stats['payment_commitments'] += 1

                # Agregar nota al contexto
                context.notas_conversacion.append(
                    f"Compromiso registrado: {resultado['monto_acordado']} soles para {resultado['fecha_compromiso']}"
                )

                self.logger.info(f"🔔 COMPROMISO EXITOSO - Llamada se finalizará después de despedida")
            else:
                self.logger.warning(f"ERROR GUARDANDO COMPROMISO: {resultado['error']}")

        except Exception as e:
            self.logger.error(f"Error procesando compromiso: {e}")
    
    async def end_conversation(self, call_id: str, reason: str = "normal") -> dict:
        """Finaliza una conversación y genera resumen."""
        try:
            context = self.active_contexts.get(call_id)
            if not context:
                return {'error': 'Conversación no encontrada'}
            
            # Calcular duración
            duration = datetime.now() - context.inicio_conversacion
            
            # Generar resumen
            summary = await self._generate_conversation_summary(call_id, context)
            
            # Guardar log de conversación
            await self._save_conversation_log(call_id, context, summary, reason)
            
            # Actualizar estadísticas
            self._update_call_stats(duration.total_seconds())
            
            # Limpiar contexto
            del self.active_contexts[call_id]
            del self.conversation_history[call_id]
            
            self.logger.info(f"Conversación {call_id} finalizada: {reason}")
            
            return {
                'call_id': call_id,
                'duration': duration.total_seconds(),
                'summary': summary,
                'reason': reason
            }
            
        except Exception as e:
            self.logger.error(f"Error finalizando conversación: {e}")
            return {'error': str(e)}
    
    async def _generate_conversation_summary(self, call_id: str, context: ConversationContext) -> str:
        """Genera un resumen de la conversación."""
        try:
            conversation_history = self.conversation_history.get(call_id, [])
            
            # Construir historial para el resumen
            messages = []
            for msg in conversation_history[-10:]:  # Últimos 10 mensajes
                if msg.role in ['user', 'assistant']:
                    messages.append(f"{msg.role}: {msg.content}")
            
            conversation_text = "\n".join(messages)
            
            # Formatear deuda de forma segura
            deuda_str = f"${context.deuda_total:,.2f}" if context.deuda_total is not None else "No disponible"
            
            summary_prompt = f"""
            Genera un resumen conciso de esta conversación de cobranza:
            
            Cliente: {context.nombre_cliente or 'No identificado'}
            Teléfono: {context.telefono}
            Deuda: {deuda_str}
            Estado final: {context.estado.value}
            
            Conversación:
            {conversation_text}
            
            Resumen (máximo 200 palabras):
            """
            
            response = await self.client.chat.completions.create(
                model=self.config.model,
                messages=[{"role": "user", "content": summary_prompt}],
                max_tokens=300,
                temperature=0.3
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"Error generando resumen: {e}")
            return f"Error generando resumen: {str(e)}"
    
    async def _save_conversation_log(self, call_id: str, context: ConversationContext, 
                                   summary: str, reason: str) -> None:
        """Guarda el log de la conversación en la base de datos."""
        try:
            conversation_data = {
                'call_id': call_id,
                'telefono': context.telefono,
                'cliente_id': context.cliente_id,
                'estado_final': context.estado.value,
                'duracion': (datetime.now() - context.inicio_conversacion).total_seconds(),
                'resumen': summary,
                'razon_finalizacion': reason,
                'notas': '\n'.join(context.notas_conversacion),
                'fecha': datetime.now()
            }
            
            # Usar el método log_call que existe en DatabaseManager
            duracion_segundos = int((datetime.now() - context.inicio_conversacion).total_seconds())
            
            # Mapear estado de conversación a resultado de llamada válido
            resultado_llamada = "error_tecnico"  # valor por defecto
            if context.estado == ConversationState.COMPROMETIENDO:
                resultado_llamada = "compromiso_obtenido"
            elif context.estado == ConversationState.TERMINADA:
                resultado_llamada = "llamada_cortada"
            elif reason == "cleanup":
                resultado_llamada = "llamada_cortada"
            
            await self.db_manager.log_call(
                telefono=context.telefono,
                duracion=duracion_segundos,
                estado=resultado_llamada,
                notas=f"Resumen: {summary}. Razón: {reason}. Notas: {'; '.join(context.notas_conversacion)}"
            )
            
        except Exception as e:
            self.logger.error(f"Error guardando log de conversación: {e}")
    
    def _update_call_stats(self, duration: float) -> None:
        """Actualiza las estadísticas de llamadas."""
        try:
            # Calcular promedio de duración
            total_calls = self.stats['total_calls']
            current_avg = self.stats['average_call_duration']
            
            new_avg = ((current_avg * (total_calls - 1)) + duration) / total_calls
            self.stats['average_call_duration'] = new_avg
            
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas: {e}")
    
    def get_conversation_context(self, call_id: str) -> Optional[ConversationContext]:
        """Obtiene el contexto de una conversación activa."""
        return self.active_contexts.get(call_id)

    def _user_says_goodbye(self, text: str) -> bool:
        """Detecta si el usuario está diciendo adiós."""
        goodbye_phrases = [
            'adiós', 'adios', 'chau', 'chao', 'hasta luego', 'hasta la vista',
            'nos vemos', 'me voy', 'cuelgo', 'corto', 'termino', 'fin',
            'ya no', 'no más', 'basta', 'suficiente', 'no quiero más',
            'no me llamen', 'no molesten', 'déjenme', 'dejenme',
            'corta la llamada', 'termina la llamada', 'finaliza la llamada',
            'no quiero hablar', 'no puedo hablar', 'estoy ocupado'
        ]

        text_lower = text.lower().strip()

        # Detectar frases de despedida
        for phrase in goodbye_phrases:
            if phrase in text_lower:
                self.logger.info(f"👋 DESPEDIDA DETECTADA DEL USUARIO: '{phrase}' en '{text}'")
                return True

        # Detectar patrones específicos
        patterns = [
            # "no no no adiós"
            (lambda t: 'no' in t and any(bye in t for bye in ['adiós', 'adios', 'chau'])),
            # "no termina/corta/finaliza"
            (lambda t: t.startswith('no') and any(word in t for word in ['termina', 'corta', 'finaliza'])),
            # Múltiples "no" seguidos de despedida
            (lambda t: t.count('no') >= 2 and any(bye in t for bye in ['adiós', 'adios', 'chau', 'fin'])),
        ]

        for pattern in patterns:
            if pattern(text_lower):
                self.logger.info(f"👋 PATRÓN DE DESPEDIDA DETECTADO: '{text}'")
                return True

        return False
    
    def get_stats(self) -> dict:
        """Obtiene las estadísticas del motor de conversación."""
        return self.stats.copy()
    
    async def get_greeting(self) -> str:
        """Obtiene el saludo inicial para la llamada."""
        return "Hola, soy qubits, asistente de cobranza de banco qubos. ¿Me brindas tu numero de DNI para poder ayudarte?"
    
    def has_response(self) -> bool:
        """Verifica si hay una respuesta pendiente."""
        # Por ahora siempre retorna False, se puede implementar lógica más compleja
        return False
    
    async def cleanup(self) -> None:
        """Limpia recursos y finaliza conversaciones pendientes."""
        try:
            # Finalizar conversaciones activas
            for call_id in list(self.active_contexts.keys()):
                await self.end_conversation(call_id, "cleanup")
            
            # Cerrar cliente de OpenAI
            await self.client.close()
            
            self.logger.info("Limpieza del motor de conversación completada")
            
        except Exception as e:
            self.logger.error(f"Error en limpieza: {e}")


# Función de prueba
async def test_ai_conversation():
    """Función de prueba para el motor de conversación."""
    from config import load_config
    
    try:
        config = load_config()
        db_manager = DatabaseManager()
        
        engine = AIConversationEngine(config.openai, db_manager)
        await engine.initialize()
        
        # Simular conversación
        call_id = "test_001"
        telefono = "+1234567890"
        
        context = await engine.start_conversation(call_id, telefono)
        print(f"Conversación iniciada: {context.call_id}")
        
        response = await engine.process_user_input(call_id, "Hola, buenos días")
        print(f"Respuesta: {response.text}")
        
        await engine.end_conversation(call_id, "test")
        await engine.cleanup()
        
        print("Prueba completada exitosamente")
        
    except Exception as e:
        print(f"Error en prueba: {e}")


if __name__ == "__main__":
    asyncio.run(test_ai_conversation())