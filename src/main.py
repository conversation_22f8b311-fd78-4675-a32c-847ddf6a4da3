#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - <PERSON><PERSON><PERSON><PERSON> Principal
========================================

Este módulo principal integra todos los componentes del sistema:
- Servidor AudioSocket para comunicación con Asterisk
- ASR Handler para reconocimiento de voz (Deepgram)
- TTS Handler para síntesis de voz (ElevenLabs)
- Motor de conversación con IA (OpenAI)
- Gestión de base de datos

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import logging
import signal
import sys
from typing import Optional, Dict, Any
from datetime import datetime

# Importar componentes del sistema
from config import Config
from database import DatabaseManager
from audiosocket_server import AudioSocketServer
from asr_handler import ASRHandler
from google_tts_handler import GoogleTTSHandler
from ai_conversation import AIConversationEngine


class CobranzaBotSystem:
    """Sistema principal del bot de cobranza con IA."""
    
    def __init__(self):
        # Configuración
        self.config = Config()
        self.logger = self._setup_logging()
        
        # Componentes del sistema
        self.db_manager: Optional[DatabaseManager] = None
        self.audiosocket_server: Optional[AudioSocketServer] = None
        self.asr_handler: Optional[ASRHandler] = None
        self.tts_handler: Optional[TTSHandler] = None
        self.ai_engine: Optional[AIConversationEngine] = None
        
        # Estado del sistema
        self.is_running = False
        self.startup_time: Optional[datetime] = None
        
        # Estadísticas globales
        self.system_stats = {
            'calls_processed': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'total_uptime_seconds': 0,
            'last_restart': None
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Configura el sistema de logging."""
        # Configurar formato de logging
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Configurar nivel de logging
        log_level = getattr(logging, self.config.logging.level.upper())
        
        # Configurar logging básico
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(self.config.logging.file)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("Sistema de logging configurado")
        
        return logger
    
    async def initialize(self) -> None:
        """Inicializa todos los componentes del sistema."""
        try:
            self.logger.info("Iniciando sistema de cobranza con IA...")
            
            # Validar configuración
            if not self.config.validate():
                raise ValueError("Configuración del sistema inválida")
            
            # Inicializar base de datos
            self.logger.info("Inicializando base de datos...")
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            
            # Inicializar handlers de audio
            self.logger.info("Inicializando ASR Handler...")
            self.asr_handler = ASRHandler(self.config.deepgram)
            await self.asr_handler.initialize()
            
            self.logger.info("Inicializando TTS Handler...")
            self.tts_handler = GoogleTTSHandler(self.config.google_tts)
            await self.tts_handler.initialize()
            
            # Inicializar motor de IA
            self.logger.info("Inicializando motor de conversación IA...")
            self.ai_engine = AIConversationEngine(self.config.openai, self.db_manager)
            await self.ai_engine.initialize()
            
            # Inicializar servidor AudioSocket
            self.logger.info("Inicializando servidor AudioSocket...")
            self.audiosocket_server = AudioSocketServer(self.config, self.db_manager)
            
            # Configurar callbacks entre componentes
            await self._setup_component_callbacks()
            
            self.startup_time = datetime.now()
            self.system_stats['last_restart'] = self.startup_time.isoformat()
            
            self.logger.info("Sistema inicializado correctamente")
            
        except Exception as e:
            self.logger.error(f"Error inicializando sistema: {e}")
            await self.cleanup()
            raise
    
    async def _setup_component_callbacks(self) -> None:
        """Configura callbacks entre componentes para integración."""
        try:
            # Callback ASR -> AI Engine
            async def on_transcription(call_id: str, text: str, is_final: bool):
                if is_final and self.ai_engine:
                    try:
                        response = await self.ai_engine.process_user_input(call_id, text)
                        # Enviar respuesta a TTS
                        if self.audiosocket_server:
                            await self.audiosocket_server.send_ai_response(call_id, response.text)
                    except Exception as e:
                        self.logger.error(f"Error procesando transcripción: {e}")
            
            if self.asr_handler:
                self.asr_handler.set_transcription_callback(on_transcription)
            
            # Callback AudioSocket -> Sistema (estadísticas)
            async def on_call_event(event_type: str, call_id: str, data: Dict[str, Any]):
                try:
                    if event_type == "call_started":
                        self.system_stats['calls_processed'] += 1
                        self.logger.info(f"Nueva llamada iniciada: {call_id}")
                    
                    elif event_type == "call_ended":
                        success = data.get('success', False)
                        if success:
                            self.system_stats['successful_calls'] += 1
                        else:
                            self.system_stats['failed_calls'] += 1
                        
                        self.logger.info(f"Llamada finalizada: {call_id} (éxito: {success})")
                    
                except Exception as e:
                    self.logger.error(f"Error procesando evento de llamada: {e}")
            
            if self.audiosocket_server:
                self.audiosocket_server.set_call_event_callback(on_call_event)
            
            self.logger.info("Callbacks entre componentes configurados")
            
        except Exception as e:
            self.logger.error(f"Error configurando callbacks: {e}")
    
    async def start(self) -> None:
        """Inicia el sistema completo."""
        try:
            if not self.audiosocket_server:
                raise ValueError("Sistema no inicializado")
            
            self.logger.info("Iniciando servidor AudioSocket...")
            await self.audiosocket_server.start()
            
            self.is_running = True
            self.logger.info(f"Sistema de cobranza iniciado en puerto {self.config.audiosocket.port}")
            
            # Mostrar información del sistema
            await self._log_system_info()
            
        except Exception as e:
            self.logger.error(f"Error iniciando sistema: {e}")
            raise
    
    async def _log_system_info(self) -> None:
        """Registra información del sistema al iniciar."""
        try:
            info = [
                "=" * 60,
                "SISTEMA DE COBRANZA CON IA - INICIADO",
                "=" * 60,
                f"Tiempo de inicio: {self.startup_time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"Puerto AudioSocket: {self.config.audiosocket.port}",
                f"Modelo ASR: {self.config.deepgram.model}",
                f"Modelo TTS: {self.config.elevenlabs.model_id}",
                f"Modelo IA: {self.config.openai.model}",
                f"Base de datos: {self.config.database.host}:{self.config.database.port}",
                "=" * 60
            ]
            
            for line in info:
                self.logger.info(line)
                
        except Exception as e:
            self.logger.error(f"Error mostrando info del sistema: {e}")
    
    async def stop(self) -> None:
        """Detiene el sistema de manera ordenada."""
        try:
            self.logger.info("Deteniendo sistema de cobranza...")
            
            self.is_running = False
            
            # Detener servidor AudioSocket
            if self.audiosocket_server:
                await self.audiosocket_server.stop()
            
            # Limpiar componentes
            await self.cleanup()
            
            self.logger.info("Sistema detenido correctamente")
            
        except Exception as e:
            self.logger.error(f"Error deteniendo sistema: {e}")
    
    async def cleanup(self) -> None:
        """Limpia recursos del sistema."""
        try:
            self.logger.info("Limpiando recursos del sistema...")
            
            # Limpiar componentes en orden inverso
            if self.ai_engine:
                await self.ai_engine.cleanup()
                self.ai_engine = None
            
            if self.tts_handler:
                await self.tts_handler.cleanup()
                self.tts_handler = None
            
            if self.asr_handler:
                await self.asr_handler.cleanup()
                self.asr_handler = None
            
            if self.audiosocket_server:
                await self.audiosocket_server.cleanup()
                self.audiosocket_server = None
            
            if self.db_manager:
                await self.db_manager.close()
                self.db_manager = None
            
            self.logger.info("Recursos limpiados")
            
        except Exception as e:
            self.logger.error(f"Error limpiando recursos: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Obtiene el estado actual del sistema."""
        try:
            uptime = 0
            if self.startup_time:
                uptime = (datetime.now() - self.startup_time).total_seconds()
            
            status = {
                'is_running': self.is_running,
                'startup_time': self.startup_time.isoformat() if self.startup_time else None,
                'uptime_seconds': uptime,
                'components': {
                    'database': self.db_manager is not None,
                    'audiosocket_server': self.audiosocket_server is not None,
                    'asr_handler': self.asr_handler is not None,
                    'tts_handler': self.tts_handler is not None,
                    'ai_engine': self.ai_engine is not None
                },
                'stats': self.system_stats
            }
            
            # Agregar estadísticas de componentes
            if self.audiosocket_server:
                status['audiosocket_stats'] = self.audiosocket_server.get_stats()
            
            if self.asr_handler:
                status['asr_stats'] = self.asr_handler.get_stats()
            
            if self.tts_handler:
                status['tts_stats'] = self.tts_handler.get_stats()
            
            if self.ai_engine:
                status['ai_stats'] = self.ai_engine.get_stats()
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error obteniendo estado del sistema: {e}")
            return {'error': str(e)}
    
    async def restart_component(self, component_name: str) -> bool:
        """Reinicia un componente específico del sistema."""
        try:
            self.logger.info(f"Reiniciando componente: {component_name}")
            
            if component_name == "asr":
                if self.asr_handler:
                    await self.asr_handler.cleanup()
                self.asr_handler = ASRHandler(self.config.deepgram)
                await self.asr_handler.initialize()
            
            elif component_name == "tts":
                if self.tts_handler:
                    await self.tts_handler.cleanup()
                self.tts_handler = GoogleTTSHandler(self.config.google_tts)
                await self.tts_handler.initialize()
            
            elif component_name == "ai":
                if self.ai_engine:
                    await self.ai_engine.cleanup()
                self.ai_engine = AIConversationEngine(self.config.openai, self.db_manager)
                await self.ai_engine.initialize()
            
            elif component_name == "database":
                if self.db_manager:
                    await self.db_manager.close()
                self.db_manager = DatabaseManager()
                await self.db_manager.initialize()
            
            else:
                self.logger.warning(f"Componente desconocido: {component_name}")
                return False
            
            # Reconfigurar callbacks
            await self._setup_component_callbacks()
            
            self.logger.info(f"Componente {component_name} reiniciado correctamente")
            return True
            
        except Exception as e:
            self.logger.error(f"Error reiniciando componente {component_name}: {e}")
            return False


# Función principal
async def main():
    """Función principal del sistema."""
    system = None
    
    try:
        # Crear sistema
        system = CobranzaBotSystem()
        
        # Configurar manejo de señales
        def signal_handler(signum, frame):
            print(f"\nSeñal {signum} recibida. Deteniendo sistema...")
            if system:
                asyncio.create_task(system.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Inicializar y iniciar sistema
        await system.initialize()
        await system.start()
        
        # Mantener sistema ejecutándose
        while system.is_running:
            await asyncio.sleep(1)
            
            # Actualizar estadísticas de uptime
            if system.startup_time:
                system.system_stats['total_uptime_seconds'] = (
                    datetime.now() - system.startup_time
                ).total_seconds()
    
    except KeyboardInterrupt:
        print("\nInterrupción de teclado recibida")
    
    except Exception as e:
        print(f"Error en sistema principal: {e}")
        if system and system.logger:
            system.logger.error(f"Error crítico en sistema principal: {e}")
    
    finally:
        if system:
            await system.stop()


# Función de utilidad para testing
async def test_system():
    """Función de prueba para el sistema completo."""
    system = CobranzaBotSystem()
    
    try:
        print("Iniciando prueba del sistema...")
        
        await system.initialize()
        print("Sistema inicializado")
        
        status = await system.get_system_status()
        print(f"Estado del sistema: {status}")
        
        # Simular funcionamiento por unos segundos
        await asyncio.sleep(5)
        
    except Exception as e:
        print(f"Error en prueba: {e}")
    
    finally:
        await system.cleanup()
        print("Prueba completada")


if __name__ == "__main__":
    # Verificar argumentos de línea de comandos
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        asyncio.run(test_system())
    else:
        asyncio.run(main())