#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Calculador optimizado de fechas naturales para compromisos de pago.
Sistema ultra-rápido con mapeo pre-calculado de frases comunes.
"""

import re
import logging
from datetime import datetime, timedelta, date
from typing import Optional, Dict, List, Tuple
from functools import lru_cache


class DateCalculator:
    """Calculador optimizado de fechas naturales con cache y mapeo pre-establecido."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Cache de fechas calculadas para máxima velocidad
        self._date_cache: Dict[str, date] = {}
        
        # Mapeo pre-establecido de frases comunes (más rápido que regex)
        self._common_phrases = self._build_common_phrases_map()
        
        # Patrones de días de la semana
        self._weekdays = {
            'lunes': 0, 'martes': 1, 'miércoles': 2, 'miercoles': 2,
            'jueves': 3, 'viernes': 4, 'sábado': 5, 'sabado': 5, 'domingo': 6
        }
        
        # Patrones de tiempo relativo
        self._relative_patterns = [
            (r'\b(?:hoy|ahorita)\b', 0),
            (r'\b(?:mañana|manana)\b', 1),
            (r'\b(?:pasado mañana|pasado manana)\b', 2),
            (r'\b(?:en (?:dos|2) días?|en (?:dos|2) dias?)\b', 2),
            (r'\b(?:en (?:tres|3) días?|en (?:tres|3) dias?)\b', 3),
            (r'\b(?:en una semana|la próxima semana|la proxima semana)\b', 7),
            (r'\b(?:en (?:dos|2) semanas)\b', 14),
            (r'\b(?:el próximo mes|el proximo mes)\b', 30),
        ]
    
    def _build_common_phrases_map(self) -> Dict[str, int]:
        """Construye mapeo de frases comunes a días de diferencia."""
        today = datetime.now().date()
        
        return {
            # Frases exactas más comunes (ultra-rápido)
            'hoy': 0,
            'ahorita': 0,
            'en el día': 0,
            'hoy mismo': 0,
            'mañana': 1,
            'manana': 1,
            'pasado mañana': 2,
            'pasado manana': 2,
            'en dos días': 2,
            'en dos dias': 2,
            'en tres días': 3,
            'en tres dias': 3,
            'en una semana': 7,
            'la próxima semana': 7,
            'la proxima semana': 7,
            'en dos semanas': 14,
            'el próximo mes': 30,
            'el proximo mes': 30,
        }
    
    @lru_cache(maxsize=1000)
    def parse_natural_date(self, text: str) -> Optional[date]:
        """
        Parsea fecha natural del texto con máxima velocidad.
        Usa cache LRU para frases repetidas.
        """
        if not text:
            return None
        
        text_lower = text.lower().strip()
        
        # 1. CACHE CHECK - Ultra rápido para frases repetidas
        if text_lower in self._date_cache:
            self.logger.debug(f"CACHE HIT para '{text_lower}'")
            return self._date_cache[text_lower]
        
        today = datetime.now().date()
        calculated_date = None
        
        try:
            # 2. MAPEO DIRECTO - Frases comunes pre-establecidas (más rápido)
            # Ordenar por longitud descendente para evaluar frases más específicas primero
            sorted_phrases = sorted(self._common_phrases.items(), key=lambda x: len(x[0]), reverse=True)
            for phrase, days_offset in sorted_phrases:
                if phrase in text_lower:
                    calculated_date = today + timedelta(days=days_offset)
                    self.logger.info(f"MAPEO DIRECTO: '{phrase}' -> {calculated_date}")
                    break
            
            # 3. DÍAS DE LA SEMANA - Próximo día específico
            if not calculated_date:
                calculated_date = self._parse_weekday(text_lower, today)
            
            # 4. PATRONES RELATIVOS - Regex para casos complejos
            if not calculated_date:
                calculated_date = self._parse_relative_patterns(text_lower, today)
            
            # 5. FECHAS ESPECÍFICAS - dd/mm, dd de mes, etc.
            if not calculated_date:
                calculated_date = self._parse_specific_dates(text_lower, today)
            
            # Guardar en cache para próximas consultas
            if calculated_date:
                self._date_cache[text_lower] = calculated_date
                self.logger.info(f"FECHA CALCULADA: '{text}' -> {calculated_date}")
            else:
                self.logger.warning(f"NO SE PUDO CALCULAR FECHA: '{text}'")
            
            return calculated_date
            
        except Exception as e:
            self.logger.error(f"Error calculando fecha para '{text}': {e}")
            return None
    
    def _parse_weekday(self, text: str, today: date) -> Optional[date]:
        """Parsea días de la semana (lunes, martes, etc.)."""
        for day_name, weekday_num in self._weekdays.items():
            if day_name in text:
                # Calcular próximo día de la semana
                days_ahead = weekday_num - today.weekday()
                
                # Si es hoy o ya pasó esta semana, ir a la próxima
                if days_ahead <= 0:
                    days_ahead += 7
                
                # Manejar "este" vs "próximo"
                if 'próximo' in text or 'proximo' in text or 'que viene' in text:
                    if days_ahead <= 7:  # Si es esta semana, ir a la próxima
                        days_ahead += 7
                
                calculated_date = today + timedelta(days=days_ahead)
                self.logger.info(f"DÍA SEMANA: '{day_name}' -> {calculated_date}")
                return calculated_date
        
        return None
    
    def _parse_relative_patterns(self, text: str, today: date) -> Optional[date]:
        """Parsea patrones relativos usando regex."""
        for pattern, days_offset in self._relative_patterns:
            if re.search(pattern, text):
                calculated_date = today + timedelta(days=days_offset)
                self.logger.info(f"PATRÓN RELATIVO: '{pattern}' -> {calculated_date}")
                return calculated_date
        
        return None
    
    def _parse_specific_dates(self, text: str, today: date) -> Optional[date]:
        """Parsea fechas específicas (dd/mm, dd de mes, etc.)."""
        # Patrón dd/mm o dd-mm
        date_pattern = r'\b(\d{1,2})[/-](\d{1,2})\b'
        match = re.search(date_pattern, text)
        if match:
            day, month = int(match.group(1)), int(match.group(2))
            try:
                # Asumir año actual, si ya pasó usar próximo año
                year = today.year
                calculated_date = date(year, month, day)
                if calculated_date <= today:
                    calculated_date = date(year + 1, month, day)
                
                self.logger.info(f"FECHA ESPECÍFICA: {day}/{month} -> {calculated_date}")
                return calculated_date
            except ValueError:
                pass
        
        # Patrón "dd de mes"
        months_es = {
            'enero': 1, 'febrero': 2, 'marzo': 3, 'abril': 4,
            'mayo': 5, 'junio': 6, 'julio': 7, 'agosto': 8,
            'septiembre': 9, 'octubre': 10, 'noviembre': 11, 'diciembre': 12
        }
        
        for month_name, month_num in months_es.items():
            pattern = rf'\b(\d{{1,2}})\s+de\s+{month_name}\b'
            match = re.search(pattern, text)
            if match:
                day = int(match.group(1))
                try:
                    year = today.year
                    calculated_date = date(year, month_num, day)
                    if calculated_date <= today:
                        calculated_date = date(year + 1, month_num, day)
                    
                    self.logger.info(f"FECHA MES: {day} de {month_name} -> {calculated_date}")
                    return calculated_date
                except ValueError:
                    pass
        
        return None
    
    def clear_cache(self):
        """Limpia el cache de fechas."""
        self._date_cache.clear()
        self.parse_natural_date.cache_clear()
        self.logger.info("Cache de fechas limpiado")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Obtiene estadísticas del cache."""
        cache_info = self.parse_natural_date.cache_info()
        return {
            'cache_size': len(self._date_cache),
            'lru_hits': cache_info.hits,
            'lru_misses': cache_info.misses,
            'lru_maxsize': cache_info.maxsize,
            'lru_currsize': cache_info.currsize
        }


# Función de utilidad para testing rápido
def test_date_calculator():
    """Prueba rápida del calculador de fechas."""
    calculator = DateCalculator()
    
    test_phrases = [
        "mañana",
        "pasado mañana", 
        "el próximo lunes",
        "este jueves",
        "en una semana",
        "15 de marzo",
        "hoy",
        "el viernes que viene"
    ]
    
    print("=== PRUEBA DATE CALCULATOR ===")
    for phrase in test_phrases:
        result = calculator.parse_natural_date(phrase)
        print(f"'{phrase}' -> {result}")
    
    print(f"\nEstadísticas cache: {calculator.get_cache_stats()}")


if __name__ == "__main__":
    test_date_calculator()
