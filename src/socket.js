function a412_0x2dfb(_0x5b53c3,_0x4eb21e){const _0x214693=a412_0x2146();return a412_0x2dfb=function(_0x2dfbfc,_0xb7a65){_0x2dfbfc=_0x2dfbfc-0x1a8;let _0x557982=_0x214693[_0x2dfbfc];return _0x557982;},a412_0x2dfb(_0x5b53c3,_0x4eb21e);}const a412_0x5b56f0=a412_0x2dfb;(function(_0x58f2f1,_0x242077){const _0x8ab433=a412_0x2dfb,_0x1bbaf3=_0x58f2f1();while(!![]){try{const _0xf00a9d=-parseInt(_0x8ab433(0x1bf))/0x1+parseInt(_0x8ab433(0x1b2))/0x2+parseInt(_0x8ab433(0x1fe))/0x3+-parseInt(_0x8ab433(0x1b4))/0x4+parseInt(_0x8ab433(0x1e6))/0x5*(-parseInt(_0x8ab433(0x1ad))/0x6)+-parseInt(_0x8ab433(0x20a))/0x7*(-parseInt(_0x8ab433(0x1d2))/0x8)+parseInt(_0x8ab433(0x20b))/0x9;if(_0xf00a9d===_0x242077)break;else _0x1bbaf3['push'](_0x1bbaf3['shift']());}catch(_0xb93db9){_0x1bbaf3['push'](_0x1bbaf3['shift']());}}}(a412_0x2146,0x97701),require(a412_0x5b56f0(0x1f6))[a412_0x5b56f0(0x1e8)]());const {EventEmitter}=require(a412_0x5b56f0(0x1e2)),{toUUID}=require(a412_0x5b56f0(0x1ee)),{Asr}=require(a412_0x5b56f0(0x1f2)),{Llm}=require(a412_0x5b56f0(0x1c1)),{Tts}=require(a412_0x5b56f0(0x1c5)),{Sts}=require(a412_0x5b56f0(0x1cb)),logger=require(a412_0x5b56f0(0x1e9)),TERMINATE_PACKET=0x0,UUID_PACKET=0x1,AUDIO_PACKET=0x10,ERROR_PACKET=0xff,TERMINATE_PACKET_LENGTH=0x3,MAX_CHUNK_SIZE=0x140;function a412_0x2146(){const _0x38833f=['Error\x20packet\x20received:\x20','disconnect','STS\x20streaming\x20ended','handleEnd','alloc','Sends\x20text\x20from\x20Llm\x20to\x20Tts:\x20','llm','handleTtsAudio','7jUiTTC','6106239jWaCLN','sendAudioPacket','slice','handleError','toString','startAudioProcessing','clearTextQueue','uuid','18ytMOBv','shift','Unknown\x20packet\x20type:\x20','handleData','bind','703364dznDEW','processAudio','2294732WKkUHm','handleAsrEnd','handleLlmError','Sends\x20text\x20from\x20LLM\x20to\x20TTS:\x20','system','sts','errors','exports','emit','writeUInt16BE','Error\x20during\x20streaming\x20to\x20TTS:\x20','917868RbBeQt','audio','./llm','handleLlmText','handleTtsEnd','Error\x20during\x20streaming\x20to\x20STS:\x20','./tts','handleStsAudio','error','handleAsrError','push','sendHangupPacket','./sts','info','handleTranscript','handleTtsError','concat','Socket\x20error:\x20','Sends\x20transcript\x20from\x20ASR\x20to\x20LLM:\x20','639712NRQEGc','messages','handleErrorPacket','Error\x20during\x20streaming\x20to\x20LLM:\x20','ASR\x20streaming\x20ended','handleAudioPacket','TTS\x20streaming\x20ended','Terminate\x20packet\x20received','length','sendToTts','You\x20are\x20','copy','handleStsEnd','handleUuidPacket','env','end','events','handleTerminatePacket','asr','readUInt16BE','77845hvIMUW','setupSocket','config','./logger','text','an\x20AI\x20assistant','writable','setupEvents','to-uuid','Socket\x20Client\x20disconnected','LLM\x20streaming\x20ended','audioQueue','./asr','isSendingAudio','min','true','dotenv','message','hex','handleStsError','readUInt8','user','stopStreaming','tts','3145290lxsQTI','socket','forEach','SYSTEM_MESSAGE'];a412_0x2146=function(){return _0x38833f;};return a412_0x2146();}class ClientHandler extends EventEmitter{constructor(_0x2ebf3b){const _0x3c789f=a412_0x5b56f0;super(),this[_0x3c789f(0x1ff)]=_0x2ebf3b,this[_0x3c789f(0x1ac)]=null,this[_0x3c789f(0x1d3)]=[],this['audioQueue']=[],this[_0x3c789f(0x1f3)]=![],this[_0x3c789f(0x1e4)]=new Asr(),this[_0x3c789f(0x208)]=new Llm(),this[_0x3c789f(0x1fd)]=new Tts(),this[_0x3c789f(0x1b9)]=new Sts(),this['setupSocket'](),this[_0x3c789f(0x1ed)]();}[a412_0x5b56f0(0x1e7)](){const _0x2af9aa=a412_0x5b56f0;this[_0x2af9aa(0x1ff)]['on']('data',this[_0x2af9aa(0x1b0)]['bind'](this)),this[_0x2af9aa(0x1ff)]['on']('end',this[_0x2af9aa(0x205)][_0x2af9aa(0x1b1)](this)),this[_0x2af9aa(0x1ff)]['on']('error',this[_0x2af9aa(0x1a8)][_0x2af9aa(0x1b1)](this));}[a412_0x5b56f0(0x1ed)](){const _0x4a9bdb=a412_0x5b56f0;this['asr']['on']('transcript',this[_0x4a9bdb(0x1cd)][_0x4a9bdb(0x1b1)](this)),this['asr']['on'](_0x4a9bdb(0x1e1),this['handleAsrEnd'][_0x4a9bdb(0x1b1)](this)),this[_0x4a9bdb(0x1e4)]['on'](_0x4a9bdb(0x1c7),this[_0x4a9bdb(0x1c8)]['bind'](this)),this['llm']['on'](_0x4a9bdb(0x1ea),this['handleLlmText'][_0x4a9bdb(0x1b1)](this)),this['llm']['on'](_0x4a9bdb(0x1c0),this['handleLlmAudio'][_0x4a9bdb(0x1b1)](this)),this[_0x4a9bdb(0x208)]['on'](_0x4a9bdb(0x1e1),this['handleLlmEnd']['bind'](this)),this[_0x4a9bdb(0x208)]['on'](_0x4a9bdb(0x1c7),this[_0x4a9bdb(0x1b6)]['bind'](this)),this[_0x4a9bdb(0x1fd)]['on'](_0x4a9bdb(0x1c0),this['handleTtsAudio'][_0x4a9bdb(0x1b1)](this)),this[_0x4a9bdb(0x1fd)]['on'](_0x4a9bdb(0x1e1),this['handleTtsEnd'][_0x4a9bdb(0x1b1)](this)),this[_0x4a9bdb(0x1fd)]['on'](_0x4a9bdb(0x1c7),this['handleTtsError'][_0x4a9bdb(0x1b1)](this)),this[_0x4a9bdb(0x1b9)]['on']('audio',this[_0x4a9bdb(0x1c6)][_0x4a9bdb(0x1b1)](this)),this[_0x4a9bdb(0x1b9)]['on']('end',this['handleStsEnd']['bind'](this)),this['sts']['on']('error',this[_0x4a9bdb(0x1f9)][_0x4a9bdb(0x1b1)](this));}async[a412_0x5b56f0(0x1b0)](_0x3cc510){const _0x8a714c=a412_0x5b56f0,_0x207a6a=_0x3cc510[_0x8a714c(0x1fa)](0x0),_0x67e08c=_0x3cc510[_0x8a714c(0x1e5)](0x1);switch(_0x207a6a){case TERMINATE_PACKET:this['handleTerminatePacket']();break;case UUID_PACKET:this[_0x8a714c(0x1df)](_0x3cc510,_0x67e08c);break;case AUDIO_PACKET:this[_0x8a714c(0x1d7)](_0x3cc510,_0x67e08c);break;case ERROR_PACKET:this[_0x8a714c(0x1d4)](_0x3cc510,_0x67e08c);break;default:logger[_0x8a714c(0x1c7)](_0x8a714c(0x1af)+_0x207a6a);break;}}[a412_0x5b56f0(0x1d4)](_0x4e259c,_0x1380fd){const _0x3ae512=a412_0x5b56f0;logger['error'](_0x3ae512(0x202)+_0x4e259c+',\x20'+_0x1380fd);}async[a412_0x5b56f0(0x1df)](_0x2cb17a,_0x33d766){const _0x5d2a0f=a412_0x5b56f0;this['uuid']=toUUID(_0x2cb17a[_0x5d2a0f(0x20d)](0x3,0x3+_0x33d766)[_0x5d2a0f(0x1a9)](_0x5d2a0f(0x1f8))),logger[_0x5d2a0f(0x1cc)]('UUID\x20packet\x20received:\x20'+this['uuid']),this['startAudioProcessing']();if(process[_0x5d2a0f(0x1e0)][_0x5d2a0f(0x201)])logger['info'](_0x5d2a0f(0x207)+process[_0x5d2a0f(0x1e0)][_0x5d2a0f(0x201)]),this[_0x5d2a0f(0x1c2)](process[_0x5d2a0f(0x1e0)][_0x5d2a0f(0x201)]);else{const _0xf4981d={'role':_0x5d2a0f(0x1b8),'content':_0x5d2a0f(0x1dc)+(process[_0x5d2a0f(0x1e0)]['SYSTEM_NAME']||_0x5d2a0f(0x1eb))+'.\x20You\x20must\x20introduce\x20yourself\x20quickly\x20and\x20concisely.'};this['messages'][_0x5d2a0f(0x1c9)](_0xf4981d);}}[a412_0x5b56f0(0x1d7)](_0x4c1236,_0x5ef373){const _0x5708d8=a412_0x5b56f0,_0x4a8580=_0x4c1236[_0x5708d8(0x20d)](0x3,0x3+_0x5ef373);process[_0x5708d8(0x1e0)]['STS_URL']?this[_0x5708d8(0x1b9)]['processAudio'](_0x4a8580):process[_0x5708d8(0x1e0)]['INTERRUPT_LISTENING']===_0x5708d8(0x1f5)?this[_0x5708d8(0x1f3)]?this[_0x5708d8(0x1e4)][_0x5708d8(0x1fc)]():this[_0x5708d8(0x1e4)][_0x5708d8(0x1b3)](_0x4a8580):this['asr']['processAudio'](_0x4a8580);}[a412_0x5b56f0(0x1e3)](){const _0x17d790=a412_0x5b56f0;logger['info'](_0x17d790(0x1d9)),this[_0x17d790(0x1ca)](),this[_0x17d790(0x1e4)][_0x17d790(0x1fc)](),this[_0x17d790(0x1fd)][_0x17d790(0x1ab)](),this[_0x17d790(0x1b9)]['stopStreaming']();}async[a412_0x5b56f0(0x1aa)](){setImmediate(async()=>{const _0x9e8e91=a412_0x2dfb;while(!this[_0x9e8e91(0x1ff)]['destroyed']){if(this[_0x9e8e91(0x1f1)][_0x9e8e91(0x1da)]>0x0){const _0x2e9404=this[_0x9e8e91(0x1f1)][_0x9e8e91(0x1ae)]();this[_0x9e8e91(0x1f3)]=!![],await this[_0x9e8e91(0x20c)](_0x2e9404);}else this[_0x9e8e91(0x1f3)]=![],await this['sendAudioPacket'](Buffer[_0x9e8e91(0x206)](0x140,0xff));await new Promise(_0x141b8a=>setTimeout(_0x141b8a,0xa));}});}async[a412_0x5b56f0(0x20c)](_0x96ddb3){const _0x2c9af8=a412_0x5b56f0;for(let _0x55779f=0x0;_0x55779f<_0x96ddb3[_0x2c9af8(0x1da)];_0x55779f+=MAX_CHUNK_SIZE){let _0xad78e5=_0x96ddb3[_0x2c9af8(0x20d)](_0x55779f,Math[_0x2c9af8(0x1f4)](_0x55779f+MAX_CHUNK_SIZE,_0x96ddb3[_0x2c9af8(0x1da)]));const _0x3d4042=Buffer[_0x2c9af8(0x206)](MAX_CHUNK_SIZE-_0xad78e5['length'],0xff);_0xad78e5=Buffer[_0x2c9af8(0x1cf)]([_0xad78e5,_0x3d4042]);const _0x58b42b=Buffer[_0x2c9af8(0x206)](0x3);_0x58b42b['writeUInt8'](AUDIO_PACKET,0x0),_0x58b42b['writeUInt16BE'](_0xad78e5[_0x2c9af8(0x1da)],0x1);const _0x3b62cf=Buffer[_0x2c9af8(0x1cf)]([_0x58b42b,_0xad78e5]);this[_0x2c9af8(0x1ff)][_0x2c9af8(0x1ec)]&&(this['socket']['write'](_0x3b62cf),await new Promise(_0x2329be=>setTimeout(_0x2329be,0x14)));}}[a412_0x5b56f0(0x1cd)](_0x42ad53){const _0x5cb1f0=a412_0x5b56f0;this[_0x5cb1f0(0x1f1)]=[],this[_0x5cb1f0(0x1fd)]['clearTextQueue']();const _0xb871b2={'role':_0x5cb1f0(0x1fb),'content':_0x42ad53};this['messages'][_0x5cb1f0(0x1c9)](_0xb871b2),logger[_0x5cb1f0(0x1cc)](_0x5cb1f0(0x1d1)+_0x42ad53),this[_0x5cb1f0(0x208)]['sendToLlm'](this[_0x5cb1f0(0x1ac)],_0x42ad53,this['messages']);}[a412_0x5b56f0(0x1b5)](){const _0x33df53=a412_0x5b56f0;logger[_0x33df53(0x1cc)](_0x33df53(0x1d6));}[a412_0x5b56f0(0x1c8)](_0x23e560){const _0x49fa19=a412_0x5b56f0;logger[_0x49fa19(0x1c7)]('Error\x20during\x20streaming\x20to\x20ASR:\x20'+_0x23e560[_0x49fa19(0x1f7)]),this['handleTerminatePacket'](),this[_0x49fa19(0x1bc)](_0x49fa19(0x1c7),_0x23e560);}[a412_0x5b56f0(0x1c2)](_0x31dbfd){const _0x2cc9f8=a412_0x5b56f0,_0x1c50a3={'role':'assistant','content':_0x31dbfd};this['messages']['push'](_0x1c50a3),logger[_0x2cc9f8(0x1cc)](_0x2cc9f8(0x1b7)+_0x31dbfd),this[_0x2cc9f8(0x1fd)][_0x2cc9f8(0x1db)](_0x31dbfd);}['handleLlmAudio'](_0xee25b9){const _0x1da00f=a412_0x5b56f0;this[_0x1da00f(0x1f1)]['push'](_0xee25b9);}['handleLlmEnd'](){const _0x4c511e=a412_0x5b56f0;logger[_0x4c511e(0x1cc)](_0x4c511e(0x1f0));}[a412_0x5b56f0(0x1b6)](_0x1b2f3d){const _0x5efe68=a412_0x5b56f0;logger[_0x5efe68(0x1c7)](_0x5efe68(0x1d5)+_0x1b2f3d[_0x5efe68(0x1f7)]),this[_0x5efe68(0x1e3)](),this[_0x5efe68(0x1bc)]('error',_0x1b2f3d);}[a412_0x5b56f0(0x209)](_0x2024e9){const _0x2cad59=a412_0x5b56f0;this[_0x2cad59(0x1f1)][_0x2cad59(0x1c9)](_0x2024e9);}[a412_0x5b56f0(0x1c6)](_0x53f215){const _0x77efa0=a412_0x5b56f0;this[_0x77efa0(0x1f1)][_0x77efa0(0x1c9)](_0x53f215);}[a412_0x5b56f0(0x1c3)](){const _0x577525=a412_0x5b56f0;logger[_0x577525(0x1cc)](_0x577525(0x1d8));}[a412_0x5b56f0(0x1de)](){const _0x200b2f=a412_0x5b56f0;logger[_0x200b2f(0x1cc)](_0x200b2f(0x204));}[a412_0x5b56f0(0x1ce)](_0x580527){const _0x147fd8=a412_0x5b56f0;logger['error'](_0x147fd8(0x1be)+_0x580527[_0x147fd8(0x1f7)]),this[_0x147fd8(0x1e3)](),this['emit']('error',_0x580527);}[a412_0x5b56f0(0x1f9)](_0x1a5999){const _0xafa1ad=a412_0x5b56f0;logger[_0xafa1ad(0x1c7)](_0xafa1ad(0x1c4)+_0x1a5999[_0xafa1ad(0x1f7)]),this[_0xafa1ad(0x1e3)](),this[_0xafa1ad(0x1bc)]('error',_0x1a5999);}[a412_0x5b56f0(0x1f9)](_0x1c783d){const _0x5bcd1e=a412_0x5b56f0;logger[_0x5bcd1e(0x1c7)]('Error\x20during\x20streaming\x20to\x20STS:\x20'+_0x1c783d[_0x5bcd1e(0x1f7)]),this[_0x5bcd1e(0x1e3)](),this[_0x5bcd1e(0x1bc)](_0x5bcd1e(0x1c7),_0x1c783d);}[a412_0x5b56f0(0x205)](){const _0x2c4772=a412_0x5b56f0;logger[_0x2c4772(0x1cc)](_0x2c4772(0x1ef)),this[_0x2c4772(0x1e3)](),this[_0x2c4772(0x1bc)](_0x2c4772(0x203));}['handleError'](_0x1dcb33){const _0x2f6586=a412_0x5b56f0;_0x1dcb33 instanceof AggregateError?_0x1dcb33[_0x2f6586(0x1ba)][_0x2f6586(0x200)](_0x1cda10=>logger['error'](_0x2f6586(0x1d0)+_0x1cda10['message'])):logger['error'](_0x2f6586(0x1d0)+_0x1dcb33[_0x2f6586(0x1f7)]),this[_0x2f6586(0x1e3)](),this['emit']('error',_0x1dcb33);}['sendHangupPacket'](){const _0x567ad7=a412_0x5b56f0,_0x25f73b=Buffer[_0x567ad7(0x206)](0x0),_0x1f2e32=Buffer[_0x567ad7(0x206)](TERMINATE_PACKET_LENGTH+_0x25f73b[_0x567ad7(0x1da)]);_0x1f2e32['writeUInt8'](TERMINATE_PACKET,0x0),_0x1f2e32[_0x567ad7(0x1bd)](_0x25f73b[_0x567ad7(0x1da)],0x1),_0x25f73b[_0x567ad7(0x1dd)](_0x1f2e32,TERMINATE_PACKET_LENGTH),this[_0x567ad7(0x1ff)]['write'](_0x1f2e32);}}module[a412_0x5b56f0(0x1bb)]={'ClientHandler':ClientHandler};