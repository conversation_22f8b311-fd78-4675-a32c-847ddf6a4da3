#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Manejador de Google Cloud TTS
======================================================

Este módulo implementa el manejador de síntesis de voz usando Google Cloud Text-to-Speech
para convertir texto a audio en tiempo real durante las llamadas.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import logging
import io
import os
import struct
from typing import Optional, Dict, Any, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime

try:
    from google.cloud import texttospeech
    from google.oauth2 import service_account
    import google.auth
except ImportError:
    raise ImportError("google-cloud-texttospeech no está instalado. Ejecute: pip install google-cloud-texttospeech")

from config import GoogleTTSConfig


@dataclass
class TTSRequest:
    """Solicitud de síntesis de voz."""
    text: str
    language_code: str
    voice_name: str
    audio_encoding: str
    sample_rate: int
    speaking_rate: float = 1.0
    pitch: float = 0.0
    volume_gain_db: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now()


@dataclass
class TTSResult:
    """Resultado de síntesis de voz."""
    audio_data: bytes
    text: str
    duration_ms: int
    voice_name: str
    sample_rate: int
    timestamp: datetime = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now()


class GoogleTTSHandler:
    """Manejador de síntesis de voz usando Google Cloud Text-to-Speech."""
    
    def __init__(self, config: GoogleTTSConfig):
        self.config = config
        self.client = None
        self.is_initialized = False
        
        # Configurar logging
        self.logger = logging.getLogger(__name__)
        
        # Estadísticas
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_characters': 0,
            'total_audio_bytes': 0,
            'average_generation_time': 0.0,
            'errors': 0
        }
    
    async def initialize(self) -> None:
        """Inicializa el cliente de Google Cloud TTS."""
        try:
            if self.is_initialized:
                return
            
            # Configurar credenciales
            if self.config.credentials_path:
                # Usar archivo de credenciales
                credentials = service_account.Credentials.from_service_account_file(
                    self.config.credentials_path
                )
                self.client = texttospeech.TextToSpeechClient(credentials=credentials)
            else:
                # Usar credenciales por defecto (ADC)
                self.client = texttospeech.TextToSpeechClient()
            
            # Verificar que el cliente funciona
            await self._test_connection()
            
            self.is_initialized = True
            self.logger.info("Google TTS Handler inicializado correctamente")
            
        except Exception as e:
            self.logger.error(f"Error inicializando Google TTS Handler: {e}")
            raise
    
    async def _test_connection(self) -> None:
        """Prueba la conexión con Google Cloud TTS."""
        try:
            # Ejecutar en thread pool para no bloquear
            loop = asyncio.get_event_loop()
            
            def _test():
                # Listar voces disponibles como prueba
                request = texttospeech.ListVoicesRequest(
                    language_code=self.config.language_code
                )
                response = self.client.list_voices(request=request)
                return len(response.voices)
            
            voice_count = await loop.run_in_executor(None, _test)
            self.logger.info(f"Conexión exitosa. Voces disponibles para {self.config.language_code}: {voice_count}")
            
        except Exception as e:
            self.logger.error(f"Error probando conexión: {e}")
            raise
    
    async def text_to_speech(self, text: str, voice_name: Optional[str] = None) -> bytes:
        """Convierte texto a audio usando Google Cloud TTS."""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            if not text or not text.strip():
                return b''
            
            # Crear solicitud
            request = TTSRequest(
                text=text.strip(),
                language_code=self.config.language_code,
                voice_name=voice_name or self.config.voice_name,
                audio_encoding=self.config.audio_encoding,
                sample_rate=self.config.sample_rate,
                speaking_rate=self.config.speaking_rate,
                pitch=self.config.pitch,
                volume_gain_db=self.config.volume_gain_db
            )
            
            # Generar audio
            start_time = asyncio.get_event_loop().time()
            audio_data = await self._generate_audio(request)
            generation_time = asyncio.get_event_loop().time() - start_time
            
            # Actualizar estadísticas
            self._update_stats(request, len(audio_data), generation_time)
            
            self.logger.info(f"TTS_DEBUG: Audio generado: {len(text)} caracteres -> {len(audio_data)} bytes en {generation_time:.3f}s")
            self.logger.info(f"TTS_DEBUG: Texto: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            self.logger.info(f"TTS_DEBUG: Primeros 20 bytes del audio: {audio_data[:20].hex() if len(audio_data) >= 20 else audio_data.hex()}")
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Error en síntesis de voz: {e}")
            self.stats['errors'] += 1
            
            # Generar audio silencioso como fallback
            self.logger.warning("Generando audio silencioso como fallback")
            return self._generate_silence_audio(len(text))
    
    async def _generate_audio(self, request: TTSRequest) -> bytes:
        """Genera audio usando la API de Google Cloud TTS."""
        try:
            # Ejecutar generación en thread pool
            loop = asyncio.get_event_loop()
            
            def _generate():
                # Configurar entrada de texto
                synthesis_input = texttospeech.SynthesisInput(text=request.text)
                
                # Configurar voz
                voice = texttospeech.VoiceSelectionParams(
                    language_code=request.language_code,
                    name=request.voice_name
                )
                
                # Configurar audio
                audio_config = texttospeech.AudioConfig(
                    audio_encoding=getattr(texttospeech.AudioEncoding, request.audio_encoding),
                    sample_rate_hertz=request.sample_rate,
                    speaking_rate=request.speaking_rate,
                    pitch=request.pitch,
                    volume_gain_db=request.volume_gain_db
                )
                
                # Realizar síntesis
                response = self.client.synthesize_speech(
                    input=synthesis_input,
                    voice=voice,
                    audio_config=audio_config
                )
                
                return response.audio_content
            
            audio_data_raw = await loop.run_in_executor(None, _generate)
            
            processed_audio_data = audio_data_raw
            # Extraer solo los datos PCM del archivo WAV (sin headers)
            # Google TTS devuelve WAV, pero AudioSocket necesita PCM crudo
            if audio_data_raw.startswith(b'RIFF') and b'WAVE' in audio_data_raw[:12]:
                # Buscar el chunk 'data' en el archivo WAV
                data_pos = audio_data_raw.find(b'data')
                if data_pos != -1:
                    # Saltar 'data' (4 bytes) + tamaño del chunk (4 bytes)
                    processed_audio_data = audio_data_raw[data_pos + 8:]
                    self.logger.info(f"TTS_DEBUG: Extraído PCM crudo: {len(processed_audio_data)} bytes de {len(audio_data_raw)} bytes WAV")
                else:
                    self.logger.warning("No se encontró chunk 'data' en WAV, usando audio completo")
            
            # Detectar y remover silencio excesivo del inicio
            processed_audio_data = self._trim_leading_silence(processed_audio_data)

            # SIN PADDING - El comando de limpieza de buffer lo reemplaza
            # Usar audio directo sin padding de silencio
            final_audio_data = processed_audio_data

            self.logger.info(f"TTS_DEBUG: Audio generado (SIN padding): {len(final_audio_data)} bytes PCM puro")
            return final_audio_data
            
        except Exception as e:
            self.logger.error(f"Error generando audio: {e}")
            raise
    
    async def text_to_speech_stream(self, text: str, voice_name: Optional[str] = None) -> AsyncGenerator[bytes, None]:
        """Convierte texto a audio con streaming (simulado para compatibilidad)."""
        try:
            # Google TTS no soporta streaming real, pero simulamos para compatibilidad
            audio_data = await self.text_to_speech(text, voice_name)
            
            if audio_data:
                # Dividir en chunks para simular streaming
                chunk_size = 4096
                for i in range(0, len(audio_data), chunk_size):
                    chunk = audio_data[i:i + chunk_size]
                    yield chunk
                    await asyncio.sleep(0.001)  # Pequeño delay para simular streaming
                    
        except Exception as e:
            self.logger.error(f"Error en streaming de TTS: {e}")
    
    def _generate_silence_audio(self, text_length: int) -> bytes:
        """Genera audio silencioso como fallback."""
        try:
            # Calcular duración aproximada basada en longitud del texto
            # Asumiendo ~150 palabras por minuto, ~5 caracteres por palabra
            estimated_duration_seconds = max(1, text_length / (150 * 5 / 60))
            
            # Generar audio silencioso en formato LINEAR16
            sample_rate = self.config.sample_rate
            samples_needed = int(sample_rate * estimated_duration_seconds)
            
            # Crear audio silencioso (todos los valores en 0)
            silence_audio = b'\x00\x00' * samples_needed
            
            self.logger.info(f"Generado audio silencioso de {estimated_duration_seconds:.1f}s para texto de {text_length} caracteres")
            return silence_audio
            
        except Exception as e:
            self.logger.error(f"Error generando audio silencioso: {e}")
            # Fallback mínimo: 1 segundo de silencio
            return b'\x00\x00' * self.config.sample_rate
     
    def _trim_leading_silence(self, audio_data: bytes, silence_threshold: int = 200) -> bytes:
        """Remueve silencio excesivo del inicio del audio."""
        try:
            if len(audio_data) < 4:
                return audio_data

            # Buscar el primer sample que no sea silencio
            samples_to_skip = 0
            max_samples_to_check = min(len(audio_data) // 2, self.config.sample_rate)  # Máximo 1 segundo

            for i in range(0, max_samples_to_check * 2, 2):
                if i + 1 >= len(audio_data):
                    break

                # Leer sample de 16-bit little-endian
                sample = struct.unpack('<h', audio_data[i:i+2])[0]

                if abs(sample) > silence_threshold:
                    # Encontramos audio real, mantener un pequeño buffer
                    buffer_samples = min(samples_to_skip, self.config.sample_rate // 40)  # 25ms buffer
                    start_pos = max(0, (samples_to_skip - buffer_samples) * 2)

                    trimmed_audio = audio_data[start_pos:]
                    trimmed_duration = samples_to_skip / self.config.sample_rate

                    if samples_to_skip > 0:
                        self.logger.info(f"TTS_DEBUG: Removido silencio inicial: {trimmed_duration:.3f}s ({samples_to_skip} samples)")

                    return trimmed_audio

                samples_to_skip += 1

            # Si todo es silencio, devolver original
            return audio_data

        except Exception as e:
            self.logger.error(f"Error removiendo silencio inicial: {e}")
            return audio_data

    def _generate_silence_padding(self, duration_ms: int) -> bytes:
        """Genera padding de silencio para evitar cortes al inicio del audio."""
        try:
            # Convertir milisegundos a segundos
            duration_seconds = duration_ms / 1000.0

            # Calcular número de samples necesarios
            sample_rate = self.config.sample_rate
            samples_needed = int(sample_rate * duration_seconds)

            # Crear audio silencioso en formato LINEAR16 (16-bit, little-endian)
            silence_padding = b'\x00\x00' * samples_needed

            return silence_padding

        except Exception as e:
             self.logger.error(f"Error generando padding de silencio: {e}")
             return b''
    
    def _update_stats(self, request: TTSRequest, audio_size: int, generation_time: float) -> None:
        """Actualiza estadísticas del handler."""
        try:
            self.stats['total_requests'] += 1
            self.stats['successful_requests'] += 1
            self.stats['total_characters'] += len(request.text)
            self.stats['total_audio_bytes'] += audio_size
            
            # Calcular tiempo promedio de generación
            current_avg = self.stats['average_generation_time']
            total_requests = self.stats['total_requests']
            
            if total_requests == 1:
                self.stats['average_generation_time'] = generation_time
            else:
                self.stats['average_generation_time'] = (
                    (current_avg * (total_requests - 1) + generation_time) / total_requests
                )
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estadísticas del handler."""
        return {
            **self.stats,
            'is_initialized': self.is_initialized,
            'current_voice': self.config.voice_name,
            'language_code': self.config.language_code,
            'sample_rate': self.config.sample_rate,
            'audio_encoding': self.config.audio_encoding
        }

    async def text_to_speech_stream_real(self, text: str):
        """Genera audio TTS con streaming REAL usando OpenAI TTS."""
        try:
            import openai
            from openai import OpenAI
            import io

            # Usar OpenAI TTS que soporta streaming real
            client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

            self.logger.info(f"TTS_STREAM: Iniciando streaming real para: '{text[:50]}...'")

            # Generar audio con streaming
            response = client.audio.speech.create(
                model="tts-1",  # Modelo más rápido
                voice="alloy",  # Voz neutral
                input=text,
                response_format="pcm",  # PCM directo
                speed=1.0
            )

            # Leer el stream en chunks
            chunk_size = 320  # 40ms a 8kHz
            total_bytes = 0

            # Convertir respuesta a bytes y procesar en chunks
            audio_data = response.content

            for i in range(0, len(audio_data), chunk_size):
                chunk = audio_data[i:i + chunk_size]
                total_bytes += len(chunk)
                yield chunk

                # Pequeña pausa para simular streaming real
                await asyncio.sleep(0.001)

            self.logger.info(f"TTS_STREAM: Completado - {total_bytes} bytes")
            yield None

        except Exception as e:
            self.logger.error(f"Error en TTS streaming real: {e}")
            # Fallback al método tradicional
            try:
                audio_data = await self.text_to_speech(text)
                chunk_size = 320

                for i in range(0, len(audio_data), chunk_size):
                    chunk = audio_data[i:i + chunk_size]
                    yield chunk
                    await asyncio.sleep(0.001)

                yield None
            except:
                yield None

    async def text_to_speech_stream(self, text: str):
        """Genera audio TTS como stream asíncrono (simulado)."""
        try:
            # Generar audio completo primero
            audio_data = await self.text_to_speech(text)

            # Simular streaming dividiendo en chunks
            chunk_size = 320  # 40ms de audio a 8kHz

            for i in range(0, len(audio_data), chunk_size):
                chunk = audio_data[i:i + chunk_size]
                yield chunk

                # Sin delay - streaming lo más rápido posible
                # await asyncio.sleep(0.001)  # Mínimo yield

            # Señalar fin del stream
            yield None

        except Exception as e:
            self.logger.error(f"Error en TTS streaming: {e}")
            yield None

    async def cleanup(self) -> None:
        """Limpia recursos del handler."""
        try:
            self.client = None
            self.is_initialized = False
            
            self.logger.info("Google TTS Handler limpiado")
            
        except Exception as e:
            self.logger.error(f"Error limpiando Google TTS Handler: {e}")


# Función de utilidad para testing
async def test_google_tts_handler():
    """Función de prueba para el Google TTS Handler."""
    from config import Config
    
    config = Config()
    handler = GoogleTTSHandler(config.google_tts)
    
    try:
        await handler.initialize()
        print("Google TTS Handler inicializado correctamente")
        
        # Probar síntesis básica
        test_text = "Hola, este es un mensaje de prueba del sistema de cobranza."
        audio_data = await handler.text_to_speech(test_text)
        
        print(f"Audio generado: {len(audio_data)} bytes")
        print(f"Estadísticas: {handler.get_stats()}")
        
        await handler.cleanup()
        print("Prueba completada exitosamente")
        
    except Exception as e:
        print(f"Error en prueba: {e}")
        await handler.cleanup()


if __name__ == "__main__":
    asyncio.run(test_google_tts_handler())