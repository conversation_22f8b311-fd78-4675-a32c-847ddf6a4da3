#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Call Session Manager
===================

Maneja las sesiones de llamada individuales con todos sus handlers y estado.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass, field

from asr_handler import ASRHandler
from google_tts_handler import GoogleTTSHandler
from ai_conversation import AIConversationEngine


@dataclass
class CallSession:
    """Representa una sesión de llamada activa."""
    session_id: str
    caller_number: str
    start_time: datetime
    asr_handler: Optional['ASRHandler'] = None
    tts_handler: Optional['GoogleTTSHandler'] = None
    ai_engine: Optional['AIConversationEngine'] = None
    audio_buffer: bytes = b''
    is_speaking: bool = False
    conversation_state: str = 'greeting'
    customer_data: Optional[Dict[str, Any]] = None
    
    # Atributos para manejo de interrupciones
    client_speaking: bool = False
    last_speech_time: float = 0.0
    audio_playing: bool = False
    audio_interrupted: bool = False
    audio_playing_start_time: Optional[datetime] = None
    
    # Tareas asíncronas
    silence_task: Optional[asyncio.Task] = None
    keepalive_task: Optional[asyncio.Task] = None
    audio_send_task: Optional[asyncio.Task] = None  # 🚀 Para cancelar envío de audio

    # Control de procesamiento
    processing_user_input: bool = False
    pending_user_inputs: list = field(default_factory=list)
    
    def __post_init__(self):
        if not self.session_id:
            self.session_id = str(uuid.uuid4())
        if not self.start_time:
            self.start_time = datetime.now()


class CallSessionManager:
    """Maneja las sesiones de llamada."""
    
    def __init__(self, config, db_manager, logger):
        self.config = config
        self.db_manager = db_manager
        self.logger = logger
        self.active_sessions: Dict[str, CallSession] = {}
    
    async def create_session(self, session_id: str, caller_number: str) -> CallSession:
        """Crea una nueva sesión de llamada."""
        session = CallSession(
            session_id=session_id,
            caller_number=caller_number,
            start_time=datetime.now()
        )
        
        # Inicializar handlers
        await self._initialize_session_handlers(session)
        
        # Registrar sesión
        self.active_sessions[session_id] = session
        self.logger.info(f"Sesión {session_id} creada para {caller_number}")
        
        return session
    
    async def _initialize_session_handlers(self, session: CallSession) -> None:
        """Inicializa los handlers para la sesión."""
        try:
            # Inicializar ASR Handler Y PRE-INICIALIZAR STREAMING
            session.asr_handler = ASRHandler(self.config.deepgram)
            await session.asr_handler.initialize()
            # 🚀 OPTIMIZACIÓN: Pre-inicializar streaming para respuesta inmediata
            await session.asr_handler.start_streaming()
            self.logger.info(f"ASR streaming pre-inicializado para sesión {session.session_id}")

            # Inicializar TTS Handler
            session.tts_handler = GoogleTTSHandler(self.config.google_tts)
            await session.tts_handler.initialize()

            # Inicializar AI Engine
            session.ai_engine = AIConversationEngine(self.config.openai, self.db_manager)
            await session.ai_engine.initialize()

            self.logger.info(f"Handlers inicializados para sesión {session.session_id}")

        except Exception as e:
            self.logger.error(f"Error inicializando handlers: {e}")
            raise
    
    async def cleanup_session(self, session_id: str) -> None:
        """Limpia una sesión terminada."""
        try:
            session = self.active_sessions.get(session_id)
            if not session:
                return
            
            # Cancelar tareas asíncronas
            await self._cancel_session_tasks(session)
            
            # Cerrar handlers
            if session.asr_handler:
                await session.asr_handler.cleanup()
            if session.tts_handler:
                await session.tts_handler.cleanup()
            if session.ai_engine:
                await session.ai_engine.cleanup()
            
            # Registrar fin de llamada en BD
            await self._log_call_end(session)
            
            # Remover de sesiones activas
            del self.active_sessions[session_id]
            
            self.logger.info(f"Sesión {session_id} limpiada")
            
        except Exception as e:
            self.logger.error(f"Error limpiando sesión {session_id}: {e}")
    
    async def _cancel_session_tasks(self, session: CallSession) -> None:
        """Cancela todas las tareas asíncronas de la sesión."""
        tasks_to_cancel = []

        if session.silence_task and not session.silence_task.done():
            tasks_to_cancel.append(session.silence_task)

        if session.keepalive_task and not session.keepalive_task.done():
            tasks_to_cancel.append(session.keepalive_task)

        # 🚀 OPTIMIZACIÓN: Cancelar tarea de envío de audio
        if session.audio_send_task and not session.audio_send_task.done():
            tasks_to_cancel.append(session.audio_send_task)

        for task in tasks_to_cancel:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
    
    async def _log_call_end(self, session: CallSession) -> None:
        """Registra el fin de la llamada en la base de datos."""
        try:
            end_time = datetime.now()
            duration = (end_time - session.start_time).total_seconds()
            
            await self.db_manager.log_call(
                telefono=session.caller_number,
                duracion=int(duration),
                estado='llamada_cortada',
                notas=f"Sesión: {session.session_id}"
            )
            
        except Exception as e:
            self.logger.error(f"Error registrando fin de llamada: {e}")
    
    def get_session(self, session_id: str) -> Optional[CallSession]:
        """Obtiene una sesión por ID."""
        return self.active_sessions.get(session_id)
    
    def get_active_sessions_count(self) -> int:
        """Retorna el número de sesiones activas."""
        return len(self.active_sessions)
    
    def get_all_sessions(self) -> Dict[str, CallSession]:
        """Retorna todas las sesiones activas."""
        return self.active_sessions.copy()
