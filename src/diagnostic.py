#!/usr/bin/env python3
"""
Diagnóstico del Sistema
=======================

Script para diagnosticar problemas del sistema de cobranza.
"""

import os
import asyncio
import logging
from datetime import datetime

# Configurar logging básico
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def check_environment():
    """Verifica variables de entorno."""
    print("\n🔍 VERIFICANDO VARIABLES DE ENTORNO")
    print("=" * 50)
    
    required_vars = [
        'DEEPGRAM_API_KEY',
        'OPENAI_API_KEY', 
        'GOOGLE_APPLICATION_CREDENTIALS',
        'DATABASE_PASSWORD'
    ]
    
    for var in required_vars:
        value = os.getenv(var, '')
        if value:
            print(f"✅ {var}: {value[:10]}...")
        else:
            print(f"❌ {var}: NO CONFIGURADA")

async def check_deepgram():
    """Verifica conexión a Deepgram."""
    print("\n🎤 VERIFICANDO DEEPGRAM")
    print("=" * 50)
    
    try:
        from deepgram import DeepgramClient, LiveOptions
        
        api_key = os.getenv('DEEPGRAM_API_KEY', '')
        if not api_key:
            print("❌ DEEPGRAM_API_KEY no configurada")
            return False
        
        client = DeepgramClient(api_key)
        print("✅ Cliente Deepgram creado")
        
        # Test básico de configuración
        options = LiveOptions(
            model="nova-2",
            language="es",
            encoding="linear16",
            sample_rate=8000,
            interim_results=True,
            punctuate=False,
            profanity_filter=False
        )
        print("✅ Opciones básicas configuradas")
        
        return True
        
    except Exception as e:
        print(f"❌ Error con Deepgram: {e}")
        return False

async def check_openai():
    """Verifica conexión a OpenAI."""
    print("\n🤖 VERIFICANDO OPENAI")
    print("=" * 50)
    
    try:
        from openai import AsyncOpenAI
        
        api_key = os.getenv('OPENAI_API_KEY', '')
        if not api_key:
            print("❌ OPENAI_API_KEY no configurada")
            return False
        
        client = AsyncOpenAI(api_key=api_key)
        print("✅ Cliente OpenAI creado")
        
        # Test básico
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": "test"}],
            max_tokens=5
        )
        print("✅ Test de API exitoso")
        
        return True
        
    except Exception as e:
        print(f"❌ Error con OpenAI: {e}")
        return False

async def check_database():
    """Verifica conexión a base de datos."""
    print("\n💾 VERIFICANDO BASE DE DATOS")
    print("=" * 50)
    
    try:
        from database import DatabaseManager
        from config import Config
        
        config = Config()
        db = DatabaseManager(config.database)
        
        # Test de conexión
        result = await db.fetch_one("SELECT 1 as test")
        if result and result['test'] == 1:
            print("✅ Conexión a BD exitosa")
            
            # Verificar tablas
            tables = ['clientes', 'deudas', 'compromisos_pago', 'logs_llamadas']
            for table in tables:
                count = await db.fetch_one(f"SELECT COUNT(*) as count FROM {table}")
                print(f"✅ Tabla {table}: {count['count']} registros")
            
            return True
        else:
            print("❌ Test de BD falló")
            return False
        
    except Exception as e:
        print(f"❌ Error con BD: {e}")
        return False

async def check_google_tts():
    """Verifica Google TTS."""
    print("\n🔊 VERIFICANDO GOOGLE TTS")
    print("=" * 50)
    
    try:
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '')
        if not credentials_path:
            print("❌ GOOGLE_APPLICATION_CREDENTIALS no configurada")
            return False
        
        if not os.path.exists(credentials_path):
            print(f"❌ Archivo de credenciales no existe: {credentials_path}")
            return False
        
        print(f"✅ Credenciales encontradas: {credentials_path}")
        
        # Test básico
        from google_tts_handler import GoogleTTSHandler
        from config import Config
        
        config = Config()
        tts = GoogleTTSHandler(config.google_tts)
        
        # Test de síntesis
        audio = await tts.text_to_speech("test")
        if audio and len(audio) > 0:
            print(f"✅ TTS funcionando: {len(audio)} bytes generados")
            return True
        else:
            print("❌ TTS no generó audio")
            return False
        
    except Exception as e:
        print(f"❌ Error con Google TTS: {e}")
        return False

async def run_full_diagnostic():
    """Ejecuta diagnóstico completo."""
    print("🏥 DIAGNÓSTICO COMPLETO DEL SISTEMA")
    print("=" * 60)
    print(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {}
    
    # Verificar cada componente
    results['environment'] = await check_environment()
    results['deepgram'] = await check_deepgram()
    results['openai'] = await check_openai()
    results['database'] = await check_database()
    results['google_tts'] = await check_google_tts()
    
    # Resumen final
    print("\n📊 RESUMEN DEL DIAGNÓSTICO")
    print("=" * 50)
    
    total_checks = len(results)
    passed_checks = sum(1 for result in results.values() if result)
    
    for component, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {component.upper()}: {'OK' if status else 'FALLO'}")
    
    print(f"\n📈 RESULTADO: {passed_checks}/{total_checks} componentes funcionando")
    
    if passed_checks == total_checks:
        print("🎉 ¡SISTEMA COMPLETAMENTE FUNCIONAL!")
    elif passed_checks >= total_checks * 0.8:
        print("⚠️  Sistema mayormente funcional con algunos problemas")
    else:
        print("🚨 Sistema con problemas críticos")
    
    return results

if __name__ == "__main__":
    asyncio.run(run_full_diagnostic())
