#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Connection Handler
==================

Maneja las conexiones AudioSocket individuales y el protocolo de comunicación.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import socket
import struct
import uuid
from datetime import datetime
from typing import Optional

from call_session import CallSessionManager
from audio_processor import AudioProcessor
from silence_manager import SilenceManager


class ConnectionHandler:
    """Maneja conexiones AudioSocket individuales."""
    
    def __init__(self, config, db_manager, logger, stats):
        self.config = config
        self.db_manager = db_manager
        self.logger = logger
        self.stats = stats
        
        # Componentes
        self.session_manager = CallSessionManager(config, db_manager, logger)
        self.audio_processor = AudioProcessor(config, logger)
        self.silence_manager = SilenceManager(config, logger)
    
    async def handle_connection(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter) -> None:
        """Maneja una nueva conexión de Asterisk con configuraciones optimizadas."""
        client_addr = writer.get_extra_info('peername')
        temp_session_id = str(uuid.uuid4())

        self.logger.info(f"Nueva conexión AudioSocket desde {client_addr} - Sesión temporal: {temp_session_id}")

        # Configurar opciones de la conexión individual
        self._configure_connection(writer)

        try:
            # Leer UUID de Asterisk (este será nuestro session_id real)
            asterisk_uuid = await self._read_asterisk_uuid(reader)
            if not asterisk_uuid:
                self.logger.error("No se pudo leer UUID de Asterisk")
                return

            # 🎯 USAR UUID DE ASTERISK COMO SESSION_ID PARA IDENTIFICACIÓN PRECISA
            session_id = asterisk_uuid
            self.logger.info(f"🔗 UUID de Asterisk recibido: {asterisk_uuid}")
            self.logger.info(f"🎯 Usando UUID de Asterisk como session_id: {session_id}")

            # Obtener información del canal
            caller_number = await self._get_caller_info(asterisk_uuid)
            if not caller_number:
                self.logger.error("No se pudo obtener información del llamador")
                return

            # Crear sesión de llamada con UUID de Asterisk
            session = await self.session_manager.create_session(session_id, caller_number)
            
            # Actualizar estadísticas
            self.stats['total_calls'] += 1
            self.stats['active_calls'] += 1
            
            # Procesar llamada
            await self._process_call(session, reader, writer)
            
        except Exception as e:
            self.logger.error(f"Error procesando conexión {client_addr}: {e}")
            self.stats['failed_calls'] += 1
        finally:
            # Limpiar sesión
            await self.session_manager.cleanup_session(session_id)

            # Cerrar conexión de forma segura
            await self._close_connection_safely(writer)

            self.stats['active_calls'] -= 1
    
    def _configure_connection(self, writer: asyncio.StreamWriter) -> None:
        """Configura opciones de la conexión individual."""
        try:
            transport = writer.get_extra_info('socket')
            if transport:
                transport.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                transport.setsockopt(socket.SOL_TCP, socket.TCP_NODELAY, 1)
                # Configurar buffer sizes
                transport.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)  # 64KB send buffer
                transport.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)  # 64KB receive buffer
        except Exception as e:
            self.logger.warning(f"No se pudieron configurar opciones de socket: {e}")
    
    async def _read_asterisk_uuid(self, reader: asyncio.StreamReader) -> Optional[str]:
        """Lee el UUID enviado por Asterisk según el protocolo AudioSocket."""
        try:
            # Leer header AudioSocket (3 bytes: type + length)
            header = await reader.read(3)
            if len(header) != 3:
                self.logger.error("Header AudioSocket incompleto")
                return None
            
            msg_type, payload_length = struct.unpack('>BH', header)
            
            # Verificar que es un mensaje UUID (tipo 0x01)
            if msg_type != 0x01:
                self.logger.error(f"Tipo de mensaje inesperado: 0x{msg_type:02x}, esperado 0x01")
                return None
            
            # Verificar longitud del UUID (16 bytes)
            if payload_length != 16:
                self.logger.error(f"Longitud UUID incorrecta: {payload_length}, esperado 16")
                return None
            
            # Leer UUID
            uuid_bytes = await reader.read(16)
            if len(uuid_bytes) == 16:
                return uuid_bytes.hex()
            
            return None
        except Exception as e:
            self.logger.error(f"Error leyendo UUID de Asterisk: {e}")
            return None
    
    async def _get_caller_info(self, asterisk_uuid: str) -> Optional[str]:
        """Obtiene información del llamador desde Asterisk usando AMI."""
        try:
            # Importar AMI para consultar información del canal
            from .asterisk_ami import create_ami_client

            ami = create_ami_client(self.config)

            if await ami.connect():
                self.logger.info("🔍 Consultando información del llamador via AMI")

                # Obtener todos los canales para encontrar el que corresponde al UUID
                channels = await ami._get_all_channels()

                # Buscar canal que pueda estar relacionado con este UUID
                for channel in channels:
                    # Obtener información detallada del canal
                    channel_info = await ami.get_channel_info(channel)
                    if channel_info:
                        caller_id = channel_info.get('calleridnum', '')
                        context = channel_info.get('context', '')

                        # Si está en contexto 'avr' (nuestro contexto AudioSocket)
                        if context == 'avr' and caller_id and caller_id != '<unknown>':
                            self.logger.info(f"📞 Caller encontrado: {caller_id} en canal {channel}")
                            await ami.disconnect()
                            return caller_id

                await ami.disconnect()

            # Fallback: extraer de variables de entorno o usar placeholder
            self.logger.warning("⚠️ No se pudo obtener caller real, usando placeholder")
            return "1234567890"  # Placeholder

        except Exception as e:
            self.logger.error(f"Error obteniendo caller info: {e}")
            return "1234567890"  # Placeholder
    
    async def _process_call(self, session, reader: asyncio.StreamReader, writer: asyncio.StreamWriter) -> None:
        """Procesa el audio de la llamada según el protocolo AudioSocket."""
        self.logger.info(f"Iniciando procesamiento de llamada para {session.caller_number}")
        
        try:
            # Enviar saludo inicial
            await self._send_greeting(session, writer)
            
            # Iniciar keepalive para mantener la llamada activa
            keepalive_task = asyncio.create_task(self.silence_manager.keepalive_loop(writer, session))
            session.keepalive_task = keepalive_task

            # Loop principal de procesamiento de audio
            while True:
                # Leer header AudioSocket (3 bytes)
                header = await reader.read(3)
                if len(header) != 3:
                    self.logger.debug("Conexión cerrada por el cliente")
                    break
                
                msg_type, payload_length = struct.unpack('>BH', header)
                
                # Manejar diferentes tipos de mensaje
                self.logger.debug(f"AUDIOSOCKET (Sesión {session.session_id}): Recibido header: tipo={msg_type:#02x}, longitud={payload_length}")
                
                if msg_type == 0x00:  # Terminate
                    self.logger.info(f"AUDIOSOCKET (Sesión {session.session_id}): Recibido mensaje de terminación. Cerrando loop.")
                    self.logger.info(f"AUDIOSOCKET (Sesión {session.session_id}): DEBUG - Terminación payload length: {payload_length}")
                    if payload_length > 0:
                        term_data = await reader.read(payload_length)
                        self.logger.info(f"AUDIOSOCKET (Sesión {session.session_id}): DEBUG - Terminación payload hex: {term_data.hex()}")
                    break
                    
                elif msg_type == 0x10:  # Audio data
                    if payload_length > 0:
                        self.logger.debug(f"AUDIOSOCKET (Sesión {session.session_id}): Recibiendo {payload_length} bytes de audio.")
                        audio_data = await reader.read(payload_length)
                        if len(audio_data) == payload_length:
                            self.logger.debug(f"AUDIOSOCKET (Sesión {session.session_id}): {len(audio_data)} bytes de audio recibidos. Procesando...")
                            await self.audio_processor.process_incoming_audio(session, audio_data, writer)
                        else:
                            self.logger.warning(f"AUDIOSOCKET (Sesión {session.session_id}): Se esperaban {payload_length} bytes de audio, se recibieron {len(audio_data)}.")
                    else:
                        self.logger.debug(f"AUDIOSOCKET (Sesión {session.session_id}): Mensaje de audio con longitud 0, ignorando.")
                        
                elif msg_type == 0xff:  # Error
                    error_data = await reader.read(payload_length) if payload_length > 0 else b''
                    self.logger.error(f"AUDIOSOCKET (Sesión {session.session_id}): Recibido mensaje de error de Asterisk: {error_data.decode(errors='ignore') if error_data else 'Sin datos de error'}. Cerrando loop.")
                    self.logger.error(f"AUDIOSOCKET (Sesión {session.session_id}): DEBUG - Error payload hex: {error_data.hex() if error_data else 'vacío'}")
                    self.logger.error(f"AUDIOSOCKET (Sesión {session.session_id}): DEBUG - Payload length: {payload_length}")
                    break
                    
                else:
                    self.logger.warning(f"AUDIOSOCKET (Sesión {session.session_id}): Tipo de mensaje desconocido: {msg_type:#02x}. Descartando {payload_length} bytes de payload si los hay.")
                    if payload_length > 0:
                        await reader.read(payload_length) # Leer y descartar
            
            self.stats['successful_calls'] += 1
            self.logger.info(f"Llamada completada exitosamente: {session.session_id}")

        except Exception as e:
            self.logger.error(f"Error procesando llamada {session.session_id}: {e}")
            self.stats['failed_calls'] += 1
    
    async def _send_greeting(self, session, writer: asyncio.StreamWriter) -> None:
        """Envía el saludo inicial al cliente."""
        try:
            # Inicializar conversación
            await session.ai_engine.start_conversation(session.session_id, session.caller_number)
            
            # Obtener saludo - MÉTODO TRADICIONAL OPTIMIZADO
            greeting_text = await session.ai_engine.get_greeting()
            audio_data = await session.tts_handler.text_to_speech(greeting_text)

            # Enviar audio a Asterisk con timing optimizado
            await self.audio_processor.send_audio_to_asterisk(audio_data, writer, session)

            self.logger.info(f"Saludo enviado a {session.caller_number}")
            
        except Exception as e:
            self.logger.error(f"Error enviando saludo: {e}")
    
    async def _close_connection_safely(self, writer: asyncio.StreamWriter) -> None:
        """Cierra la conexión de forma segura."""
        try:
            if not writer.is_closing():
                writer.close()
                await asyncio.wait_for(writer.wait_closed(), timeout=2.0)
        except Exception as e:
            self.logger.debug(f"Error cerrando conexión: {e}")
