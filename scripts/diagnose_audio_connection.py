#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de Diagnóstico para Problemas de Audio en AudioSocket
============================================================

Este script ayuda a diagnosticar problemas de conexión y audio
entre el bot de cobranza y Asterisk a través de AudioSocket.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import socket
import struct
import time
import logging
from typing import Optional, Tuple

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AudioSocketDiagnostic:
    """Herramienta de diagnóstico para AudioSocket."""
    
    def __init__(self, host: str = '127.0.0.1', port: int = 5001):
        self.host = host
        self.port = port
        
    async def test_connection(self) -> bool:
        """Prueba la conectividad básica al puerto AudioSocket."""
        logger.info(f"Probando conexión a {self.host}:{self.port}")
        
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(self.host, self.port),
                timeout=5.0
            )
            
            logger.info("✅ Conexión establecida exitosamente")
            
            # Cerrar conexión
            writer.close()
            await writer.wait_closed()
            return True
            
        except asyncio.TimeoutError:
            logger.error("❌ Timeout conectando al servidor AudioSocket")
            return False
        except ConnectionRefusedError:
            logger.error("❌ Conexión rechazada - ¿Está el servidor corriendo?")
            return False
        except Exception as e:
            logger.error(f"❌ Error de conexión: {e}")
            return False
    
    async def test_socket_options(self) -> None:
        """Prueba las opciones de socket recomendadas."""
        logger.info("Probando configuraciones de socket...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            
            # Probar configuraciones recomendadas
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            logger.info("✅ SO_KEEPALIVE configurado")
            
            sock.setsockopt(socket.SOL_TCP, socket.TCP_NODELAY, 1)
            logger.info("✅ TCP_NODELAY configurado")
            
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)
            logger.info("✅ Buffer de envío configurado (64KB)")
            
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
            logger.info("✅ Buffer de recepción configurado (64KB)")
            
            sock.close()
            
        except Exception as e:
            logger.error(f"❌ Error configurando opciones de socket: {e}")
    
    async def test_audio_send(self, chunk_size: int = 1024) -> bool:
        """Prueba el envío de audio simulado."""
        logger.info(f"Probando envío de audio con chunks de {chunk_size} bytes")
        
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(self.host, self.port),
                timeout=5.0
            )
            
            # Configurar socket
            transport = writer.get_extra_info('socket')
            if transport:
                transport.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                transport.setsockopt(socket.SOL_TCP, socket.TCP_NODELAY, 1)
            
            # Simular UUID de Asterisk (16 bytes)
            fake_uuid = b'\x01\x23\x45\x67\x89\xab\xcd\xef\x01\x23\x45\x67\x89\xab\xcd\xef'
            uuid_header = struct.pack('>BH', 0x01, 16)
            writer.write(uuid_header + fake_uuid)
            await writer.drain()
            logger.info("✅ UUID simulado enviado")
            
            # Generar audio de prueba (silencio)
            test_audio = b'\x00\x00' * (chunk_size // 2)  # 16-bit samples
            total_chunks = 10
            
            start_time = time.time()
            
            for i in range(total_chunks):
                # Crear header AudioSocket para audio
                header = struct.pack('>BH', 0x10, len(test_audio))
                
                try:
                    writer.write(header + test_audio)
                    await asyncio.wait_for(writer.drain(), timeout=2.0)
                    logger.info(f"✅ Chunk {i+1}/{total_chunks} enviado")
                    
                    # Pequeña pausa
                    await asyncio.sleep(0.001)
                    
                except asyncio.TimeoutError:
                    logger.error(f"❌ Timeout enviando chunk {i+1}")
                    return False
                except Exception as e:
                    logger.error(f"❌ Error enviando chunk {i+1}: {e}")
                    return False
            
            end_time = time.time()
            total_time = end_time - start_time
            total_bytes = total_chunks * len(test_audio)
            
            logger.info(f"✅ Envío completado: {total_bytes} bytes en {total_time:.3f}s")
            logger.info(f"✅ Velocidad: {total_bytes/total_time:.0f} bytes/s")
            
            # Cerrar conexión
            writer.close()
            await writer.wait_closed()
            return True
            
        except Exception as e:
            logger.error(f"❌ Error en prueba de envío de audio: {e}")
            return False
    
    async def test_multiple_connections(self, num_connections: int = 3) -> None:
        """Prueba múltiples conexiones simultáneas."""
        logger.info(f"Probando {num_connections} conexiones simultáneas")
        
        async def single_connection_test(conn_id: int) -> bool:
            try:
                reader, writer = await asyncio.wait_for(
                    asyncio.open_connection(self.host, self.port),
                    timeout=5.0
                )
                
                logger.info(f"✅ Conexión {conn_id} establecida")
                
                # Mantener conexión por un momento
                await asyncio.sleep(1.0)
                
                writer.close()
                await writer.wait_closed()
                logger.info(f"✅ Conexión {conn_id} cerrada")
                return True
                
            except Exception as e:
                logger.error(f"❌ Error en conexión {conn_id}: {e}")
                return False
        
        # Ejecutar conexiones en paralelo
        tasks = [single_connection_test(i) for i in range(num_connections)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful = sum(1 for r in results if r is True)
        logger.info(f"Resultado: {successful}/{num_connections} conexiones exitosas")
    
    async def run_full_diagnostic(self) -> None:
        """Ejecuta diagnóstico completo."""
        logger.info("=== INICIANDO DIAGNÓSTICO COMPLETO ===")
        
        # 1. Prueba de conectividad básica
        logger.info("\n1. Prueba de conectividad básica")
        if not await self.test_connection():
            logger.error("❌ Diagnóstico detenido - no hay conectividad básica")
            return
        
        # 2. Prueba de opciones de socket
        logger.info("\n2. Prueba de configuraciones de socket")
        await self.test_socket_options()
        
        # 3. Prueba de envío de audio
        logger.info("\n3. Prueba de envío de audio")
        await self.test_audio_send(chunk_size=1024)
        
        # 4. Prueba con chunks más pequeños
        logger.info("\n4. Prueba con chunks pequeños (512 bytes)")
        await self.test_audio_send(chunk_size=512)
        
        # 5. Prueba de múltiples conexiones
        logger.info("\n5. Prueba de múltiples conexiones")
        await self.test_multiple_connections()
        
        logger.info("\n=== DIAGNÓSTICO COMPLETADO ===")


async def main():
    """Función principal."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Diagnóstico de AudioSocket')
    parser.add_argument('--host', default='127.0.0.1', help='Host del servidor AudioSocket')
    parser.add_argument('--port', type=int, default=5001, help='Puerto del servidor AudioSocket')
    
    args = parser.parse_args()
    
    diagnostic = AudioSocketDiagnostic(args.host, args.port)
    await diagnostic.run_full_diagnostic()


if __name__ == "__main__":
    asyncio.run(main())
