#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Monitor de Salud de Conexiones AudioSocket
==========================================

Script para monitorear en tiempo real la salud de las conexiones
AudioSocket y detectar problemas de red o audio.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import signal
import sys

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class ConnectionMetrics:
    """Métricas de una conexión."""
    session_id: str
    start_time: datetime
    bytes_sent: int = 0
    bytes_received: int = 0
    chunks_sent: int = 0
    chunks_failed: int = 0
    last_activity: Optional[datetime] = None
    connection_errors: int = 0
    avg_chunk_time: float = 0.0
    is_healthy: bool = True


class AudioSocketMonitor:
    """Monitor de salud para conexiones AudioSocket."""
    
    def __init__(self, host: str = '127.0.0.1', port: int = 5001):
        self.host = host
        self.port = port
        self.connections: Dict[str, ConnectionMetrics] = {}
        self.global_stats = {
            'total_connections': 0,
            'active_connections': 0,
            'total_bytes_sent': 0,
            'total_chunks_sent': 0,
            'total_errors': 0,
            'start_time': datetime.now()
        }
        self.running = False
        
    async def start_monitoring(self, interval: float = 5.0) -> None:
        """Inicia el monitoreo continuo."""
        self.running = True
        logger.info(f"Iniciando monitoreo de AudioSocket en {self.host}:{self.port}")
        
        try:
            while self.running:
                await self.collect_metrics()
                await self.analyze_health()
                await self.report_status()
                await asyncio.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("Deteniendo monitoreo...")
        finally:
            self.running = False
    
    def stop_monitoring(self) -> None:
        """Detiene el monitoreo."""
        self.running = False
    
    async def collect_metrics(self) -> None:
        """Recolecta métricas del sistema."""
        try:
            # Simular recolección de métricas
            # En una implementación real, esto se conectaría al servidor AudioSocket
            # para obtener estadísticas en tiempo real
            
            # Por ahora, simulamos algunas métricas
            current_time = datetime.now()
            
            # Limpiar conexiones antiguas (más de 5 minutos sin actividad)
            cutoff_time = current_time.timestamp() - 300
            
            to_remove = []
            for session_id, metrics in self.connections.items():
                if metrics.last_activity and metrics.last_activity.timestamp() < cutoff_time:
                    to_remove.append(session_id)
            
            for session_id in to_remove:
                del self.connections[session_id]
                self.global_stats['active_connections'] -= 1
                
        except Exception as e:
            logger.error(f"Error recolectando métricas: {e}")
    
    async def analyze_health(self) -> None:
        """Analiza la salud de las conexiones."""
        try:
            unhealthy_connections = []
            
            for session_id, metrics in self.connections.items():
                # Criterios de salud
                is_healthy = True
                reasons = []
                
                # 1. Demasiados errores de conexión
                if metrics.connection_errors > 5:
                    is_healthy = False
                    reasons.append(f"Muchos errores de conexión ({metrics.connection_errors})")
                
                # 2. Tiempo promedio de chunk muy alto
                if metrics.avg_chunk_time > 2.0:
                    is_healthy = False
                    reasons.append(f"Tiempo de chunk alto ({metrics.avg_chunk_time:.2f}s)")
                
                # 3. Muchos chunks fallidos
                if metrics.chunks_sent > 0:
                    failure_rate = metrics.chunks_failed / metrics.chunks_sent
                    if failure_rate > 0.1:  # Más del 10% de fallos
                        is_healthy = False
                        reasons.append(f"Alta tasa de fallos ({failure_rate:.1%})")
                
                # 4. Sin actividad reciente
                if metrics.last_activity:
                    inactive_time = datetime.now() - metrics.last_activity
                    if inactive_time.total_seconds() > 60:  # 1 minuto sin actividad
                        is_healthy = False
                        reasons.append(f"Sin actividad por {inactive_time.total_seconds():.0f}s")
                
                if not is_healthy:
                    unhealthy_connections.append((session_id, reasons))
                
                metrics.is_healthy = is_healthy
            
            # Reportar conexiones no saludables
            if unhealthy_connections:
                logger.warning(f"Conexiones no saludables detectadas: {len(unhealthy_connections)}")
                for session_id, reasons in unhealthy_connections:
                    logger.warning(f"  {session_id}: {', '.join(reasons)}")
                    
        except Exception as e:
            logger.error(f"Error analizando salud: {e}")
    
    async def report_status(self) -> None:
        """Reporta el estado actual del sistema."""
        try:
            uptime = datetime.now() - self.global_stats['start_time']
            
            # Calcular estadísticas
            active_connections = len(self.connections)
            healthy_connections = sum(1 for m in self.connections.values() if m.is_healthy)
            
            total_bytes = sum(m.bytes_sent for m in self.connections.values())
            total_chunks = sum(m.chunks_sent for m in self.connections.values())
            total_errors = sum(m.connection_errors for m in self.connections.values())
            
            # Crear reporte
            report = {
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': uptime.total_seconds(),
                'connections': {
                    'active': active_connections,
                    'healthy': healthy_connections,
                    'unhealthy': active_connections - healthy_connections,
                    'total_created': self.global_stats['total_connections']
                },
                'traffic': {
                    'total_bytes_sent': total_bytes,
                    'total_chunks_sent': total_chunks,
                    'total_errors': total_errors,
                    'avg_bytes_per_connection': total_bytes / max(active_connections, 1),
                    'error_rate': total_errors / max(total_chunks, 1) if total_chunks > 0 else 0
                },
                'performance': {
                    'avg_chunk_time': sum(m.avg_chunk_time for m in self.connections.values()) / max(active_connections, 1) if active_connections > 0 else 0,
                    'bytes_per_second': total_bytes / max(uptime.total_seconds(), 1)
                }
            }
            
            # Log del reporte
            logger.info("=== REPORTE DE SALUD ===")
            logger.info(f"Conexiones: {active_connections} activas, {healthy_connections} saludables")
            logger.info(f"Tráfico: {total_bytes:,} bytes, {total_chunks:,} chunks, {total_errors} errores")
            logger.info(f"Performance: {report['performance']['avg_chunk_time']:.3f}s/chunk, {report['performance']['bytes_per_second']:.0f} bytes/s")
            
            # Guardar reporte en archivo
            with open('/tmp/audiosocket_health.json', 'w') as f:
                json.dump(report, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error generando reporte: {e}")
    
    def add_connection(self, session_id: str) -> None:
        """Registra una nueva conexión."""
        self.connections[session_id] = ConnectionMetrics(
            session_id=session_id,
            start_time=datetime.now(),
            last_activity=datetime.now()
        )
        self.global_stats['total_connections'] += 1
        self.global_stats['active_connections'] += 1
        logger.info(f"Nueva conexión registrada: {session_id}")
    
    def update_connection_metrics(self, session_id: str, bytes_sent: int = 0, 
                                chunks_sent: int = 0, chunks_failed: int = 0,
                                chunk_time: float = 0.0, connection_error: bool = False) -> None:
        """Actualiza métricas de una conexión."""
        if session_id not in self.connections:
            self.add_connection(session_id)
        
        metrics = self.connections[session_id]
        metrics.bytes_sent += bytes_sent
        metrics.chunks_sent += chunks_sent
        metrics.chunks_failed += chunks_failed
        metrics.last_activity = datetime.now()
        
        if connection_error:
            metrics.connection_errors += 1
        
        if chunk_time > 0:
            # Calcular promedio móvil del tiempo de chunk
            if metrics.avg_chunk_time == 0:
                metrics.avg_chunk_time = chunk_time
            else:
                metrics.avg_chunk_time = (metrics.avg_chunk_time * 0.9) + (chunk_time * 0.1)
    
    def remove_connection(self, session_id: str) -> None:
        """Remueve una conexión."""
        if session_id in self.connections:
            del self.connections[session_id]
            self.global_stats['active_connections'] -= 1
            logger.info(f"Conexión removida: {session_id}")


async def main():
    """Función principal."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Monitor de salud AudioSocket')
    parser.add_argument('--host', default='127.0.0.1', help='Host del servidor AudioSocket')
    parser.add_argument('--port', type=int, default=5001, help='Puerto del servidor AudioSocket')
    parser.add_argument('--interval', type=float, default=5.0, help='Intervalo de monitoreo en segundos')
    
    args = parser.parse_args()
    
    monitor = AudioSocketMonitor(args.host, args.port)
    
    # Configurar manejo de señales
    def signal_handler(signum, frame):
        logger.info("Señal recibida, deteniendo monitor...")
        monitor.stop_monitoring()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Iniciar monitoreo
    await monitor.start_monitoring(args.interval)


if __name__ == "__main__":
    asyncio.run(main())
