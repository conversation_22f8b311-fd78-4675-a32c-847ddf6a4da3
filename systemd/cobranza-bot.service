[Unit]
Description=Bot de Cobranza con IA - Sistema Automatizado
Documentation=file:///opt/cobranza-bot/README.md
After=network.target postgresql.service
Wants=postgresql.service
Requires=network.target

[Service]
# Tipo de servicio
Type=simple
Restart=always
RestartSec=10

# Usuario y grupo
User=cobranza
Group=cobranza

# Directorio de trabajo
WorkingDirectory=/opt/cobranza-bot

# Comando de ejecución
ExecStart=/opt/cobranza-bot/venv/bin/python3 /opt/cobranza-bot/start_system.py start
ExecStop=/opt/cobranza-bot/venv/bin/python3 /opt/cobranza-bot/start_system.py stop
ExecReload=/bin/kill -HUP $MAINPID

# Variables de entorno
Environment=PYTHONPATH=/opt/cobranza-bot/src
Environment=PYTHONUNBUFFERED=1
EnvironmentFile=/opt/cobranza-bot/.env

# Configuración de proceso
KillMode=mixed
KillSignal=SIGTERM
TimeoutStartSec=60
TimeoutStopSec=30
TimeoutReloadSec=10

# Límites de recursos
LimitNOFILE=65536
LimitNPROC=4096

# Configuración de logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cobranza-bot

# Configuración de seguridad
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/cobranza-bot/logs /tmp

# Configuración de red
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX

[Install]
WantedBy=multi-user.target