#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script de prueba para el sistema de despedida y corte de llamada.
Verifica que el flujo completo funcione correctamente.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.call_manager import CallManager, CallEndReason
from src.asterisk_ami import AsteriskAMI, AMIConfig
from config import Config


class MockSession:
    """Mock de sesión para pruebas."""
    
    def __init__(self, session_id: str, caller_number: str):
        self.session_id = session_id
        self.caller_number = caller_number
        self.client_name = "<PERSON>"
        self.start_time = datetime.now()


class MockDBManager:
    """Mock del database manager."""
    
    async def log_call(self, telefono: str, duracion: int, estado: str, notas: str):
        print(f"📝 LOG GUARDADO: {telefono} - {duracion}s - {estado}")
        print(f"   Notas: {notas[:100]}...")
        return True


async def test_ami_connection():
    """Prueba la conexión AMI básica."""
    print("🔌 PRUEBA DE CONEXIÓN AMI")
    print("=" * 50)
    
    try:
        config = Config()
        
        # Verificar configuración AMI
        if not config.asterisk.validate():
            print("❌ Configuración AMI incompleta")
            print(f"   Host: {config.asterisk.host}")
            print(f"   Puerto: {config.asterisk.ami_port}")
            print(f"   Usuario: {config.asterisk.ami_user}")
            print(f"   Secret: {'***' if config.asterisk.ami_secret else 'NO CONFIGURADO'}")
            return False
        
        print(f"✅ Configuración AMI válida:")
        print(f"   Host: {config.asterisk.host}:{config.asterisk.ami_port}")
        print(f"   Usuario: {config.asterisk.ami_user}")
        
        # Intentar conexión
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            print("✅ Conexión AMI exitosa")
            
            # Probar comando básico
            channel_info = await ami.find_channel_by_uuid("test-uuid")
            print(f"📋 Búsqueda de canal: {channel_info or 'No encontrado (normal en prueba)'}")
            
            await ami.disconnect()
            return True
        else:
            print("❌ Error conectando a AMI")
            return False
            
    except Exception as e:
        print(f"❌ Error en prueba AMI: {e}")
        return False


async def test_farewell_messages():
    """Prueba la generación de mensajes de despedida."""
    print("\n📢 PRUEBA DE MENSAJES DE DESPEDIDA")
    print("=" * 50)
    
    config = Config()
    db_manager = MockDBManager()
    logger = logging.getLogger(__name__)
    
    call_manager = CallManager(config, db_manager, logger)
    
    # Datos de compromiso de prueba
    commitment_data = {
        'monto_acordado': 2350.50,
        'fecha_compromiso': '2025-06-10',
        'compromiso_id': 123
    }
    
    # Prueba 1: Mensaje de compromiso exitoso
    message1 = call_manager._generate_farewell_message(
        CallEndReason.COMPROMISO_OBTENIDO,
        client_name="Juan Pérez",
        commitment_data=commitment_data
    )
    print(f"🎉 Compromiso exitoso:")
    print(f"   '{message1}'")
    
    # Prueba 2: Despedida normal
    message2 = call_manager._generate_farewell_message(
        CallEndReason.DESPEDIDA_NORMAL,
        client_name="María González"
    )
    print(f"\n👋 Despedida normal:")
    print(f"   '{message2}'")
    
    # Prueba 3: Error técnico
    message3 = call_manager._generate_farewell_message(
        CallEndReason.ERROR_TECNICO,
        client_name="Carlos Ruiz"
    )
    print(f"\n⚠️ Error técnico:")
    print(f"   '{message3}'")
    
    return True


async def test_call_termination_logic():
    """Prueba la lógica de detección de terminación."""
    print("\n🔍 PRUEBA DE LÓGICA DE TERMINACIÓN")
    print("=" * 50)
    
    config = Config()
    db_manager = MockDBManager()
    logger = logging.getLogger(__name__)
    
    call_manager = CallManager(config, db_manager, logger)
    
    # Frases de prueba
    test_phrases = [
        ("Gracias por su tiempo, que tenga buen día.", True),
        ("Hasta luego, nos comunicaremos pronto.", True),
        ("Adiós, que esté bien.", True),
        ("¿Puede pagar mañana?", False),
        ("Su deuda es de 1500 soles.", False),
        ("Perfecto, confirmo su compromiso. Que tenga buen día.", True),
        ("Lo contactaremos nuevamente. Hasta luego.", True),
    ]
    
    print("📋 Frases de prueba:")
    for phrase, expected in test_phrases:
        result = call_manager.should_end_conversation(phrase)
        status = "✅" if result == expected else "❌"
        print(f"   {status} '{phrase}' -> {result} (esperado: {expected})")
    
    return True


async def test_complete_flow():
    """Prueba el flujo completo de finalización."""
    print("\n🔄 PRUEBA DE FLUJO COMPLETO")
    print("=" * 50)
    
    config = Config()
    db_manager = MockDBManager()
    logger = logging.getLogger(__name__)
    
    call_manager = CallManager(config, db_manager, logger)
    
    # Crear sesión mock
    session = MockSession("test-session-123", "987654321")
    
    # Datos de compromiso
    commitment_data = {
        'monto_acordado': 1500.0,
        'fecha_compromiso': '2025-06-10',
        'compromiso_id': 456,
        'deuda_id': 1
    }
    
    print("📋 Simulando compromiso exitoso...")
    
    # Simular manejo de compromiso exitoso
    # Nota: No ejecutará el hangup real porque no hay canal activo
    try:
        result = await call_manager.handle_commitment_success(session, commitment_data)
        print(f"✅ Resultado: {result}")
    except Exception as e:
        print(f"⚠️ Error esperado (sin canal real): {e}")
    
    print("\n📋 Simulando despedida normal...")
    
    # Simular despedida normal
    try:
        result = await call_manager.handle_conversation_end(session, CallEndReason.DESPEDIDA_NORMAL)
        print(f"✅ Resultado: {result}")
    except Exception as e:
        print(f"⚠️ Error esperado (sin canal real): {e}")
    
    return True


async def run_all_tests():
    """Ejecuta todas las pruebas del sistema de terminación."""
    print("🚀 INICIANDO PRUEBAS DEL SISTEMA DE TERMINACIÓN DE LLAMADAS")
    print("=" * 80)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
    
    results = {}
    
    try:
        # Prueba 1: Conexión AMI
        results['ami_connection'] = await test_ami_connection()
        
        # Prueba 2: Mensajes de despedida
        results['farewell_messages'] = await test_farewell_messages()
        
        # Prueba 3: Lógica de terminación
        results['termination_logic'] = await test_call_termination_logic()
        
        # Prueba 4: Flujo completo
        results['complete_flow'] = await test_complete_flow()
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Resumen
    print("\n" + "=" * 80)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 80)
    
    ami_ok = results.get('ami_connection', False)
    farewell_ok = results.get('farewell_messages', False)
    logic_ok = results.get('termination_logic', False)
    flow_ok = results.get('complete_flow', False)
    
    print(f"🔌 Conexión AMI: {'✅ EXITOSA' if ami_ok else '❌ FALLIDA'}")
    print(f"📢 Mensajes de despedida: {'✅ CORRECTOS' if farewell_ok else '❌ ERROR'}")
    print(f"🔍 Lógica de terminación: {'✅ FUNCIONAL' if logic_ok else '❌ ERROR'}")
    print(f"🔄 Flujo completo: {'✅ SIMULADO' if flow_ok else '❌ ERROR'}")
    
    if all([ami_ok, farewell_ok, logic_ok, flow_ok]):
        print(f"\n🎉 TODAS LAS PRUEBAS PASARON")
        print(f"   - El sistema está listo para finalizar llamadas automáticamente")
        print(f"   - Los compromisos exitosos activarán despedida y corte")
        print(f"   - Las despedidas normales también cortarán la llamada")
    else:
        print(f"\n⚠️ ALGUNAS PRUEBAS FALLARON")
        print(f"   - Revisar configuración AMI si falló la conexión")
        print(f"   - Verificar lógica de detección si falló terminación")
    
    return all([ami_ok, farewell_ok, logic_ok, flow_ok])


if __name__ == "__main__":
    # Ejecutar pruebas
    asyncio.run(run_all_tests())
