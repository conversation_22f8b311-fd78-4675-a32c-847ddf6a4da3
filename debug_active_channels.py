#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script para debuggear canales activos durante una llamada AudioSocket.
Ejecutar DURANTE una llamada activa para ver qué canales están disponibles.
"""

import sys
import os
import asyncio
import logging

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.asterisk_ami import AsteriskAMI, AMIConfig
from src.config import Config


async def debug_active_channels():
    """Debuggea todos los canales activos con detalles completos."""
    print("🔍 DEBUG DE CANALES ACTIVOS DURANTE LLAMADA")
    print("=" * 60)
    print("⚠️ EJECUTAR ESTE SCRIPT DURANTE UNA LLAMADA AUDIOSOCKET ACTIVA")
    print("=" * 60)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            print("✅ Conectado a AMI para debugging")
            
            # Obtener información detallada de todos los canales
            print(f"\n📋 INFORMACIÓN DETALLADA DE CANALES:")
            print("-" * 60)
            
            action_id = ami._get_action_id()
            
            # Comando CoreShowChannels con más detalles
            channels_cmd = (
                f"Action: CoreShowChannels\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )
            
            ami.writer.write(channels_cmd.encode())
            await ami.writer.drain()
            
            channels_found = []
            channel_count = 0
            
            # Leer todas las respuestas
            while True:
                response = await ami._read_response()
                if not response:
                    break
                
                if 'channel' in response:
                    channel_count += 1
                    channel_name = response.get('channel', '')
                    channels_found.append(response)
                    
                    print(f"\n📞 CANAL #{channel_count}: {channel_name}")
                    print(f"   Estado: {response.get('channelstate', 'N/A')} ({response.get('channelstatedesc', 'N/A')})")
                    print(f"   Contexto: {response.get('context', 'N/A')}")
                    print(f"   Extensión: {response.get('extension', 'N/A')}")
                    print(f"   Prioridad: {response.get('priority', 'N/A')}")
                    print(f"   Caller ID: {response.get('calleridnum', 'N/A')}")
                    print(f"   Caller Name: {response.get('calleridname', 'N/A')}")
                    print(f"   Connected Line: {response.get('connectedlinenum', 'N/A')}")
                    print(f"   Unique ID: {response.get('uniqueid', 'N/A')}")
                    print(f"   Linked ID: {response.get('linkedid', 'N/A')}")
                    print(f"   Application: {response.get('application', 'N/A')}")
                    print(f"   Data: {response.get('applicationdata', 'N/A')}")
                    print(f"   Duration: {response.get('duration', 'N/A')}")
                    print(f"   Bridge ID: {response.get('bridgeid', 'N/A')}")
                    
                    # Identificar tipo de canal
                    if 'audiosocket' in channel_name.lower():
                        print(f"   🎯 TIPO: Canal AudioSocket")
                    elif 'pjsip' in channel_name.lower():
                        print(f"   📞 TIPO: Canal SIP")
                    elif 'local' in channel_name.lower():
                        print(f"   🔗 TIPO: Canal Local")
                    else:
                        print(f"   ❓ TIPO: Otro")
                
                # Si llegamos al final de la lista
                if response.get('event') == 'CoreShowChannelsComplete':
                    break
            
            print(f"\n📊 RESUMEN:")
            print(f"   Total de canales activos: {channel_count}")
            
            # Analizar canales por tipo
            audiosocket_channels = [ch for ch in channels_found if 'audiosocket' in ch.get('channel', '').lower()]
            pjsip_channels = [ch for ch in channels_found if 'pjsip' in ch.get('channel', '').lower()]
            local_channels = [ch for ch in channels_found if 'local' in ch.get('channel', '').lower()]
            avr_context_channels = [ch for ch in channels_found if ch.get('context', '') == 'avr']
            
            print(f"   Canales AudioSocket: {len(audiosocket_channels)}")
            print(f"   Canales PJSIP: {len(pjsip_channels)}")
            print(f"   Canales Local: {len(local_channels)}")
            print(f"   Canales en contexto 'avr': {len(avr_context_channels)}")
            
            # Recomendaciones para hangup
            print(f"\n🎯 RECOMENDACIONES PARA HANGUP:")
            print("-" * 40)
            
            if audiosocket_channels:
                print(f"✅ Usar canales AudioSocket para hangup:")
                for ch in audiosocket_channels:
                    print(f"   - {ch.get('channel')}")
            elif avr_context_channels:
                print(f"✅ Usar canales en contexto 'avr' para hangup:")
                for ch in avr_context_channels:
                    print(f"   - {ch.get('channel')} (Estado: {ch.get('channelstatedesc')})")
            elif len(channels_found) == 1:
                print(f"⚠️ Solo hay 1 canal activo - Usar con precaución:")
                print(f"   - {channels_found[0].get('channel')}")
            else:
                print(f"❌ No se puede determinar canal seguro para hangup")
                print(f"   Múltiples canales sin identificación clara")
            
            # Mostrar información de bridge si existe
            bridges = set()
            for ch in channels_found:
                bridge_id = ch.get('bridgeid', '')
                if bridge_id and bridge_id != '<none>':
                    bridges.add(bridge_id)
            
            if bridges:
                print(f"\n🌉 BRIDGES ACTIVOS:")
                for bridge_id in bridges:
                    print(f"   Bridge ID: {bridge_id}")
                    bridge_channels = [ch for ch in channels_found if ch.get('bridgeid') == bridge_id]
                    for ch in bridge_channels:
                        print(f"     - {ch.get('channel')}")
            
            await ami.disconnect()
            return True
            
        else:
            print("❌ No se pudo conectar a AMI")
            return False
            
    except Exception as e:
        print(f"❌ Error en debugging: {e}")
        import traceback
        traceback.print_exc()
        return False


async def monitor_channels_continuously():
    """Monitorea canales continuamente cada 5 segundos."""
    print(f"\n🔄 MONITOREO CONTINUO DE CANALES")
    print("=" * 60)
    print("Presiona Ctrl+C para detener")
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        iteration = 0
        
        while True:
            iteration += 1
            print(f"\n📊 ITERACIÓN #{iteration} - {asyncio.get_event_loop().time()}")
            print("-" * 30)
            
            ami = AsteriskAMI(ami_config)
            
            if await ami.connect():
                channels = await ami._get_all_channels()
                
                if channels:
                    print(f"📞 {len(channels)} canales activos:")
                    for i, channel in enumerate(channels, 1):
                        print(f"   {i}. {channel}")
                else:
                    print("📞 No hay canales activos")
                
                await ami.disconnect()
            else:
                print("❌ Error conectando a AMI")
            
            # Esperar 5 segundos
            await asyncio.sleep(5)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Monitoreo detenido por usuario")
    except Exception as e:
        print(f"❌ Error en monitoreo: {e}")


async def main():
    """Función principal."""
    print("🚀 HERRAMIENTA DE DEBUG DE CANALES ASTERISK")
    print("=" * 80)
    
    print("Selecciona una opción:")
    print("1. Debug detallado de canales activos")
    print("2. Monitoreo continuo de canales")
    print("3. Ambos")
    
    # Para automatizar, usar opción 1
    choice = "1"
    
    if choice in ["1", "3"]:
        await debug_active_channels()
    
    if choice in ["2", "3"]:
        await monitor_channels_continuously()


if __name__ == "__main__":
    asyncio.run(main())
