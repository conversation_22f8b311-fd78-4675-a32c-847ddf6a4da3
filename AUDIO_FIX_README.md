# Solución para Problemas de Audio TTS en AudioSocket

## Problema Identificado

El sistema presenta problemas donde el audio TTS se corta durante las interacciones (solo se escucha la parte final), aunque el saludo inicial funciona correctamente. El análisis de los logs muestra:

```
EXCEPCIÓN - Conexión reseteada durante el envío de audio: Connection lost. Marcando audio_interrupted = True.
```

## Causa Raíz

1. **Conexiones inestables**: La conexión AudioSocket se resetea durante el envío de chunks de audio
2. **Buffers grandes**: Chunks de 8KB causan timeouts en conexiones lentas
3. **Falta de reintentos**: No hay mecanismo de recuperación ante fallos de conexión
4. **Configuración de socket subóptima**: Sin optimizaciones TCP para streaming de audio

## Soluciones Implementadas

### 1. Optimización de Chunks de Audio

**Antes:**
```python
chunk_size = self.config.audiosocket.buffer_size  # 8192 bytes
```

**Después:**
```python
chunk_size = self.config.audiosocket.buffer_size  # 1024 bytes (configurable)
```

### 2. Sistema de Reintentos Robusto

```python
max_retries = 3
retry_delay = 0.1  # Con backoff exponencial
chunk_timeout = 2.0  # Timeout por chunk
```

### 3. Configuraciones de Socket Optimizadas

```python
# Configuraciones TCP para mejor estabilidad
sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
sock.setsockopt(socket.SOL_TCP, socket.TCP_NODELAY, 1)
sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)  # 64KB
sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)  # 64KB
```

### 4. Verificación de Salud de Conexión

```python
def _is_connection_healthy(self, writer: asyncio.StreamWriter) -> bool:
    """Verifica si la conexión está saludable antes del envío."""
```

## Configuración Recomendada

Actualizar el archivo `.env` con estas configuraciones optimizadas:

```bash
# AudioSocket - Configuración optimizada para estabilidad
AUDIOSOCKET_BUFFER_SIZE=1024              # Chunks pequeños
AUDIOSOCKET_CHUNK_TIMEOUT=2.0             # Timeout por chunk
AUDIOSOCKET_MAX_RETRIES=3                 # Reintentos máximos
AUDIOSOCKET_RETRY_DELAY=0.1               # Delay entre reintentos
AUDIOSOCKET_CONNECTION_TIMEOUT=10.0       # Timeout de conexión
AUDIOSOCKET_CHUNK_PAUSE_INTERVAL=10       # Pausa cada 10 chunks
AUDIOSOCKET_CHUNK_PAUSE_DURATION=0.001    # 1ms de pausa
```

## Herramientas de Diagnóstico

### 1. Script de Diagnóstico

```bash
python scripts/diagnose_audio_connection.py --host 127.0.0.1 --port 5001
```

**Funciones:**
- Prueba conectividad básica
- Verifica configuraciones de socket
- Simula envío de audio
- Prueba múltiples conexiones

### 2. Monitor de Salud

```bash
python scripts/monitor_audio_health.py --interval 5.0
```

**Funciones:**
- Monitoreo en tiempo real
- Detección de conexiones no saludables
- Métricas de performance
- Reportes JSON

## Pasos para Implementar la Solución

### 1. Actualizar Configuración

```bash
# Copiar configuración optimizada
cp .env.example .env
# Editar .env con las nuevas configuraciones
```

### 2. Reiniciar el Servicio

```bash
# Detener el servicio actual
pkill -f audiosocket_server.py

# Iniciar con nueva configuración
cd /opt/cobranza-bot/venv
source bin/activate
python src/main.py
```

### 3. Verificar Funcionamiento

```bash
# Ejecutar diagnóstico
python scripts/diagnose_audio_connection.py

# Monitorear en tiempo real
python scripts/monitor_audio_health.py
```

### 4. Probar con Llamada Real

1. Realizar llamada al anexo 8000
2. Verificar que el saludo se escuche completo
3. Interactuar y verificar que las respuestas se escuchen completas
4. Revisar logs para confirmar ausencia de errores de conexión

## Monitoreo de Logs

### Logs Exitosos (Esperados)

```
SEND_AUDIO_TO_ASTERISK: Envío completado exitosamente - 300/300 chunks.
TTS_DEBUG: Audio generado: 275 caracteres -> 282090 bytes en 2.825s
```

### Logs de Problema (A Evitar)

```
EXCEPCIÓN - Conexión reseteada durante el envío de audio: Connection lost
Timeout enviando chunk X
```

## Métricas de Performance Esperadas

- **Tiempo por chunk**: < 0.1s
- **Tasa de error**: < 1%
- **Reintentos exitosos**: > 95%
- **Latencia total**: < 3s para respuestas de 300 caracteres

## Troubleshooting

### Si el problema persiste:

1. **Verificar red**:
   ```bash
   ping 127.0.0.1
   netstat -an | grep 5001
   ```

2. **Reducir chunk size**:
   ```bash
   AUDIOSOCKET_BUFFER_SIZE=512  # Probar con 512 bytes
   ```

3. **Aumentar timeouts**:
   ```bash
   AUDIOSOCKET_CHUNK_TIMEOUT=5.0  # Aumentar a 5 segundos
   ```

4. **Verificar Asterisk**:
   ```bash
   asterisk -rx "core show channels"
   asterisk -rx "module show like audiosocket"
   ```

## Contacto y Soporte

Para problemas adicionales:
1. Revisar logs detallados en `/var/log/cobranza-bot.log`
2. Ejecutar herramientas de diagnóstico
3. Verificar configuración de red y Asterisk
4. Considerar ajustes específicos del entorno

---

**Nota**: Esta solución ha sido optimizada para entornos con conexiones de red variables y prioriza la estabilidad sobre la velocidad máxima de transferencia.
