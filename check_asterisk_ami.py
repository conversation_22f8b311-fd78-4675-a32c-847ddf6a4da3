#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script para verificar la configuración de Asterisk AMI.
"""

import asyncio
import socket
import sys
import os

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def check_asterisk_service():
    """Verifica si Asterisk está ejecutándose."""
    print("🔍 VERIFICANDO SERVICIO ASTERISK")
    print("=" * 50)
    
    try:
        # Verificar proceso Asterisk
        import subprocess
        result = subprocess.run(['pgrep', '-f', 'asterisk'], capture_output=True, text=True)
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"✅ Asterisk ejecutándose (PIDs: {', '.join(pids)})")
            
            # Verificar versión
            version_result = subprocess.run(['asterisk', '-V'], capture_output=True, text=True)
            if version_result.returncode == 0:
                print(f"📋 Versión: {version_result.stdout.strip()}")
            
            return True
        else:
            print("❌ Asterisk NO está ejecutándose")
            print("   Ejecutar: sudo systemctl start asterisk")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando Asterisk: {e}")
        return False


async def check_ami_port():
    """Verifica si el puerto AMI está abierto."""
    print("\n🔌 VERIFICANDO PUERTO AMI")
    print("=" * 50)
    
    host = 'localhost'
    port = 5038
    
    try:
        # Intentar conexión TCP básica
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Puerto {port} está abierto en {host}")
            return True
        else:
            print(f"❌ Puerto {port} NO está abierto en {host}")
            print("   Verificar configuración en /etc/asterisk/manager.conf")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando puerto: {e}")
        return False


async def check_ami_banner():
    """Verifica el banner AMI de Asterisk."""
    print("\n📡 VERIFICANDO BANNER AMI")
    print("=" * 50)
    
    try:
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection('localhost', 5038),
            timeout=10
        )
        
        # Leer banner
        banner_line = await asyncio.wait_for(reader.readline(), timeout=5)
        banner = banner_line.decode().strip()
        
        print(f"📋 Banner recibido: '{banner}'")
        
        if 'Asterisk Call Manager' in banner:
            print("✅ Banner AMI válido")
            success = True
        else:
            print("❌ Banner AMI inválido")
            print("   Esperado: algo que contenga 'Asterisk Call Manager'")
            success = False
        
        writer.close()
        await writer.wait_closed()
        
        return success
        
    except asyncio.TimeoutError:
        print("❌ Timeout conectando a AMI")
        return False
    except Exception as e:
        print(f"❌ Error verificando banner: {e}")
        return False


async def check_ami_config_files():
    """Verifica archivos de configuración AMI."""
    print("\n📁 VERIFICANDO ARCHIVOS DE CONFIGURACIÓN")
    print("=" * 50)
    
    config_files = [
        '/etc/asterisk/manager.conf',
        '/etc/asterisk/asterisk.conf',
        '/etc/asterisk/extensions.conf'
    ]
    
    for config_file in config_files:
        try:
            if os.path.exists(config_file):
                print(f"✅ {config_file} existe")
                
                # Verificar permisos de lectura
                if os.access(config_file, os.R_OK):
                    print(f"   📖 Permisos de lectura: OK")
                    
                    # Para manager.conf, verificar contenido básico
                    if 'manager.conf' in config_file:
                        with open(config_file, 'r') as f:
                            content = f.read()
                            
                        if 'enabled = yes' in content:
                            print(f"   ✅ AMI habilitado")
                        else:
                            print(f"   ❌ AMI NO habilitado (falta 'enabled = yes')")
                            
                        if '[avr]' in content:
                            print(f"   ✅ Usuario 'avr' configurado")
                        else:
                            print(f"   ❌ Usuario 'avr' NO configurado")
                            
                else:
                    print(f"   ❌ Sin permisos de lectura")
            else:
                print(f"❌ {config_file} NO existe")
                
        except Exception as e:
            print(f"❌ Error verificando {config_file}: {e}")


def show_ami_config_example():
    """Muestra ejemplo de configuración AMI."""
    print("\n📝 CONFIGURACIÓN AMI RECOMENDADA")
    print("=" * 50)
    
    manager_conf = """
# /etc/asterisk/manager.conf
[general]
enabled = yes
port = 5038
bindaddr = 127.0.0.1

[avr]
secret = avr
deny = 0.0.0.0/0.0.0.0
permit = 127.0.0.1/255.255.255.0
read = system,call,log,verbose,command,agent,user,config,command,dtmf,reporting,cdr,dialplan
write = system,call,log,verbose,command,agent,user,config,command,dtmf,reporting,cdr,dialplan
"""
    
    print(manager_conf)
    
    print("\n🔧 COMANDOS PARA APLICAR:")
    print("sudo nano /etc/asterisk/manager.conf")
    print("sudo systemctl reload asterisk")
    print("sudo asterisk -rx 'manager reload'")


async def test_ami_login():
    """Prueba login AMI con credenciales."""
    print("\n🔐 PROBANDO LOGIN AMI")
    print("=" * 50)
    
    try:
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection('localhost', 5038),
            timeout=10
        )
        
        # Leer banner
        banner = await reader.readline()
        print(f"📋 Banner: {banner.decode().strip()}")
        
        # Enviar login
        login_cmd = (
            "Action: Login\r\n"
            "Username: avr\r\n"
            "Secret: avr\r\n"
            "ActionID: test_login\r\n"
            "\r\n"
        )
        
        writer.write(login_cmd.encode())
        await writer.drain()
        
        # Leer respuesta
        response_lines = []
        while True:
            line = await asyncio.wait_for(reader.readline(), timeout=5)
            line_str = line.decode().strip()
            
            if not line_str:  # Línea vacía indica fin de respuesta
                break
                
            response_lines.append(line_str)
            print(f"📨 {line_str}")
        
        # Verificar respuesta
        response_text = '\n'.join(response_lines)
        if 'Response: Success' in response_text:
            print("✅ Login AMI exitoso")
            success = True
        else:
            print("❌ Login AMI fallido")
            success = False
        
        writer.close()
        await writer.wait_closed()
        
        return success
        
    except Exception as e:
        print(f"❌ Error en login AMI: {e}")
        return False


async def main():
    """Función principal de verificación."""
    print("🚀 DIAGNÓSTICO COMPLETO DE ASTERISK AMI")
    print("=" * 80)
    
    results = {}
    
    # Verificaciones
    results['asterisk_service'] = await check_asterisk_service()
    results['ami_port'] = await check_ami_port()
    results['ami_banner'] = await check_ami_banner()
    results['ami_login'] = await test_ami_login()
    
    # Verificar archivos de configuración
    await check_ami_config_files()
    
    # Mostrar configuración recomendada
    show_ami_config_example()
    
    # Resumen final
    print("\n" + "=" * 80)
    print("📊 RESUMEN DEL DIAGNÓSTICO")
    print("=" * 80)
    
    service_ok = results.get('asterisk_service', False)
    port_ok = results.get('ami_port', False)
    banner_ok = results.get('ami_banner', False)
    login_ok = results.get('ami_login', False)
    
    print(f"🔧 Servicio Asterisk: {'✅ EJECUTÁNDOSE' if service_ok else '❌ DETENIDO'}")
    print(f"🔌 Puerto AMI (5038): {'✅ ABIERTO' if port_ok else '❌ CERRADO'}")
    print(f"📡 Banner AMI: {'✅ VÁLIDO' if banner_ok else '❌ INVÁLIDO'}")
    print(f"🔐 Login AMI: {'✅ EXITOSO' if login_ok else '❌ FALLIDO'}")
    
    if all([service_ok, port_ok, banner_ok, login_ok]):
        print(f"\n🎉 AMI COMPLETAMENTE FUNCIONAL")
        print(f"   El sistema de corte de llamada debería funcionar correctamente")
    else:
        print(f"\n⚠️ PROBLEMAS DETECTADOS EN AMI")
        if not service_ok:
            print(f"   - Iniciar Asterisk: sudo systemctl start asterisk")
        if not port_ok or not banner_ok:
            print(f"   - Verificar configuración en /etc/asterisk/manager.conf")
        if not login_ok:
            print(f"   - Verificar credenciales de usuario 'avr'")


if __name__ == "__main__":
    asyncio.run(main())
