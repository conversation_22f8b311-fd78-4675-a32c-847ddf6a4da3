#!/usr/bin/env bash
# -*- coding: utf-8 -*-

# ============================================================================
# Bot de Cobranza con IA - Archivo .gitignore
# ============================================================================
# Archivo para excluir archivos sensibles y temporales del control de versiones
# Entorno: /opt/cobranza-bot/venv
#
# Autor: Sistema de Cobranza Automatizada
# Fecha: 2024
# ============================================================================

# ===================
# ARCHIVOS SENSIBLES
# ===================

# Variables de entorno con credenciales
.env
.env.local
.env.production
.env.staging

# Archivos de configuración con secretos
config/secrets.yaml
config/production.yaml
secrets/

# Claves y certificados
*.key
*.pem
*.crt
*.p12
*.pfx

# ===================
# PYTHON
# ===================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===================
# LOGS Y TEMPORALES
# ===================

# Log files
*.log
logs/
*.log.*

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Audio files temporales
*.wav
*.mp3
*.ogg
*.flac
audio_temp/
recordings/

# ===================
# BASES DE DATOS
# ===================

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal

# Database dumps
*.sql
*.dump
backups/

# ===================
# IDEs Y EDITORES
# ===================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================
# SISTEMA OPERATIVO
# ===================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# ===================
# ESPECÍFICOS DEL PROYECTO
# ===================

# Archivos de configuración local
config/local.yaml
config/development.yaml

# Datos de prueba
test_data/
sample_audio/

# Archivos de monitoreo
monitoring/
metrics/

# Cache de APIs
api_cache/
.cache/

# Archivos de sesión
sessions/
*.session

# ===================
# DOCKER
# ===================

# Docker
.dockerignore
Dockerfile.local
docker-compose.override.yml

# ===================
# OTROS
# ===================

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Node modules (si se usa alguna herramienta de Node)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*