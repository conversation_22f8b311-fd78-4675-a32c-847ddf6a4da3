# MEMORIA DEL PROYECTO: BOT DE COBRANZA CON IA
# ================================================
# Archivo de contexto para desarrollo del sistema de llamadas automatizadas
# Fecha de creación: $(date +%Y-%m-%d)
# Entorno: Desarrollo Cloud en /opt/cobranza-bot/venv

## RESUMEN DEL PROYECTO
- **Objetivo**: Bot de llamadas automatizado para gestión de cobranza
## TECNOLOGÍAS Y HERRAMIENTAS
- **Backend**: Python 3.9+ con asyncio
- **Base de Datos**: PostgreSQL con asyncpg
- **Asterisk**: v21 con app_audiosocket.so
- **ASR (Speech-to-Text)**: Deepgram API (streaming)
- **TTS (Text-to-Speech)**: ElevenLabs API (streaming)
- **LLM**: OpenAI GPT-4 (streaming)
- **Audio**: AudioSocket streaming, pyaudio
- **Async**: aiohttp, websockets, uvloop

- **Tecnologías**: Asterisk 22 + PostgreSQL + Python + IA (OpenAI + ElevenLabs)
- **Anexo**: 8000 (punto de entrada para llamadas)
- **Comunicación**: AudioSocket en tiempo real (sin grabaciones previas)

## ARQUITECTURA TÉCNICA

### Componentes Principales:
1. **Asterisk Server**: Puerto 5001 para AudioSocket
2. **Python Application**: Servidor de procesamiento de audio
3. **PostgreSQL**: Base de datos de clientes y deudas
4. **APIs Externas**: ElevenLabs (TTS/STT) + OpenAI (IA conversacional)

### Dialplan Asterisk:
```
exten => 8000,1,GoSub(avr,s,1(127.0.0.1:5001))

[avr]
exten => s,1,Answer()
 same => n,Ringing()
 same => n,Wait(1)
 same => n,Set(UUID=${SHELL(uuidgen | tr -d '\n')})
 same => n,AudioSocket(${UUID},${ARG1})
 same => n,Hangup()
```

## ENTORNO DE DESARROLLO
- **Local**: /Users/<USER>/Sites/autobot/ (solo desarrollo)
- **Cloud**: /opt/cobranza-bot/venv (entorno virtual para testing)
- **OS**: Linux Debian 12 (servidor) / macOS (desarrollo local)

## ESTRUCTURA DEL PROYECTO
```
autobot/
├── src/
│   ├── audiosocket_server.py    # Servidor principal AudioSocket
│   ├── stt_handler.py           # Manejo de Speech-to-Text
│   ├── tts_handler.py           # Manejo de Text-to-Speech
│   ├── ai_conversation.py       # Motor de IA conversacional
│   ├── database.py              # Conexión y consultas DB
│   └── config.py                # Configuraciones
├── sql/
│   └── schema.sql               # Esquema de base de datos
├── requirements.txt             # Dependencias Python
├── .env                         # Variables de entorno
├── .memory                      # Este archivo de contexto
└── README.md                    # Documentación
```

## DEPENDENCIAS PRINCIPALES
```
psycopg2-binary>=2.9.0    # PostgreSQL
openai>=1.0.0             # API OpenAI
google-cloud-texttospeech>=2.16.3         # google
asyncio                   # Programación asíncrona
wave                      # Procesamiento de audio
struct                    # Manejo de datos binarios
logging                   # Sistema de logs
```

## ESQUEMA DE BASE DE DATOS
```sql
-- Tabla de clientes
CREATE TABLE clientes (
    id SERIAL PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    telefono VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de deudas
CREATE TABLE deudas (
    id SERIAL PRIMARY KEY,
    cliente_id INTEGER REFERENCES clientes(id),
    monto DECIMAL(10,2) NOT NULL,
    fecha_vencimiento DATE NOT NULL,
    estado VARCHAR(50) DEFAULT 'pendiente',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de compromisos de pago
CREATE TABLE compromisos_pago (
    id SERIAL PRIMARY KEY,
    deuda_id INTEGER REFERENCES deudas(id),
    fecha_compromiso DATE NOT NULL,
    monto_acordado DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de logs de llamadas
CREATE TABLE logs_llamadas (
    id SERIAL PRIMARY KEY,
    cliente_id INTEGER REFERENCES clientes(id),
    uuid_llamada VARCHAR(255) NOT NULL,
    transcripcion TEXT,
    resultado VARCHAR(100),
    duracion INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## FLUJO DE CONVERSACIÓN
1. **Saludo**: Identificación del bot y solicitud de datos
2. **Validación**: Verificar identidad del cliente en BD
3. **Consulta**: Obtener información de deuda y vencimiento
4. **Negociación**: Solicitar compromiso de pago
5. **Confirmación**: Registrar acuerdo en base de datos
6. **Cierre**: Despedida y finalización de llamada

## CONFIGURACIONES CRÍTICAS
- **Latencia objetivo**: <500ms para conversación natural
- **Interrupciones**: Permitir que cliente interrumpa al bot
- **Seguridad**: No exponer datos sensibles en logs
- **Escalabilidad**: Diseño para múltiples llamadas simultáneas

## VARIABLES DE ENTORNO REQUERIDAS
```
# APIs
OPENAI_API_KEY=sk-...
ELEVENLABS_API_KEY=...

# Base de datos
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cobranza
DB_USER=...
DB_PASSWORD=...

# AudioSocket
AUDIOSOCKET_HOST=127.0.0.1
AUDIOSOCKET_PORT=5001

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/cobranza-bot.log
```

## NOTAS DE IMPLEMENTACIÓN
- Usar cabeceras estándar en todos los archivos Python
- Implementar manejo robusto de errores
- Logging detallado para debugging
- Validación exhaustiva de datos de entrada
- Optimización de performance para tiempo real

## PRÓXIMOS PASOS
1. Configurar entorno virtual en /opt/cobranza-bot/venv
2. Crear estructura básica del proyecto
3. Implementar servidor AudioSocket
4. Integrar APIs de TTS/STT
5. Desarrollar motor de IA conversacional
6. Testing e integración con Asterisk

## CONTACTOS Y RECURSOS
- Documentación AudioSocket: https://wiki.asterisk.org/wiki/display/AST/AudioSocket
- API ElevenLabs: https://docs.elevenlabs.io/
- API OpenAI: https://platform.openai.com/docs/

---
Este archivo debe actualizarse con cada cambio significativo del proyecto.