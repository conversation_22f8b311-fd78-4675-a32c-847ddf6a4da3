# Environment
- User cannot test locally - all files are in cloud environment and testing must be done there.
- User prefers no test files to be created during development work.

# AudioSocket Implementation Issues
- User is considering abandoning Python for Node.js due to persistent audio transmission issues in the AudioSocket implementation.
- AudioSocket audio transmission has severe delay issues - audio doesn't reach the caller until 10+ seconds after being sent, indicating fundamental protocol or connection problems beyond just silence padding.
- Current Python implementation uses batch processing causing audio quality drops, delays, and premature call termination - streaming architecture is required for proper AudioSocket performance.

# Working AudioSocket Comparison
- User has a working proprietary AudioSocket solution called 'infra_avr' on the same platform that works excellently with real-time audio transmission, confirming AudioSocket and platform are capable of proper performance.
- User's working infra_avr solution uses true streaming mode for audio transmission.
- User has additional obfuscated files from working infra_avr solution (index.js, logger.js, tts.js) that may contain implementation patterns for AudioSocket audio transmission.
- infra_avr AudioSocket optimal timing: 21.4ms average between chunks, 93.3 chunks/second, 320 bytes chunks, 29868 bytes/second for proper audio transmission without overlapping.

# AudioSocket Optimization & Features
- AudioSocket implementation with 21.4ms timing between chunks (matching infra_avr) and keepalive system successfully resolved audio mounting and Deepgram timeout issues.
- AudioSocket silence trimming optimization successfully reduced initial silence from 464ms to 62ms, improving AudioSocket audio transmission by removing excessive padding that was causing users to think audio wasn't playing.
- AudioSocket calls must validate debt using only DNI from cliente and deuda tables in database, and conversation response delays need optimization for better user experience.
- AudioSocket voice bots must implement proper audio interruption handling to stop TTS playback when user speaks, preventing desynchronized conversations where responses don't match user input timing, and should automatically hang up calls via AMI after generating and saving payment commitments.
- AudioSocket payment commitments obtained during calls should be saved to compromisos_pago table, and ASR streaming transcription delays need optimization for real-time performance along with LLM response optimization.
- AudioSocket payment commitment system now has optimized DateCalculator with pre-calculated phrase mapping, LRU cache, and 97.9% success rate for natural language date parsing - handles phrases like 'mañana', 'pasado mañana', 'próximo lunes', 'este jueves' with ultra-fast lookup.
- AudioSocket payment commitments require fast date calculation for natural language terms like 'mañana', 'pasado mañana', 'este jueves', 'próximo martes' - common phrases should be pre-calculated and cached for optimal performance.
- AudioSocket greeting should be simple and direct without contextual time-based greetings as they cause latency - use static greeting instead.
- AudioSocket system now has functional DNI extraction, corrected database query (placeholders $1 instead of %s), and fixed conversation states (NEGOCIANDO instead of nonexistent INFORMANDO), but saving payment commitments and other improvement adjustments still need to be corrected.
- AudioSocket system corrected: critical security flaw fixed - no longer gives false debt information without identified client, DNI search restricted to appropriate states, strict 8-digit validation, blocking of intents without identification.
- Infinite DNI loop solved: system now passes complete context of identified client to AI prompts, including ID, name and total debt, with explicit instructions not to ask for DNI again when client is already validated, and automatic direct response to continue conversation.
- Critical technical error fixed: variable 'texto_a_analizar' out of scope caused infinite "technical problem" loop - corrected by defining variable at the beginning of function, added detection of debt inquiries with keywords, system now responds directly to questions about debt when client is already identified.
- AudioSocket ASR optimized with pre-initialization at session start, nova-2 model for speed, and proper audio interruption that cancels TTS tasks when user speaks during bot playback.
- AudioSocket voice bots should store call logs when conversations end with farewells.
- AudioSocket system automatically hangs up calls via Asterisk AMI after successful payment commitments, sends personalized farewell messages, and logs call completion with commitment details.
- AudioSocket calls must actually hangup/terminate when despedida or compromiso conditions are met, not just simulate termination.
- AudioSocket hangup should identify and terminate only the specific channel for the current call session, not all active channels, to avoid disrupting other concurrent calls.
- AudioSocket AMI connection issues may be caused by test implementation problems rather than actual AMI configuration - diagnostic shows AMI is fully functional while tests report invalid banner errors.
- AudioSocket calls appear in AMI as PJSIP channels in 'avr' context with Application: AudioSocket and the session UUID in the Data field (format: UUID,127.0.0.1:5001).
- AudioSocket hangup system implemented with AMI integration: detects user goodbye phrases, generates farewell responses, identifies specific channel by UUID in ApplicationData field, executes precise hangup via Asterisk AMI, supports multiple concurrent calls safely, logs call completion - functioning with minor delay to be optimized later.
- AudioSocket system architecture: Asterisk generates UUID per call, Python uses Asterisk UUID as session_id, AMI identifies channels by UUID in ApplicationData field (format: UUID,127.0.0.1:5001), hangup executes only on specific channel matching exact UUID.
- AudioSocket hangup modules: asterisk_ami.py (AMI client with precise channel identification), call_manager.py (farewell and hangup manager), ai_conversation.py (early user goodbye detection), audio_processor.py (post-TTS hangup verification), connection_handler.py (uses Asterisk UUID as session_id).
- AudioSocket hangup flow: user says goodbye → immediate farewell response → AMI searches channels by UUID in ApplicationData → executes hangup only on specific channel → other concurrent calls remain intact → logs call completion with details.
- AudioSocket hangup security: system prevents mass hangup by requiring exact UUID match in ApplicationData, supports multiple concurrent calls safely, only terminates the specific session where user said goodbye, protects other active calls from accidental termination.
- AudioSocket hangup configuration: AMI enabled on port 5038 with user 'avr', channels appear as PJSIP in 'avr' context with Application: AudioSocket, system uses ApplicationData field to match UUID for precise channel identification and termination.

# Critical Policies
- CRÍTICO RESUELTO: Implementada doble validación de seguridad - Intent Processor ahora bloquea intents protegidos sin cliente identificado, y prompts instruyen explícitamente a no inventar información de deuda, eliminando completamente el riesgo de dar información falsa.
- Critical policy: Never provide false information or hallucinations about client data - if client is not found in database, no information should be given as this is sensitive financial data.

# Code Style Preferences
- User prefers source files to be no more than 400 lines for maintainability and wants large files refactored into separate components.