# MEMORIA DEL PROYECTO: BOT DE COBRANZA CON IA
# ================================================
# Archivo de contexto para desarrollo del sistema de llamadas automatizadas
# Fecha de creación: $(date +%Y-%m-%d)
# Entorno: Desarrollo Cloud en /opt/cobranza-bot/venv

## RESUMEN DEL PROYECTO
- **Objetivo**: Bot de llamadas automatizado para gestión de cobranza
## TECNOLOGÍAS Y HERRAMIENTAS
- **Backend**: Python 3.9+ con asyncio
- **Base de Datos**: PostgreSQL con asyncpg
- **Asterisk**: v21 con app_audiosocket.so
- **ASR (Speech-to-Text)**: Deepgram API (streaming)
- **TTS (Text-to-Speech)**: ElevenLabs API (streaming)
- **LLM**: OpenAI GPT-4 (streaming)
- **Audio**: AudioSocket streaming, pyaudio
- **Async**: aiohttp, websockets, uvloop

- **Tecnologías**: Asterisk 22 + PostgreSQL + Python + IA (OpenAI + ElevenLabs)
- **Anexo**: 8000 (punto de entrada para llamadas)
- **Comunicación**: AudioSocket en tiempo real (sin grabaciones previas)

## ARQUITECTURA TÉCNICA

### Componentes Principales:
1. **Asterisk Server**: Puerto 5001 para AudioSocket
2. **Python Application**: Servidor de procesamiento de audio
3. **PostgreSQL**: Base de datos de clientes y deudas
4. **APIs Externas**: ElevenLabs (TTS/STT) + OpenAI (IA conversacional)

### Dialplan Asterisk:
```
exten => 8000,1,GoSub(avr,s,1(127.0.0.1:5001))

[avr]
exten => s,1,Answer()
 same => n,Ringing()
 same => n,Wait(1)
 same => n,Set(UUID=${SHELL(uuidgen | tr -d '\n')})
 same => n,AudioSocket(${UUID},${ARG1})
 same => n,Hangup()
```

## ENTORNO DE DESARROLLO
- **Local**: /Users/<USER>/Sites/autobot/ (solo desarrollo)
- **Cloud**: /opt/cobranza-bot/venv (entorno virtual para testing)
- **OS**: Linux Debian 12 (servidor) / macOS (desarrollo local)

## ESTRUCTURA DEL PROYECTO
```
autobot/
├── src/
│   ├── audiosocket_server.py    # Servidor principal AudioSocket
│   ├── stt_handler.py           # Manejo de Speech-to-Text
│   ├── tts_handler.py           # Manejo de Text-to-Speech
│   ├── ai_conversation.py       # Motor de IA conversacional
│   ├── database.py              # Conexión y consultas DB
│   └── config.py                # Configuraciones
├── sql/
│   └── schema.sql               # Esquema de base de datos
├── requirements.txt             # Dependencias Python
├── .env                         # Variables de entorno
├── .memory                      # Este archivo de contexto
└── README.md                    # Documentación
```

## DEPENDENCIAS PRINCIPALES
```
psycopg2-binary>=2.9.0    # PostgreSQL
openai>=1.0.0             # API OpenAI
google-cloud-texttospeech>=2.16.3         # google
asyncio                   # Programación asíncrona
wave                      # Procesamiento de audio
struct                    # Manejo de datos binarios
logging                   # Sistema de logs
```

## ESQUEMA DE BASE DE DATOS
```sql
-- Tabla de clientes
CREATE TABLE clientes (
    id SERIAL PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    telefono VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de deudas
CREATE TABLE deudas (
    id SERIAL PRIMARY KEY,
    cliente_id INTEGER REFERENCES clientes(id),
    monto DECIMAL(10,2) NOT NULL,
    fecha_vencimiento DATE NOT NULL,
    estado VARCHAR(50) DEFAULT 'pendiente',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de compromisos de pago
CREATE TABLE compromisos_pago (
    id SERIAL PRIMARY KEY,
    deuda_id INTEGER REFERENCES deudas(id),
    fecha_compromiso DATE NOT NULL,
    monto_acordado DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de logs de llamadas
CREATE TABLE logs_llamadas (
    id SERIAL PRIMARY KEY,
    cliente_id INTEGER REFERENCES clientes(id),
    uuid_llamada VARCHAR(255) NOT NULL,
    transcripcion TEXT,
    resultado VARCHAR(100),
    duracion INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## FLUJO DE CONVERSACIÓN
1. **Saludo**: Identificación del bot y solicitud de datos
2. **Validación**: Verificar identidad del cliente en BD
3. **Consulta**: Obtener información de deuda y vencimiento
4. **Negociación**: Solicitar compromiso de pago
5. **Confirmación**: Registrar acuerdo en base de datos
6. **Cierre**: Despedida y finalización de llamada

## CONFIGURACIONES CRÍTICAS
- **Latencia objetivo**: <500ms para conversación natural
- **Interrupciones**: Permitir que cliente interrumpa al bot
- **Seguridad**: No exponer datos sensibles en logs
- **Escalabilidad**: Diseño para múltiples llamadas simultáneas

## VARIABLES DE ENTORNO REQUERIDAS
```
# APIs
OPENAI_API_KEY=sk-...
ELEVENLABS_API_KEY=...

# Base de datos
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cobranza
DB_USER=...
DB_PASSWORD=...

# AudioSocket
AUDIOSOCKET_HOST=127.0.0.1
AUDIOSOCKET_PORT=5001

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/cobranza-bot.log
```

## NOTAS DE IMPLEMENTACIÓN
- Usar cabeceras estándar en todos los archivos Python
- Implementar manejo robusto de errores
- Logging detallado para debugging
- Validación exhaustiva de datos de entrada
- Optimización de performance para tiempo real

## PRÓXIMOS PASOS
1. Configurar entorno virtual en /opt/cobranza-bot/venv
2. Crear estructura básica del proyecto
3. Implementar servidor AudioSocket
4. Integrar APIs de TTS/STT
5. Desarrollar motor de IA conversacional
6. Testing e integración con Asterisk

## CONTACTOS Y RECURSOS
- Documentación AudioSocket: https://wiki.asterisk.org/wiki/display/AST/AudioSocket
- API ElevenLabs: https://docs.elevenlabs.io/
- API OpenAI: https://platform.openai.com/docs/

---
Este archivo debe actualizarse con cada cambio significativo del proyecto.


# Environment
- User cannot test locally - all files are in cloud environment and testing must be done there.
- User prefers no test files to be created during development work.

# AudioSocket Implementation Issues
- User is considering abandoning Python for Node.js due to persistent audio transmission issues in the AudioSocket implementation.
- AudioSocket audio transmission has severe delay issues - audio doesn't reach the caller until 10+ seconds after being sent, indicating fundamental protocol or connection problems beyond just silence padding.
- Current Python implementation uses batch processing causing audio quality drops, delays, and premature call termination - streaming architecture is required for proper AudioSocket performance.

# Working AudioSocket Comparison
- User has a working proprietary AudioSocket solution called 'infra_avr' on the same platform that works excellently with real-time audio transmission, confirming AudioSocket and platform are capable of proper performance.
- User's working infra_avr solution uses true streaming mode for audio transmission.
- User has additional obfuscated files from working infra_avr solution (index.js, logger.js, tts.js) that may contain implementation patterns for AudioSocket audio transmission.
- infra_avr AudioSocket optimal timing: 21.4ms average between chunks, 93.3 chunks/second, 320 bytes chunks, 29868 bytes/second for proper audio transmission without overlapping.

# AudioSocket Optimization & Features
- AudioSocket implementation with 21.4ms timing between chunks (matching infra_avr) and keepalive system successfully resolved audio mounting and Deepgram timeout issues.
- AudioSocket silence trimming optimization successfully reduced initial silence from 464ms to 62ms, improving AudioSocket audio transmission by removing excessive padding that was causing users to think audio wasn't playing.
- AudioSocket calls must validate debt using only DNI from cliente and deuda tables in database, and conversation response delays need optimization for better user experience.
- AudioSocket voice bots must implement proper audio interruption handling to stop TTS playback when user speaks, preventing desynchronized conversations where responses don't match user input timing, and should automatically hang up calls via AMI after generating and saving payment commitments.
- AudioSocket payment commitments obtained during calls should be saved to compromisos_pago table, and ASR streaming transcription delays need optimization for real-time performance along with LLM response optimization.
- AudioSocket payment commitment system now has optimized DateCalculator with pre-calculated phrase mapping, LRU cache, and 97.9% success rate for natural language date parsing - handles phrases like 'mañana', 'pasado mañana', 'próximo lunes', 'este jueves' with ultra-fast lookup.
- AudioSocket payment commitments require fast date calculation for natural language terms like 'mañana', 'pasado mañana', 'este jueves', 'próximo martes' - common phrases should be pre-calculated and cached for optimal performance.
- AudioSocket greeting should be simple and direct without contextual time-based greetings as they cause latency - use static greeting instead.
- AudioSocket system now has functional DNI extraction, corrected database query (placeholders $1 instead of %s), and fixed conversation states (NEGOCIANDO instead of nonexistent INFORMANDO), but saving payment commitments and other improvement adjustments still need to be corrected.
- AudioSocket system corrected: critical security flaw fixed - no longer gives false debt information without identified client, DNI search restricted to appropriate states, strict 8-digit validation, blocking of intents without identification.
- Infinite DNI loop solved: system now passes complete context of identified client to AI prompts, including ID, name and total debt, with explicit instructions not to ask for DNI again when client is already validated, and automatic direct response to continue conversation.
- Critical technical error fixed: variable 'texto_a_analizar' out of scope caused infinite "technical problem" loop - corrected by defining variable at the beginning of function, added detection of debt inquiries with keywords, system now responds directly to questions about debt when client is already identified.
- AudioSocket ASR optimized with pre-initialization at session start, nova-2 model for speed, and proper audio interruption that cancels TTS tasks when user speaks during bot playback.
- AudioSocket voice bots should store call logs when conversations end with farewells.
- AudioSocket system automatically hangs up calls via Asterisk AMI after successful payment commitments, sends personalized farewell messages, and logs call completion with commitment details.
- AudioSocket calls must actually hangup/terminate when despedida or compromiso conditions are met, not just simulate termination.
- AudioSocket hangup should identify and terminate only the specific channel for the current call session, not all active channels, to avoid disrupting other concurrent calls.
- AudioSocket AMI connection issues may be caused by test implementation problems rather than actual AMI configuration - diagnostic shows AMI is fully functional while tests report invalid banner errors.
- AudioSocket calls appear in AMI as PJSIP channels in 'avr' context with Application: AudioSocket and the session UUID in the Data field (format: UUID,127.0.0.1:5001).
- AudioSocket hangup system implemented with AMI integration: detects user goodbye phrases, generates farewell responses, identifies specific channel by UUID in ApplicationData field, executes precise hangup via Asterisk AMI, supports multiple concurrent calls safely, logs call completion - functioning with minor delay to be optimized later.

# Critical Policies
- CRÍTICO RESUELTO: Implementada doble validación de seguridad - Intent Processor ahora bloquea intents protegidos sin cliente identificado, y prompts instruyen explícitamente a no inventar información de deuda, eliminando completamente el riesgo de dar información falsa.
- Critical policy: Never provide false information or hallucinations about client data - if client is not found in database, no information should be given as this is sensitive financial data.

# Code Style Preferences
- User prefers source files to be no more than 400 lines for maintainability and wants large files refactored into separate components.