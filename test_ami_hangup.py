#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script para probar el hangup real via AMI.
"""

import sys
import os
import asyncio
import logging

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.asterisk_ami import AsteriskAMI, AMIConfig
from src.config import Config


async def test_ami_connection_and_hangup():
    """Prueba conexión AMI y capacidad de hangup."""
    print("🔌 PRUEBA DE CONEXIÓN Y HANGUP AMI")
    print("=" * 60)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
    
    try:
        # Cargar configuración
        config = Config()
        
        # Verificar configuración AMI
        if not config.asterisk.validate():
            print("❌ Configuración AMI incompleta")
            print(f"   Host: {config.asterisk.host}")
            print(f"   Puerto: {config.asterisk.ami_port}")
            print(f"   Usuario: {config.asterisk.ami_user}")
            print(f"   Secret: {'***' if config.asterisk.ami_secret else 'NO CONFIGURADO'}")
            return False
        
        print(f"✅ Configuración AMI:")
        print(f"   Host: {config.asterisk.host}:{config.asterisk.ami_port}")
        print(f"   Usuario: {config.asterisk.ami_user}")
        
        # Crear cliente AMI
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        # Intentar conexión
        print(f"\n🔗 Conectando a AMI...")
        if await ami.connect():
            print("✅ Conexión AMI exitosa")
            
            # Listar canales activos
            print(f"\n📋 Listando canales activos...")
            channels = await ami._get_all_channels()
            
            if channels:
                print(f"📞 Canales encontrados ({len(channels)}):")
                for i, channel in enumerate(channels, 1):
                    print(f"   {i}. {channel}")
                
                # Preguntar si hacer hangup
                print(f"\n⚠️ ADVERTENCIA: ¿Desea colgar TODOS los canales activos?")
                print(f"   Esto cortará todas las llamadas en curso.")
                print(f"   Escriba 'SI' para confirmar, cualquier otra cosa para cancelar:")
                
                # En un script automatizado, podemos simular la respuesta
                # response = input().strip().upper()
                response = "NO"  # Cambiar a "SI" solo para pruebas reales
                
                if response == "SI":
                    print(f"\n🔌 Ejecutando hangup de todos los canales...")
                    success = await ami.hangup_all_channels()
                    
                    if success:
                        print("✅ Hangup ejecutado exitosamente")
                    else:
                        print("❌ Error ejecutando hangup")
                else:
                    print("🚫 Hangup cancelado por seguridad")
                    
                    # En su lugar, probar hangup de un canal específico (simulado)
                    print(f"\n🧪 Probando búsqueda de canal por UUID...")
                    test_uuid = "test-uuid-123"
                    found_channel = await ami.find_channel_by_uuid(test_uuid)
                    
                    if found_channel:
                        print(f"✅ Canal encontrado: {found_channel}")
                    else:
                        print(f"❌ No se encontró canal para UUID: {test_uuid}")
                        print(f"   (Esto es normal en una prueba)")
            else:
                print("📞 No hay canales activos")
                print("   Para probar hangup real, necesitas una llamada activa")
            
            await ami.disconnect()
            return True
            
        else:
            print("❌ Error conectando a AMI")
            print("\n🔧 PASOS PARA CONFIGURAR AMI:")
            print("1. sudo nano /etc/asterisk/manager.conf")
            print("2. Agregar configuración:")
            print("""
[general]
enabled = yes
port = 5038
bindaddr = 127.0.0.1

[avr]
secret = avr
deny = 0.0.0.0/0.0.0.0
permit = 127.0.0.1/255.255.255.0
read = system,call,log,verbose,command,agent,user,config,dtmf,reporting,cdr,dialplan
write = system,call,log,verbose,command,agent,user,config,dtmf,reporting,cdr,dialplan
""")
            print("3. sudo systemctl reload asterisk")
            print("4. sudo asterisk -rx 'manager reload'")
            return False
            
    except Exception as e:
        print(f"❌ Error en prueba AMI: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_manual_ami_commands():
    """Prueba comandos AMI manuales."""
    print(f"\n🛠️ PRUEBA DE COMANDOS AMI MANUALES")
    print("=" * 60)
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            print("✅ Conectado para comandos manuales")
            
            # Comando 1: Status general
            print(f"\n📊 Ejecutando comando Status...")
            action_id = ami._get_action_id()
            
            status_cmd = (
                f"Action: Status\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )
            
            ami.writer.write(status_cmd.encode())
            await ami.writer.drain()
            
            # Leer respuestas
            responses = []
            for _ in range(10):  # Leer hasta 10 respuestas
                response = await ami._read_response()
                if response:
                    responses.append(response)
                    print(f"📨 {response}")
                else:
                    break
            
            await ami.disconnect()
            
            if responses:
                print(f"✅ Comandos AMI funcionando ({len(responses)} respuestas)")
                return True
            else:
                print(f"❌ No se recibieron respuestas de AMI")
                return False
        else:
            print("❌ No se pudo conectar para comandos manuales")
            return False
            
    except Exception as e:
        print(f"❌ Error en comandos manuales: {e}")
        return False


async def main():
    """Función principal de prueba."""
    print("🚀 PRUEBA COMPLETA DE AMI HANGUP")
    print("=" * 80)
    
    # Prueba 1: Conexión y hangup
    ami_ok = await test_ami_connection_and_hangup()
    
    # Prueba 2: Comandos manuales
    commands_ok = await test_manual_ami_commands()
    
    # Resumen
    print("\n" + "=" * 80)
    print("📊 RESUMEN DE PRUEBAS AMI")
    print("=" * 80)
    
    print(f"🔌 Conexión AMI: {'✅ EXITOSA' if ami_ok else '❌ FALLIDA'}")
    print(f"🛠️ Comandos AMI: {'✅ FUNCIONAN' if commands_ok else '❌ FALLAN'}")
    
    if ami_ok and commands_ok:
        print(f"\n🎉 AMI COMPLETAMENTE FUNCIONAL")
        print(f"   - El sistema puede conectar a Asterisk")
        print(f"   - Los comandos de hangup funcionarán")
        print(f"   - Las llamadas se cortarán realmente")
    else:
        print(f"\n⚠️ PROBLEMAS CON AMI")
        if not ami_ok:
            print(f"   - Configurar AMI en /etc/asterisk/manager.conf")
            print(f"   - Reiniciar Asterisk: sudo systemctl reload asterisk")
        if not commands_ok:
            print(f"   - Verificar permisos de usuario AMI")
            print(f"   - Verificar que Asterisk esté ejecutándose")
    
    print(f"\n📞 PARA PROBAR HANGUP REAL:")
    print(f"   1. Hacer una llamada al sistema AudioSocket")
    print(f"   2. Decir 'adiós' durante la llamada")
    print(f"   3. Verificar que la llamada se corte automáticamente")


if __name__ == "__main__":
    asyncio.run(main())
