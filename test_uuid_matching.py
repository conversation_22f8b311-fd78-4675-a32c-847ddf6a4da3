#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Test para verificar la identificación de canales por UUID en ApplicationData.
"""

import sys
import os
import asyncio
import logging

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.asterisk_ami import AsteriskAMI, AMIConfig
from src.config import Config


async def test_uuid_matching():
    """Prueba la identificación de canales usando UUIDs reales."""
    print("🎯 PRUEBA DE IDENTIFICACIÓN POR UUID")
    print("=" * 60)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            print("✅ Conectado a AMI para prueba de UUID")
            
            # Obtener todos los canales activos
            print(f"\n📋 OBTENIENDO CANALES ACTIVOS:")
            print("-" * 40)
            
            action_id = ami._get_action_id()
            
            channels_cmd = (
                f"Action: CoreShowChannels\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )
            
            ami.writer.write(channels_cmd.encode())
            await ami.writer.drain()
            
            channels_with_uuids = []
            
            # Leer respuestas y extraer UUIDs
            while True:
                response = await ami._read_response()
                if not response:
                    break
                
                if 'channel' in response:
                    channel_name = response.get('channel', '')
                    app_data = response.get('applicationdata', '')
                    application = response.get('application', '')
                    
                    print(f"📞 Canal: {channel_name}")
                    print(f"   Application: {application}")
                    print(f"   Data: {app_data}")
                    
                    # Si es AudioSocket, extraer UUID
                    if application == 'AudioSocket' and app_data:
                        # El formato es: UUID,127.0.0.1:5001
                        parts = app_data.split(',')
                        if len(parts) >= 1:
                            uuid = parts[0]
                            channels_with_uuids.append({
                                'channel': channel_name,
                                'uuid': uuid,
                                'app_data': app_data
                            })
                            print(f"   🎯 UUID extraído: {uuid}")
                    
                    print()
                
                if response.get('event') == 'CoreShowChannelsComplete':
                    break
            
            # Probar búsqueda con UUIDs reales
            print(f"📊 CANALES CON UUID ENCONTRADOS: {len(channels_with_uuids)}")
            print("-" * 50)
            
            for i, channel_info in enumerate(channels_with_uuids, 1):
                uuid = channel_info['uuid']
                channel = channel_info['channel']
                
                print(f"\n🧪 PRUEBA #{i}: Buscando UUID {uuid}")
                print(f"   Canal esperado: {channel}")
                
                # Usar la función de búsqueda
                found_channel = await ami.find_channel_by_session(uuid, "test_caller")
                
                if found_channel:
                    if found_channel == channel:
                        print(f"   ✅ ÉXITO: Canal encontrado correctamente")
                    else:
                        print(f"   ⚠️ ADVERTENCIA: Canal diferente encontrado: {found_channel}")
                else:
                    print(f"   ❌ ERROR: Canal NO encontrado")
            
            # Probar con UUID falso
            print(f"\n🧪 PRUEBA DE SEGURIDAD: UUID falso")
            fake_uuid = "00000000-0000-0000-0000-000000000000"
            found_fake = await ami.find_channel_by_session(fake_uuid, "fake_caller")
            
            if found_fake is None:
                print(f"   ✅ SEGURIDAD: No encontró canal para UUID falso")
            else:
                print(f"   ❌ PELIGRO: Encontró canal para UUID falso: {found_fake}")
            
            await ami.disconnect()
            
            # Evaluar resultados
            if channels_with_uuids:
                print(f"\n🎉 SISTEMA DE IDENTIFICACIÓN FUNCIONAL")
                print(f"   ✅ {len(channels_with_uuids)} canales AudioSocket identificados")
                print(f"   ✅ UUIDs extraídos correctamente del ApplicationData")
                print(f"   ✅ Búsqueda por UUID implementada")
                
                print(f"\n📞 PARA PROBAR HANGUP REAL:")
                print(f"   1. Hacer llamada al sistema")
                print(f"   2. Decir 'adiós' durante la llamada")
                print(f"   3. El sistema debería encontrar el canal por UUID")
                print(f"   4. Ejecutar hangup del canal específico")
                
                return True
            else:
                print(f"\n⚠️ NO HAY LLAMADAS AUDIOSOCKET ACTIVAS")
                print(f"   Para probar, necesitas llamadas activas")
                return False
            
        else:
            print("❌ No se pudo conectar a AMI")
            return False
            
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        import traceback
        traceback.print_exc()
        return False


async def simulate_hangup_with_real_uuid():
    """Simula hangup usando UUIDs reales de canales activos."""
    print(f"\n🔌 SIMULACIÓN DE HANGUP CON UUID REAL")
    print("=" * 60)
    print("⚠️ ESTO CORTARÁ UNA LLAMADA REAL SI HAY ALGUNA ACTIVA")
    print("⚠️ SOLO PARA TESTING - USAR CON PRECAUCIÓN")
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            # Obtener canales AudioSocket activos
            action_id = ami._get_action_id()
            
            channels_cmd = (
                f"Action: CoreShowChannels\r\n"
                f"ActionID: {action_id}\r\n"
                f"\r\n"
            )
            
            ami.writer.write(channels_cmd.encode())
            await ami.writer.drain()
            
            audiosocket_channels = []
            
            while True:
                response = await ami._read_response()
                if not response:
                    break
                
                if 'channel' in response:
                    application = response.get('application', '')
                    app_data = response.get('applicationdata', '')
                    
                    if application == 'AudioSocket' and app_data:
                        parts = app_data.split(',')
                        if len(parts) >= 1:
                            uuid = parts[0]
                            audiosocket_channels.append({
                                'channel': response.get('channel'),
                                'uuid': uuid
                            })
                
                if response.get('event') == 'CoreShowChannelsComplete':
                    break
            
            if audiosocket_channels:
                print(f"📞 {len(audiosocket_channels)} llamadas AudioSocket activas")
                
                # COMENTADO PARA SEGURIDAD - Descomentar solo para testing real
                """
                for i, ch in enumerate(audiosocket_channels):
                    print(f"   {i+1}. {ch['channel']} (UUID: {ch['uuid']})")
                
                print(f"\n⚠️ ¿Ejecutar hangup del primer canal? (CORTARÁ LA LLAMADA)")
                print(f"   Descomenta el código para habilitar")
                
                # Descomentar estas líneas para testing real:
                # uuid_to_hangup = audiosocket_channels[0]['uuid']
                # channel_found = await ami.find_channel_by_session(uuid_to_hangup)
                # if channel_found:
                #     success = await ami.hangup_channel(channel_found)
                #     print(f"Hangup result: {success}")
                """
                
                print(f"🛡️ HANGUP DESHABILITADO POR SEGURIDAD")
                print(f"   Para habilitar, editar el código del script")
                
            else:
                print(f"📞 No hay llamadas AudioSocket activas")
            
            await ami.disconnect()
            return True
            
        else:
            print("❌ No se pudo conectar a AMI")
            return False
            
    except Exception as e:
        print(f"❌ Error en simulación: {e}")
        return False


async def main():
    """Función principal."""
    print("🚀 PRUEBAS DE IDENTIFICACIÓN Y HANGUP POR UUID")
    print("=" * 80)
    
    # Prueba 1: Identificación por UUID
    identification_ok = await test_uuid_matching()
    
    # Prueba 2: Simulación de hangup (deshabilitada por seguridad)
    simulation_ok = await simulate_hangup_with_real_uuid()
    
    # Resumen
    print("\n" + "=" * 80)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 80)
    
    print(f"🎯 Identificación por UUID: {'✅ FUNCIONAL' if identification_ok else '❌ PROBLEMAS'}")
    print(f"🔌 Simulación de hangup: {'✅ PREPARADO' if simulation_ok else '❌ NO DISPONIBLE'}")
    
    if identification_ok:
        print(f"\n🎉 SISTEMA LISTO PARA HANGUP AUTOMÁTICO")
        print(f"   ✅ Identifica canales por UUID en ApplicationData")
        print(f"   ✅ Distingue entre múltiples llamadas concurrentes")
        print(f"   ✅ Solo cortará la llamada específica donde se dijo 'adiós'")


if __name__ == "__main__":
    asyncio.run(main())
