#!/bin/bash

# Bot de Cobranza con IA - Script de Instalación
# ==============================================
#
# Este script automatiza la instalación y configuración del sistema
# de cobranza con IA en un servidor Linux.
#
# Uso: sudo ./install.sh
#
# Autor: Sistema de Cobranza Automatizada
# Fecha: 2024

set -e  # Salir en caso de error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuración
APP_NAME="cobranza-bot"
APP_DIR="/opt/cobranza-bot"
APP_USER="cobranza"
APP_GROUP="cobranza"
LOG_DIR="$APP_DIR/logs"
PYTHON_VERSION="3.9"
POSTGRES_VERSION="13"

# Funciones de utilidad
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "Este script debe ejecutarse como root (use sudo)"
        exit 1
    fi
}

check_os() {
    if [[ ! -f /etc/os-release ]]; then
        log_error "No se pudo detectar el sistema operativo"
        exit 1
    fi
    
    source /etc/os-release
    
    case $ID in
        ubuntu|debian)
            PACKAGE_MANAGER="apt"
            ;;
        centos|rhel|fedora)
            PACKAGE_MANAGER="yum"
            ;;
        *)
            log_error "Sistema operativo no soportado: $ID"
            exit 1
            ;;
    esac
    
    log_info "Sistema operativo detectado: $PRETTY_NAME"
}

install_system_packages() {
    log_info "Instalando paquetes del sistema..."
    
    if [[ $PACKAGE_MANAGER == "apt" ]]; then
        apt update
        apt install -y \
            python3 \
            python3-pip \
            python3-venv \
            python3-dev \
            postgresql-$POSTGRES_VERSION \
            postgresql-client-$POSTGRES_VERSION \
            postgresql-contrib-$POSTGRES_VERSION \
            build-essential \
            libpq-dev \
            libffi-dev \
            libssl-dev \
            curl \
            wget \
            git \
            supervisor \
            nginx \
            ufw
    elif [[ $PACKAGE_MANAGER == "yum" ]]; then
        yum update -y
        yum install -y \
            python3 \
            python3-pip \
            python3-devel \
            postgresql$POSTGRES_VERSION-server \
            postgresql$POSTGRES_VERSION \
            postgresql$POSTGRES_VERSION-contrib \
            gcc \
            gcc-c++ \
            make \
            libpq-devel \
            libffi-devel \
            openssl-devel \
            curl \
            wget \
            git \
            supervisor \
            nginx \
            firewalld
    fi
    
    log_success "Paquetes del sistema instalados"
}

create_user() {
    log_info "Creando usuario del sistema..."
    
    if ! id "$APP_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d $APP_DIR -m $APP_USER
        log_success "Usuario $APP_USER creado"
    else
        log_warning "Usuario $APP_USER ya existe"
    fi
}

setup_directories() {
    log_info "Configurando directorios..."
    
    # Crear directorios principales
    mkdir -p $APP_DIR
    mkdir -p $LOG_DIR
    mkdir -p $APP_DIR/src
    mkdir -p $APP_DIR/sql
    mkdir -p $APP_DIR/systemd
    
    # Configurar permisos
    chown -R $APP_USER:$APP_GROUP $APP_DIR
    chmod -R 755 $APP_DIR
    chmod -R 775 $LOG_DIR
    
    log_success "Directorios configurados"
}

setup_python_environment() {
    log_info "Configurando entorno Python..."
    
    # Crear entorno virtual
    sudo -u $APP_USER python3 -m venv $APP_DIR/venv
    
    # Activar entorno virtual y actualizar pip
    sudo -u $APP_USER $APP_DIR/venv/bin/pip install --upgrade pip setuptools wheel
    
    log_success "Entorno Python configurado"
}

install_python_dependencies() {
    log_info "Instalando dependencias Python..."
    
    if [[ -f "$APP_DIR/requirements.txt" ]]; then
        sudo -u $APP_USER $APP_DIR/venv/bin/pip install -r $APP_DIR/requirements.txt
        log_success "Dependencias Python instaladas"
    else
        log_warning "Archivo requirements.txt no encontrado"
    fi
}

setup_postgresql() {
    log_info "Configurando PostgreSQL..."
    
    # Inicializar PostgreSQL si es necesario
    if [[ $PACKAGE_MANAGER == "yum" ]]; then
        if [[ ! -d "/var/lib/pgsql/$POSTGRES_VERSION/data" ]]; then
            sudo -u postgres /usr/pgsql-$POSTGRES_VERSION/bin/initdb -D /var/lib/pgsql/$POSTGRES_VERSION/data
        fi
    fi
    
    # Iniciar y habilitar PostgreSQL
    systemctl start postgresql
    systemctl enable postgresql
    
    # Crear base de datos y usuario
    sudo -u postgres psql -c "CREATE USER cobranza_user WITH PASSWORD 'cobranza_pass';"
    sudo -u postgres psql -c "CREATE DATABASE cobranza_db OWNER cobranza_user;"
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE cobranza_db TO cobranza_user;"
    
    # Aplicar esquema si existe
    if [[ -f "$APP_DIR/sql/schema.sql" ]]; then
        sudo -u postgres psql -d cobranza_db -f $APP_DIR/sql/schema.sql
        log_success "Esquema de base de datos aplicado"
    fi
    
    log_success "PostgreSQL configurado"
}

setup_environment_file() {
    log_info "Configurando archivo de entorno..."
    
    if [[ ! -f "$APP_DIR/.env" ]]; then
        if [[ -f "$APP_DIR/.env.example" ]]; then
            cp $APP_DIR/.env.example $APP_DIR/.env
            
            # Configurar valores básicos
            sed -i "s/DB_HOST=localhost/DB_HOST=localhost/g" $APP_DIR/.env
            sed -i "s/DB_PORT=5432/DB_PORT=5432/g" $APP_DIR/.env
            sed -i "s/DB_NAME=cobranza_db/DB_NAME=cobranza_db/g" $APP_DIR/.env
            sed -i "s/DB_USER=cobranza_user/DB_USER=cobranza_user/g" $APP_DIR/.env
            sed -i "s/DB_PASSWORD=your_password/DB_PASSWORD=cobranza_pass/g" $APP_DIR/.env
            
            chown $APP_USER:$APP_GROUP $APP_DIR/.env
            chmod 600 $APP_DIR/.env
            
            log_success "Archivo .env configurado"
            log_warning "IMPORTANTE: Configure las API keys en $APP_DIR/.env"
        else
            log_error "Archivo .env.example no encontrado"
        fi
    else
        log_warning "Archivo .env ya existe"
    fi
}

setup_systemd_service() {
    log_info "Configurando servicio systemd..."
    
    if [[ -f "$APP_DIR/systemd/cobranza-bot.service" ]]; then
        cp $APP_DIR/systemd/cobranza-bot.service /etc/systemd/system/
        systemctl daemon-reload
        systemctl enable cobranza-bot
        
        log_success "Servicio systemd configurado"
    else
        log_warning "Archivo de servicio systemd no encontrado"
    fi
}

setup_firewall() {
    log_info "Configurando firewall..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian
        ufw allow 8080/tcp comment "Cobranza Bot AudioSocket"
        ufw allow 22/tcp comment "SSH"
        ufw --force enable
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL/Fedora
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --reload
    fi
    
    log_success "Firewall configurado"
}

setup_log_rotation() {
    log_info "Configurando rotación de logs..."
    
    cat > /etc/logrotate.d/cobranza-bot << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_GROUP
    postrotate
        systemctl reload cobranza-bot
    endscript
}
EOF
    
    log_success "Rotación de logs configurada"
}

run_tests() {
    log_info "Ejecutando pruebas del sistema..."
    
    if sudo -u $APP_USER $APP_DIR/venv/bin/python3 $APP_DIR/start_system.py test; then
        log_success "Todas las pruebas pasaron"
        return 0
    else
        log_error "Algunas pruebas fallaron"
        return 1
    fi
}

show_post_install_info() {
    echo
    echo "======================================"
    echo "  INSTALACIÓN COMPLETADA"
    echo "======================================"
    echo
    echo "El sistema de cobranza con IA ha sido instalado en: $APP_DIR"
    echo
    echo "Próximos pasos:"
    echo "1. Configure las API keys en: $APP_DIR/.env"
    echo "   - OPENAI_API_KEY"
    echo "   - DEEPGRAM_API_KEY"
    echo "   - ELEVENLABS_API_KEY"
    echo
    echo "2. Inicie el servicio:"
    echo "   sudo systemctl start cobranza-bot"
    echo
    echo "3. Verifique el estado:"
    echo "   sudo systemctl status cobranza-bot"
    echo
    echo "4. Ver logs:"
    echo "   sudo journalctl -u cobranza-bot -f"
    echo
    echo "5. Gestión manual:"
    echo "   sudo -u $APP_USER $APP_DIR/start_system.py [start|stop|status|test]"
    echo
    echo "Archivos importantes:"
    echo "- Configuración: $APP_DIR/.env"
    echo "- Logs: $LOG_DIR/"
    echo "- Servicio: /etc/systemd/system/cobranza-bot.service"
    echo
    echo "Puerto AudioSocket: 8080 (configurable en .env)"
    echo
}

# Función principal
main() {
    echo "======================================"
    echo "  INSTALADOR BOT DE COBRANZA CON IA"
    echo "======================================"
    echo
    
    # Verificaciones previas
    check_root
    check_os
    
    # Instalación
    install_system_packages
    create_user
    setup_directories
    setup_python_environment
    install_python_dependencies
    setup_postgresql
    setup_environment_file
    setup_systemd_service
    setup_firewall
    setup_log_rotation
    
    # Pruebas (opcional)
    echo
    read -p "¿Desea ejecutar las pruebas del sistema? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if ! run_tests; then
            log_warning "Las pruebas fallaron, pero la instalación continuó"
        fi
    fi
    
    # Información post-instalación
    show_post_install_info
}

# Manejo de errores
trap 'log_error "Error en línea $LINENO. Instalación abortada."' ERR

# Ejecutar instalación
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi