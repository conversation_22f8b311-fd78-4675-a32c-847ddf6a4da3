#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Test específico para verificar la detección de despedida del usuario.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import Config
from src.ai_conversation import AIConversationEngine
from src.database import DatabaseManager


async def test_goodbye_detection():
    """Prueba la detección de despedida del usuario."""
    print("👋 PRUEBA DE DETECCIÓN DE DESPEDIDA DEL USUARIO")
    print("=" * 60)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
    
    try:
        # Inicializar componentes
        config = Config()
        db_manager = DatabaseManager()
        ai_engine = AIConversationEngine(config.openai, db_manager)
        await ai_engine.initialize()
        
        # Crear sesión
        session_id = "test-goodbye-detection"
        caller_number = "987654321"
        
        print(f"📞 Iniciando conversación: {session_id}")
        context = await ai_engine.start_conversation(session_id, caller_number)
        
        # Frases de prueba que deberían activar despedida
        goodbye_phrases = [
            "adiós",
            "no no no adiós",
            "chau",
            "hasta luego",
            "me voy",
            "cuelgo",
            "corta la llamada",
            "termina la llamada",
            "no quiero hablar más",
            "basta ya",
            "no molesten más"
        ]
        
        print(f"\n🔍 PROBANDO FRASES DE DESPEDIDA:")
        print("-" * 40)
        
        for i, phrase in enumerate(goodbye_phrases):
            print(f"\n{i+1}. Usuario dice: '{phrase}'")
            
            # Procesar entrada
            response = await ai_engine.process_user_input(session_id, phrase)
            
            # Verificar si se detectó despedida
            is_goodbye = response.metadata.get('user_goodbye', False)
            should_end = response.metadata.get('should_end_call', False)
            
            if is_goodbye:
                print(f"   ✅ DESPEDIDA DETECTADA")
                print(f"   🤖 Bot responde: '{response.text}'")
                print(f"   🔚 Should end call: {should_end}")
                
                # Verificar contexto
                context = ai_engine.get_conversation_context(session_id)
                if context and getattr(context, 'should_end_call', False):
                    print(f"   ✅ Contexto marcado para finalizar")
                else:
                    print(f"   ❌ Contexto NO marcado para finalizar")
                
                break  # Salir después de la primera despedida detectada
            else:
                print(f"   ❌ NO detectada como despedida")
                print(f"   🤖 Bot responde: '{response.text}'")
        
        # Cleanup
        await ai_engine.cleanup()
        
        return is_goodbye
        
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_normal_conversation():
    """Prueba que frases normales NO activen despedida."""
    print(f"\n💬 PRUEBA DE CONVERSACIÓN NORMAL")
    print("=" * 60)
    
    try:
        # Inicializar componentes
        config = Config()
        db_manager = DatabaseManager()
        ai_engine = AIConversationEngine(config.openai, db_manager)
        await ai_engine.initialize()
        
        # Crear sesión
        session_id = "test-normal-conversation"
        caller_number = "987654322"
        
        context = await ai_engine.start_conversation(session_id, caller_number)
        
        # Frases normales que NO deberían activar despedida
        normal_phrases = [
            "hola",
            "¿cuánto debo?",
            "no entiendo",
            "no tengo dinero",
            "puedo pagar mañana",
            "mi DNI es 12345678",
            "sí acepto",
            "no puedo pagar hoy pero sí mañana"
        ]
        
        print(f"\n🔍 PROBANDO FRASES NORMALES:")
        print("-" * 40)
        
        all_normal = True
        
        for i, phrase in enumerate(normal_phrases):
            print(f"\n{i+1}. Usuario dice: '{phrase}'")
            
            # Procesar entrada
            response = await ai_engine.process_user_input(session_id, phrase)
            
            # Verificar que NO se detectó despedida
            is_goodbye = response.metadata.get('user_goodbye', False)
            
            if is_goodbye:
                print(f"   ❌ FALSO POSITIVO: Detectada como despedida")
                all_normal = False
            else:
                print(f"   ✅ Correctamente NO detectada como despedida")
            
            print(f"   🤖 Bot responde: '{response.text[:50]}...'")
        
        # Cleanup
        await ai_engine.cleanup()
        
        return all_normal
        
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Función principal de prueba."""
    print("🚀 PRUEBAS DE DETECCIÓN DE DESPEDIDA")
    print("=" * 80)
    
    # Prueba 1: Detección de despedida
    goodbye_detected = await test_goodbye_detection()
    
    # Prueba 2: Conversación normal
    normal_ok = await test_normal_conversation()
    
    # Resumen
    print("\n" + "=" * 80)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 80)
    
    print(f"👋 Detección de despedida: {'✅ FUNCIONA' if goodbye_detected else '❌ FALLA'}")
    print(f"💬 Conversación normal: {'✅ SIN FALSOS POSITIVOS' if normal_ok else '❌ FALSOS POSITIVOS'}")
    
    if goodbye_detected and normal_ok:
        print(f"\n🎉 SISTEMA DE DETECCIÓN DE DESPEDIDA FUNCIONAL")
        print(f"   - Detecta correctamente cuando el usuario dice adiós")
        print(f"   - No interfiere con conversaciones normales")
        print(f"   - Genera respuestas de despedida automáticas")
    else:
        print(f"\n⚠️ PROBLEMAS EN EL SISTEMA DE DETECCIÓN")
        if not goodbye_detected:
            print(f"   - No detecta despedidas del usuario")
        if not normal_ok:
            print(f"   - Falsos positivos en conversación normal")


if __name__ == "__main__":
    asyncio.run(main())
