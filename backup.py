#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Sistema de Backup
==========================================

Este script proporciona funcionalidades de backup y restauración
para el sistema de cobranza automatizada.

Características:
- Backup automático de base de datos
- Backup de configuraciones
- Backup de logs importantes
- Compresión y cifrado
- Rotación automática
- Restauración selectiva
- Verificación de integridad

Uso:
    python3 backup.py [comando] [opciones]

Comandos:
    create      - Crear backup completo
    database    - Backup solo de base de datos
    config      - Backup solo de configuraciones
    restore     - Restaurar desde backup
    list        - Listar backups disponibles
    verify      - Verificar integridad de backup
    cleanup     - Limpiar backups antiguos
    schedule    - Configurar backups automáticos

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import os
import sys
import gzip
import shutil
import tarfile
import hashlib
import argparse
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import json

# Agregar el directorio src al path
sys.path.insert(0, '/opt/cobranza-bot/src')

try:
    from config import Config
    from database import DatabaseManager
except ImportError as e:
    print(f"Error importando módulos: {e}")
    print("Asegúrese de que el sistema esté instalado correctamente")
    sys.exit(1)


@dataclass
class BackupInfo:
    """Información de un backup."""
    filename: str
    timestamp: str
    size_mb: float
    type: str  # 'full', 'database', 'config'
    checksum: str
    description: str
    compressed: bool
    encrypted: bool


class BackupManager:
    """Gestor de backups del sistema."""
    
    def __init__(self):
        """Inicializar el gestor de backups."""
        self.config = Config()
        self.app_dir = Path('/opt/cobranza-bot')
        self.backup_dir = self.app_dir / 'backups'
        self.log_dir = self.app_dir / 'logs'
        
        # Crear directorio de backups si no existe
        self.backup_dir.mkdir(exist_ok=True)
        
        # Configuración de retención
        self.retention_days = 30
        self.max_backups = 50
        
        # Inicializar conexión a base de datos
        try:
            self.db_manager = DatabaseManager()
        except Exception as e:
            print(f"Warning: No se pudo conectar a la base de datos: {e}")
            self.db_manager = None
    
    def calculate_checksum(self, file_path: Path) -> str:
        """Calcular checksum SHA256 de un archivo."""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def compress_file(self, source_path: Path, target_path: Path) -> None:
        """Comprimir un archivo usando gzip."""
        with open(source_path, 'rb') as f_in:
            with gzip.open(target_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
    
    def create_database_backup(self, output_file: Path) -> bool:
        """Crear backup de la base de datos."""
        if not self.db_manager:
            print("Error: No hay conexión a la base de datos")
            return False
        
        try:
            # Usar pg_dump para crear el backup
            cmd = [
                'pg_dump',
                '-h', self.config.database.host,
                '-p', str(self.config.database.port),
                '-U', self.config.database.user,
                '-d', self.config.database.name,
                '--no-password',
                '--verbose',
                '--clean',
                '--if-exists',
                '--create'
            ]
            
            # Configurar variable de entorno para la contraseña
            env = os.environ.copy()
            env['PGPASSWORD'] = self.config.database.password
            
            # Ejecutar pg_dump
            with open(output_file, 'w') as f:
                result = subprocess.run(
                    cmd,
                    stdout=f,
                    stderr=subprocess.PIPE,
                    env=env,
                    text=True
                )
            
            if result.returncode != 0:
                print(f"Error en pg_dump: {result.stderr}")
                return False
            
            print(f"Backup de base de datos creado: {output_file}")
            return True
            
        except Exception as e:
            print(f"Error creando backup de base de datos: {e}")
            return False
    
    def create_config_backup(self, output_file: Path) -> bool:
        """Crear backup de configuraciones."""
        try:
            config_files = [
                self.app_dir / '.env',
                self.app_dir / 'src' / 'config.py',
                self.app_dir / 'systemd' / 'cobranza-bot.service',
                Path('/etc/systemd/system/cobranza-bot.service')
            ]
            
            # Filtrar archivos que existen
            existing_files = [f for f in config_files if f.exists()]
            
            if not existing_files:
                print("Warning: No se encontraron archivos de configuración")
                return False
            
            # Crear archivo tar con las configuraciones
            with tarfile.open(output_file, 'w:gz') as tar:
                for config_file in existing_files:
                    # Usar ruta relativa en el archivo tar
                    arcname = str(config_file).replace('/opt/cobranza-bot/', '')
                    arcname = arcname.replace('/etc/systemd/system/', 'systemd/')
                    tar.add(config_file, arcname=arcname)
            
            print(f"Backup de configuraciones creado: {output_file}")
            return True
            
        except Exception as e:
            print(f"Error creando backup de configuraciones: {e}")
            return False
    
    def create_logs_backup(self, output_file: Path, days: int = 7) -> bool:
        """Crear backup de logs recientes."""
        try:
            if not self.log_dir.exists():
                print("Warning: Directorio de logs no existe")
                return False
            
            # Obtener logs de los últimos N días
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_logs = []
            
            for log_file in self.log_dir.glob('*.log'):
                if log_file.is_file():
                    mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if mtime >= cutoff_date:
                        recent_logs.append(log_file)
            
            if not recent_logs:
                print(f"Warning: No se encontraron logs de los últimos {days} días")
                return False
            
            # Crear archivo tar con los logs
            with tarfile.open(output_file, 'w:gz') as tar:
                for log_file in recent_logs:
                    arcname = f"logs/{log_file.name}"
                    tar.add(log_file, arcname=arcname)
            
            print(f"Backup de logs creado: {output_file} ({len(recent_logs)} archivos)")
            return True
            
        except Exception as e:
            print(f"Error creando backup de logs: {e}")
            return False
    
    def create_full_backup(self, description: str = "") -> Optional[BackupInfo]:
        """Crear backup completo del sistema."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"cobranza_full_{timestamp}"
        
        # Crear directorio temporal para el backup
        temp_dir = self.backup_dir / f"temp_{timestamp}"
        temp_dir.mkdir(exist_ok=True)
        
        try:
            success_count = 0
            total_operations = 3
            
            # 1. Backup de base de datos
            db_file = temp_dir / 'database.sql'
            if self.create_database_backup(db_file):
                success_count += 1
            
            # 2. Backup de configuraciones
            config_file = temp_dir / 'config.tar.gz'
            if self.create_config_backup(config_file):
                success_count += 1
            
            # 3. Backup de logs
            logs_file = temp_dir / 'logs.tar.gz'
            if self.create_logs_backup(logs_file):
                success_count += 1
            
            if success_count == 0:
                print("Error: No se pudo crear ningún componente del backup")
                return None
            
            # Crear archivo tar final
            final_backup = self.backup_dir / f"{backup_name}.tar.gz"
            with tarfile.open(final_backup, 'w:gz') as tar:
                for item in temp_dir.iterdir():
                    if item.is_file():
                        tar.add(item, arcname=item.name)
            
            # Calcular checksum
            checksum = self.calculate_checksum(final_backup)
            
            # Crear información del backup
            size_mb = final_backup.stat().st_size / (1024 * 1024)
            
            backup_info = BackupInfo(
                filename=final_backup.name,
                timestamp=datetime.now().isoformat(),
                size_mb=round(size_mb, 2),
                type='full',
                checksum=checksum,
                description=description or f"Backup completo automático ({success_count}/{total_operations} componentes)",
                compressed=True,
                encrypted=False
            )
            
            # Guardar metadatos
            self.save_backup_metadata(backup_info)
            
            print(f"Backup completo creado: {final_backup}")
            print(f"Tamaño: {size_mb:.2f} MB")
            print(f"Checksum: {checksum}")
            
            return backup_info
            
        except Exception as e:
            print(f"Error creando backup completo: {e}")
            return None
        
        finally:
            # Limpiar directorio temporal
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
    
    def save_backup_metadata(self, backup_info: BackupInfo) -> None:
        """Guardar metadatos del backup."""
        metadata_file = self.backup_dir / f"{backup_info.filename}.json"
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(backup_info.__dict__, f, indent=2, ensure_ascii=False)
    
    def load_backup_metadata(self, backup_filename: str) -> Optional[BackupInfo]:
        """Cargar metadatos de un backup."""
        metadata_file = self.backup_dir / f"{backup_filename}.json"
        
        if not metadata_file.exists():
            return None
        
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return BackupInfo(**data)
        except Exception as e:
            print(f"Error cargando metadatos de {backup_filename}: {e}")
            return None
    
    def list_backups(self) -> List[BackupInfo]:
        """Listar todos los backups disponibles."""
        backups = []
        
        for backup_file in self.backup_dir.glob('*.tar.gz'):
            metadata = self.load_backup_metadata(backup_file.name)
            
            if metadata:
                backups.append(metadata)
            else:
                # Crear metadatos básicos si no existen
                size_mb = backup_file.stat().st_size / (1024 * 1024)
                mtime = datetime.fromtimestamp(backup_file.stat().st_mtime)
                
                backup_info = BackupInfo(
                    filename=backup_file.name,
                    timestamp=mtime.isoformat(),
                    size_mb=round(size_mb, 2),
                    type='unknown',
                    checksum='',
                    description='Backup sin metadatos',
                    compressed=True,
                    encrypted=False
                )
                backups.append(backup_info)
        
        # Ordenar por timestamp (más reciente primero)
        backups.sort(key=lambda x: x.timestamp, reverse=True)
        return backups
    
    def verify_backup(self, backup_filename: str) -> bool:
        """Verificar integridad de un backup."""
        backup_file = self.backup_dir / backup_filename
        
        if not backup_file.exists():
            print(f"Error: Backup {backup_filename} no encontrado")
            return False
        
        metadata = self.load_backup_metadata(backup_filename)
        
        if not metadata:
            print(f"Warning: No hay metadatos para {backup_filename}")
            return True  # Asumir válido si no hay metadatos
        
        if not metadata.checksum:
            print(f"Warning: No hay checksum para {backup_filename}")
            return True
        
        # Verificar checksum
        current_checksum = self.calculate_checksum(backup_file)
        
        if current_checksum == metadata.checksum:
            print(f"✅ Backup {backup_filename} verificado correctamente")
            return True
        else:
            print(f"❌ Backup {backup_filename} corrupto (checksum no coincide)")
            print(f"   Esperado: {metadata.checksum}")
            print(f"   Actual:   {current_checksum}")
            return False
    
    def restore_database(self, backup_file: Path) -> bool:
        """Restaurar base de datos desde backup."""
        if not self.db_manager:
            print("Error: No hay conexión a la base de datos")
            return False
        
        try:
            # Usar psql para restaurar
            cmd = [
                'psql',
                '-h', self.config.database.host,
                '-p', str(self.config.database.port),
                '-U', self.config.database.user,
                '-d', self.config.database.name,
                '-f', str(backup_file)
            ]
            
            # Configurar variable de entorno para la contraseña
            env = os.environ.copy()
            env['PGPASSWORD'] = self.config.database.password
            
            # Ejecutar psql
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                print(f"Error en psql: {result.stderr}")
                return False
            
            print(f"Base de datos restaurada desde: {backup_file}")
            return True
            
        except Exception as e:
            print(f"Error restaurando base de datos: {e}")
            return False
    
    def restore_backup(self, backup_filename: str, components: List[str] = None) -> bool:
        """Restaurar desde backup."""
        backup_file = self.backup_dir / backup_filename
        
        if not backup_file.exists():
            print(f"Error: Backup {backup_filename} no encontrado")
            return False
        
        # Verificar integridad primero
        if not self.verify_backup(backup_filename):
            print("Error: Backup corrupto, abortando restauración")
            return False
        
        # Crear directorio temporal
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        temp_dir = self.backup_dir / f"restore_{timestamp}"
        temp_dir.mkdir(exist_ok=True)
        
        try:
            # Extraer backup
            with tarfile.open(backup_file, 'r:gz') as tar:
                tar.extractall(temp_dir)
            
            success_count = 0
            
            # Restaurar componentes
            if not components or 'database' in components:
                db_file = temp_dir / 'database.sql'
                if db_file.exists():
                    if self.restore_database(db_file):
                        success_count += 1
                        print("✅ Base de datos restaurada")
                    else:
                        print("❌ Error restaurando base de datos")
            
            if not components or 'config' in components:
                config_file = temp_dir / 'config.tar.gz'
                if config_file.exists():
                    print("⚠️  Restauración de configuraciones requiere intervención manual")
                    print(f"   Archivo extraído en: {config_file}")
            
            if success_count > 0:
                print(f"Restauración completada ({success_count} componentes)")
                return True
            else:
                print("No se pudo restaurar ningún componente")
                return False
            
        except Exception as e:
            print(f"Error durante la restauración: {e}")
            return False
        
        finally:
            # Limpiar directorio temporal
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
    
    def cleanup_old_backups(self) -> int:
        """Limpiar backups antiguos según política de retención."""
        backups = self.list_backups()
        
        if len(backups) <= self.max_backups:
            cutoff_date = datetime.now() - timedelta(days=self.retention_days)
            backups_to_delete = [
                b for b in backups 
                if datetime.fromisoformat(b.timestamp.replace('Z', '+00:00').replace('+00:00', '')) < cutoff_date
            ]
        else:
            # Mantener solo los más recientes
            backups_to_delete = backups[self.max_backups:]
        
        deleted_count = 0
        
        for backup in backups_to_delete:
            backup_file = self.backup_dir / backup.filename
            metadata_file = self.backup_dir / f"{backup.filename}.json"
            
            try:
                if backup_file.exists():
                    backup_file.unlink()
                if metadata_file.exists():
                    metadata_file.unlink()
                
                deleted_count += 1
                print(f"Eliminado: {backup.filename}")
                
            except Exception as e:
                print(f"Error eliminando {backup.filename}: {e}")
        
        return deleted_count
    
    def setup_cron_job(self, schedule: str = "0 2 * * *") -> bool:
        """Configurar tarea cron para backups automáticos."""
        try:
            cron_command = f"{self.app_dir}/venv/bin/python3 {self.app_dir}/backup.py create"
            cron_line = f"{schedule} {cron_command}\n"
            
            # Leer crontab actual
            result = subprocess.run(
                ['crontab', '-l'],
                capture_output=True,
                text=True
            )
            
            current_cron = result.stdout if result.returncode == 0 else ""
            
            # Verificar si ya existe la tarea
            if cron_command in current_cron:
                print("Tarea cron ya existe")
                return True
            
            # Agregar nueva tarea
            new_cron = current_cron + cron_line
            
            # Escribir nuevo crontab
            process = subprocess.Popen(
                ['crontab', '-'],
                stdin=subprocess.PIPE,
                text=True
            )
            process.communicate(input=new_cron)
            
            if process.returncode == 0:
                print(f"Tarea cron configurada: {schedule}")
                return True
            else:
                print("Error configurando tarea cron")
                return False
                
        except Exception as e:
            print(f"Error configurando cron: {e}")
            return False


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(
        description='Sistema de Backup para Cobranza Bot'
    )
    
    parser.add_argument(
        'command',
        choices=['create', 'database', 'config', 'restore', 'list', 'verify', 'cleanup', 'schedule'],
        help='Comando a ejecutar'
    )
    
    parser.add_argument(
        '--file', '-f',
        help='Archivo de backup para restaurar o verificar'
    )
    
    parser.add_argument(
        '--description', '-d',
        help='Descripción del backup'
    )
    
    parser.add_argument(
        '--components', '-c',
        nargs='+',
        choices=['database', 'config', 'logs'],
        help='Componentes a restaurar'
    )
    
    parser.add_argument(
        '--schedule', '-s',
        default='0 2 * * *',
        help='Programación cron (default: 0 2 * * * - diario a las 2 AM)'
    )
    
    args = parser.parse_args()
    
    backup_manager = BackupManager()
    
    if args.command == 'create':
        backup_info = backup_manager.create_full_backup(args.description or "")
        if backup_info:
            print("✅ Backup creado exitosamente")
        else:
            print("❌ Error creando backup")
            sys.exit(1)
    
    elif args.command == 'database':
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = backup_manager.backup_dir / f"database_{timestamp}.sql"
        
        if backup_manager.create_database_backup(output_file):
            print("✅ Backup de base de datos creado")
        else:
            print("❌ Error creando backup de base de datos")
            sys.exit(1)
    
    elif args.command == 'config':
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = backup_manager.backup_dir / f"config_{timestamp}.tar.gz"
        
        if backup_manager.create_config_backup(output_file):
            print("✅ Backup de configuraciones creado")
        else:
            print("❌ Error creando backup de configuraciones")
            sys.exit(1)
    
    elif args.command == 'restore':
        if not args.file:
            print("Error: Especifique el archivo de backup con --file")
            sys.exit(1)
        
        if backup_manager.restore_backup(args.file, args.components):
            print("✅ Restauración completada")
        else:
            print("❌ Error durante la restauración")
            sys.exit(1)
    
    elif args.command == 'list':
        backups = backup_manager.list_backups()
        
        if not backups:
            print("No hay backups disponibles")
            return
        
        print(f"\n{'Archivo':<30} {'Fecha':<20} {'Tamaño':<10} {'Tipo':<10} {'Descripción'}")
        print("-" * 100)
        
        for backup in backups:
            date_str = backup.timestamp[:19].replace('T', ' ')
            print(f"{backup.filename:<30} {date_str:<20} {backup.size_mb:<10.1f} {backup.type:<10} {backup.description}")
    
    elif args.command == 'verify':
        if not args.file:
            print("Error: Especifique el archivo de backup con --file")
            sys.exit(1)
        
        if backup_manager.verify_backup(args.file):
            print("✅ Verificación exitosa")
        else:
            print("❌ Verificación fallida")
            sys.exit(1)
    
    elif args.command == 'cleanup':
        deleted_count = backup_manager.cleanup_old_backups()
        print(f"✅ Limpieza completada: {deleted_count} backups eliminados")
    
    elif args.command == 'schedule':
        if backup_manager.setup_cron_job(args.schedule):
            print("✅ Backup automático configurado")
        else:
            print("❌ Error configurando backup automático")
            sys.exit(1)


if __name__ == '__main__':
    main()