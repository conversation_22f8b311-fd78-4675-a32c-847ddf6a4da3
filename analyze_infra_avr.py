#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Analizador de Comportamiento de infra_avr
=========================================

Script para interceptar y analizar exactamente cómo
funciona tu solución propietaria infra_avr.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import struct
import time
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class InfraAVRAnalyzer:
    """Analizador del comportamiento de infra_avr."""
    
    def __init__(self, listen_port=5002, target_port=5001):
        self.listen_port = listen_port
        self.target_port = target_port
        self.message_times = []
        self.chunk_sizes = []
        self.intervals = []
        
    async def start_analyzer(self):
        """Inicia el analizador."""
        server = await asyncio.start_server(
            self.handle_connection,
            '127.0.0.1',
            self.listen_port
        )
        
        logger.info("🔍 ANALIZADOR DE INFRA_AVR INICIADO")
        logger.info("=" * 60)
        logger.info("📋 INSTRUCCIONES:")
        logger.info("1. Cambia tu dialplan temporalmente:")
        logger.info("   AudioSocket(${UUID},127.0.0.1:5002)")
        logger.info("2. Ejecuta infra_avr en puerto 5001")
        logger.info("3. Haz una llamada de prueba")
        logger.info("4. Observa los patrones exactos")
        logger.info("=" * 60)
        
        async with server:
            await server.serve_forever()
    
    async def handle_connection(self, client_reader, client_writer):
        """Maneja conexión interceptada."""
        client_addr = client_writer.get_extra_info('peername')
        logger.info(f"🔗 Nueva conexión desde {client_addr}")
        
        try:
            # Conectar al infra_avr real
            target_reader, target_writer = await asyncio.open_connection(
                '127.0.0.1', self.target_port
            )
            
            logger.info("✅ Conectado a infra_avr")
            
            # Crear tareas para analizar tráfico
            tasks = [
                asyncio.create_task(self.analyze_traffic(
                    client_reader, target_writer, "ASTERISK → infra_avr"
                )),
                asyncio.create_task(self.analyze_traffic(
                    target_reader, client_writer, "infra_avr → ASTERISK"
                ))
            ]
            
            done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            
            for task in pending:
                task.cancel()
                
        except Exception as e:
            logger.error(f"❌ Error: {e}")
        finally:
            client_writer.close()
            if 'target_writer' in locals():
                target_writer.close()
            
            # Mostrar estadísticas finales
            self.show_statistics()
    
    async def analyze_traffic(self, reader, writer, direction):
        """Analiza el tráfico en una dirección."""
        packet_count = 0
        last_time = time.time()
        
        try:
            while True:
                # Leer header AudioSocket
                header = await reader.read(3)
                if len(header) != 3:
                    break
                
                msg_type, payload_length = struct.unpack('>BH', header)
                packet_count += 1
                current_time = time.time()
                
                # Leer payload
                payload = b''
                if payload_length > 0:
                    payload = await reader.read(payload_length)
                
                # Calcular timing
                interval = current_time - last_time
                self.intervals.append(interval)
                
                # Analizar mensaje
                self.analyze_message(direction, msg_type, payload_length, payload, 
                                   packet_count, interval, current_time)
                
                # Reenviar datos
                writer.write(header + payload)
                await writer.drain()
                
                last_time = current_time
                
        except Exception as e:
            logger.error(f"❌ Error en {direction}: {e}")
    
    def analyze_message(self, direction, msg_type, payload_length, payload, 
                       packet_count, interval, timestamp):
        """Analiza un mensaje específico."""
        time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S.%f")[:-3]
        
        if msg_type == 0x01:  # UUID
            logger.info(f"🆔 {time_str} {direction}: UUID")
            
        elif msg_type == 0x10:  # Audio data
            self.chunk_sizes.append(payload_length)
            self.message_times.append(timestamp)
            
            # Analizar contenido de audio
            audio_info = "DATOS"
            if len(payload) >= 20:
                try:
                    first_samples = struct.unpack('<10h', payload[:20])
                    max_amp = max(abs(s) for s in first_samples)
                    audio_info = f"max_amp:{max_amp}"
                    if max_amp < 100:
                        audio_info += " (SILENCIO)"
                except:
                    pass
            
            # CRÍTICO: Mostrar timing exacto
            logger.info(f"🎵 {time_str} {direction}: Audio #{packet_count} - "
                       f"{payload_length} bytes, interval: {interval*1000:.1f}ms, {audio_info}")
            
            # Detectar patrones
            if packet_count % 10 == 0:
                recent_intervals = self.intervals[-10:]
                avg_interval = sum(recent_intervals) / len(recent_intervals)
                logger.info(f"📊 Promedio últimos 10 chunks: {avg_interval*1000:.1f}ms")
                
        elif msg_type == 0x00:  # Terminate
            logger.info(f"🛑 {time_str} {direction}: TERMINATE")
            
        else:
            logger.info(f"❓ {time_str} {direction}: Tipo 0x{msg_type:02x} - {payload_length} bytes")
    
    def show_statistics(self):
        """Muestra estadísticas finales."""
        if not self.intervals:
            return
            
        logger.info("\n" + "=" * 60)
        logger.info("📊 ESTADÍSTICAS FINALES DE INFRA_AVR")
        logger.info("=" * 60)
        
        # Estadísticas de timing
        avg_interval = sum(self.intervals) / len(self.intervals)
        min_interval = min(self.intervals)
        max_interval = max(self.intervals)
        
        logger.info(f"⏱️  TIMING:")
        logger.info(f"   Promedio entre chunks: {avg_interval*1000:.1f}ms")
        logger.info(f"   Mínimo: {min_interval*1000:.1f}ms")
        logger.info(f"   Máximo: {max_interval*1000:.1f}ms")
        
        # Estadísticas de chunks
        if self.chunk_sizes:
            avg_chunk = sum(self.chunk_sizes) / len(self.chunk_sizes)
            unique_sizes = set(self.chunk_sizes)
            
            logger.info(f"📦 CHUNKS:")
            logger.info(f"   Total chunks: {len(self.chunk_sizes)}")
            logger.info(f"   Tamaño promedio: {avg_chunk:.1f} bytes")
            logger.info(f"   Tamaños únicos: {sorted(unique_sizes)}")
        
        # Duración total
        if self.message_times:
            total_duration = self.message_times[-1] - self.message_times[0]
            logger.info(f"⏰ DURACIÓN TOTAL: {total_duration:.3f}s")
            
            if len(self.chunk_sizes) > 0:
                chunks_per_second = len(self.chunk_sizes) / total_duration
                bytes_per_second = sum(self.chunk_sizes) / total_duration
                logger.info(f"📈 VELOCIDAD:")
                logger.info(f"   {chunks_per_second:.1f} chunks/segundo")
                logger.info(f"   {bytes_per_second:.0f} bytes/segundo")
        
        logger.info("=" * 60)


async def main():
    """Función principal."""
    analyzer = InfraAVRAnalyzer()
    
    try:
        await analyzer.start_analyzer()
    except KeyboardInterrupt:
        logger.info("🛑 Análisis detenido")


if __name__ == "__main__":
    asyncio.run(main())
