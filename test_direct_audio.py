#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test de Audio Directo para AudioSocket
=====================================

Envía audio de prueba directo (tono) para verificar
si el problema es el TTS o la configuración de AudioSocket.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import struct
import math
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_test_tone(frequency=440, duration_seconds=3, sample_rate=8000):
    """Genera un tono de prueba (beep) en formato PCM."""
    samples = []
    
    for i in range(int(sample_rate * duration_seconds)):
        # Generar onda senoidal
        t = i / sample_rate
        amplitude = 0.3  # 30% del máximo para evitar saturación
        sample = int(amplitude * 32767 * math.sin(2 * math.pi * frequency * t))
        
        # Convertir a bytes (16-bit little-endian)
        sample_bytes = struct.pack('<h', sample)
        samples.append(sample_bytes)
    
    return b''.join(samples)


async def test_audiosocket_with_tone():
    """Prueba AudioSocket con un tono de prueba."""
    
    logger.info("=== PRUEBA DE AUDIOSOCKET CON TONO ===")
    
    try:
        # Generar tono de prueba
        logger.info("Generando tono de prueba (440Hz, 3 segundos)")
        test_audio = generate_test_tone(frequency=440, duration_seconds=3)
        logger.info(f"Tono generado: {len(test_audio)} bytes")
        
        # Conectar a AudioSocket
        logger.info("Conectando a AudioSocket...")
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection('127.0.0.1', 5001),
            timeout=10.0
        )
        logger.info("✅ Conectado a AudioSocket")
        
        # Configurar socket para mejor performance
        transport = writer.get_extra_info('socket')
        if transport:
            import socket
            transport.setsockopt(socket.SOL_TCP, socket.TCP_NODELAY, 1)
            logger.info("✅ TCP_NODELAY configurado")
        
        # Enviar UUID falso
        fake_uuid = b'\x01\x23\x45\x67\x89\xab\xcd\xef\x01\x23\x45\x67\x89\xab\xcd\xef'
        uuid_header = struct.pack('>BH', 0x01, 16)
        writer.write(uuid_header + fake_uuid)
        await writer.drain()
        logger.info("✅ UUID enviado")
        
        # Esperar un momento para que Asterisk procese
        await asyncio.sleep(1.0)
        
        # Enviar audio en chunks muy pequeños para debugging
        chunk_size = 320  # 40ms de audio a 8kHz
        total_chunks = (len(test_audio) + chunk_size - 1) // chunk_size
        
        logger.info(f"Enviando {len(test_audio)} bytes en {total_chunks} chunks de {chunk_size} bytes")
        logger.info("🔊 DEBERÍAS ESCUCHAR UN TONO AHORA...")
        
        for i in range(0, len(test_audio), chunk_size):
            chunk = test_audio[i:i + chunk_size]
            header = struct.pack('>BH', 0x10, len(chunk))
            
            chunk_num = i // chunk_size + 1
            logger.info(f"📤 Enviando chunk {chunk_num}/{total_chunks}: {len(chunk)} bytes")
            
            writer.write(header + chunk)
            await asyncio.wait_for(writer.drain(), timeout=2.0)
            
            # Pausa muy pequeña entre chunks
            await asyncio.sleep(0.04)  # 40ms = tiempo real del chunk
        
        logger.info("✅ Audio enviado completamente")
        logger.info("🔊 ¿ESCUCHASTE EL TONO?")
        
        # Mantener conexión un momento más
        await asyncio.sleep(2.0)
        
        # Enviar mensaje de terminación
        termination_header = struct.pack('>BH', 0x00, 0)
        writer.write(termination_header)
        await writer.drain()
        logger.info("✅ Mensaje de terminación enviado")
        
        writer.close()
        await writer.wait_closed()
        logger.info("✅ Conexión cerrada")
        
    except Exception as e:
        logger.error(f"❌ Error en prueba: {e}")


async def test_multiple_tones():
    """Prueba con múltiples tonos para verificar timing."""
    
    logger.info("=== PRUEBA DE MÚLTIPLES TONOS ===")
    
    frequencies = [440, 880, 1320]  # La, La octava alta, Mi
    
    for freq in frequencies:
        logger.info(f"\n🎵 Probando tono de {freq}Hz...")
        
        try:
            # Generar tono corto
            test_audio = generate_test_tone(frequency=freq, duration_seconds=1)
            
            # Conectar
            reader, writer = await asyncio.open_connection('127.0.0.1', 5001)
            
            # UUID
            fake_uuid = b'\x01\x23\x45\x67\x89\xab\xcd\xef\x01\x23\x45\x67\x89\xab\xcd\xef'
            uuid_header = struct.pack('>BH', 0x01, 16)
            writer.write(uuid_header + fake_uuid)
            await writer.drain()
            
            # Enviar audio rápido
            chunk_size = 1024
            for i in range(0, len(test_audio), chunk_size):
                chunk = test_audio[i:i + chunk_size]
                header = struct.pack('>BH', 0x10, len(chunk))
                writer.write(header + chunk)
                await writer.drain()
                await asyncio.sleep(0.01)
            
            logger.info(f"✅ Tono {freq}Hz enviado")
            
            # Cerrar
            writer.close()
            await writer.wait_closed()
            
            # Pausa entre tonos
            await asyncio.sleep(2.0)
            
        except Exception as e:
            logger.error(f"❌ Error con tono {freq}Hz: {e}")


async def main():
    """Función principal."""
    
    logger.info("🎵 INICIANDO PRUEBAS DE AUDIO DIRECTO")
    logger.info("📞 ASEGÚRATE DE ESTAR EN UNA LLAMADA AL 8000")
    
    # Esperar confirmación
    logger.info("⏳ Esperando 3 segundos para que inicies la llamada...")
    await asyncio.sleep(3.0)
    
    # Prueba 1: Tono único
    await test_audiosocket_with_tone()
    
    await asyncio.sleep(3.0)
    
    # Prueba 2: Múltiples tonos
    await test_multiple_tones()
    
    logger.info("🏁 PRUEBAS COMPLETADAS")
    logger.info("📋 REPORTE:")
    logger.info("   - Si escuchaste los tonos: El problema es el TTS")
    logger.info("   - Si NO escuchaste nada: El problema es AudioSocket/Asterisk")


if __name__ == "__main__":
    asyncio.run(main())
