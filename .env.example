#!/usr/bin/env bash
# -*- coding: utf-8 -*-

# ============================================================================
# Bot de Cobranza con IA - Variables de Entorno
# ============================================================================
# Archivo de configuración para entorno virtual /opt/cobranza-bot/venv
# 
# IMPORTANTE: 
# - Copiar este archivo como .env y completar con valores reales
# - NO subir el archivo .env al repositorio (incluir en .gitignore)
# - Usar valores seguros en producción
#
# Autor: Sistema de Cobranza Automatizada
# Fecha: 2024
# ============================================================================

# ===================
# APIs EXTERNAS
# ===================

# OpenAI API Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=150
OPENAI_TEMPERATURE=0.7

# Google Cloud Text-to-Speech Configuration
GOOGLE_TTS_CREDENTIALS_PATH=/path/to/your/google-credentials.json
GOOGLE_TTS_PROJECT_ID=your-google-cloud-project-id
GOOGLE_TTS_LANGUAGE_CODE=es-US
GOOGLE_TTS_VOICE_NAME=es-US-Chirp3-HD-Achernar
GOOGLE_TTS_AUDIO_ENCODING=LINEAR16
GOOGLE_TTS_SAMPLE_RATE=24000
GOOGLE_TTS_SPEAKING_RATE=1.0
GOOGLE_TTS_PITCH=0.0
GOOGLE_TTS_VOLUME_GAIN=0.0

# Deepgram API Configuration (ASR Streaming)
DEEPGRAM_API_KEY=your-deepgram-api-key-here
DEEPGRAM_MODEL=nova-2
DEEPGRAM_LANGUAGE=es
DEEPGRAM_ENCODING=linear16
DEEPGRAM_SAMPLE_RATE=8000

# ===================
# BASE DE DATOS
# ===================

# PostgreSQL Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cobranza_bot
DB_USER=cobranza_user
DB_PASSWORD=your-secure-password-here
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# ===================
# AUDIOSOCKET CONFIG
# ===================

# AudioSocket Server Configuration (OPTIMIZADO PARA ESTABILIDAD)
AUDIOSOCKET_HOST=127.0.0.1
AUDIOSOCKET_PORT=5001
AUDIOSOCKET_BUFFER_SIZE=1024          # Buffer pequeño para mejor estabilidad
AUDIOSOCKET_SAMPLE_RATE=8000
AUDIOSOCKET_CHANNELS=1

# Configuraciones de Timeout y Reintentos para AudioSocket
AUDIOSOCKET_CHUNK_TIMEOUT=2.0         # Timeout por chunk en segundos
AUDIOSOCKET_MAX_RETRIES=3             # Número máximo de reintentos
AUDIOSOCKET_RETRY_DELAY=0.1           # Delay inicial entre reintentos
AUDIOSOCKET_CONNECTION_TIMEOUT=10.0   # Timeout de conexión inicial

# Configuraciones de Optimización de Red
AUDIOSOCKET_CHUNK_PAUSE_INTERVAL=10   # Pausa cada N chunks
AUDIOSOCKET_CHUNK_PAUSE_DURATION=0.001 # Duración de pausa (1ms)

# ===================
# LOGGING Y MONITORING
# ===================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/var/log/cobranza-bot/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ===================
# CONFIGURACIÓN DE IA
# ===================

# Conversation Settings
CONVERSATION_TIMEOUT=300
MAX_CONVERSATION_TURNS=20
SILENCE_TIMEOUT=5
INTERRUPTION_THRESHOLD=0.5

# ===================
# CONFIGURACIÓN DE AUDIO
# ===================

# Audio Processing
AUDIO_CHUNK_SIZE=1024
AUDIO_FORMAT=16
AUDIO_RATE=8000
AUDIO_CHANNELS=1
VAD_AGGRESSIVENESS=2

# TTS Configuration
TTS_SPEED=1.0
TTS_STABILITY=0.75
TTS_CLARITY=0.75

# STT Configuration
STT_LANGUAGE=es
STT_MODEL=whisper-1

# ===================
# CONFIGURACIÓN DE SEGURIDAD
# ===================

# Security Settings
SECRET_KEY=your-secret-key-for-encryption
ENCRYPTION_ALGORITHM=AES-256
SESSION_TIMEOUT=1800

# ===================
# CONFIGURACIÓN DE DESARROLLO
# ===================

# Development Settings
DEBUG=False
TESTING=False
DEVELOPMENT_MODE=False

# Virtual Environment Path
VENV_PATH=/opt/cobranza-bot/venv

# ===================
# CONFIGURACIÓN DE ASTERISK
# ===================

# Asterisk Integration
ASTERISK_HOST=localhost
ASTERISK_AMI_PORT=5038
ASTERISK_AMI_USER=admin
ASTERISK_AMI_SECRET=your-ami-secret

# ===================
# CONFIGURACIÓN DE COBRANZA
# ===================

# Business Logic
MAX_DEBT_AMOUNT=50000.00
MIN_PAYMENT_AMOUNT=100.00
MAX_PAYMENT_DAYS=90
DEFAULT_PAYMENT_DAYS=30

# ===================
# CONFIGURACIÓN DE PERFORMANCE
# ===================

# Performance Settings
MAX_CONCURRENT_CALLS=10
CONNECTION_POOL_SIZE=20
REQUEST_TIMEOUT=30
RETRY_ATTEMPTS=3
RETRY_DELAY=1