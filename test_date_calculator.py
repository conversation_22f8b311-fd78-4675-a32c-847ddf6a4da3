#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script de prueba para el calculador de fechas optimizado.
Prueba todas las frases comunes que los clientes pueden usar.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.date_calculator import DateCalculator
from datetime import datetime, date


def test_comprehensive_date_calculator():
    """Prueba exhaustiva del calculador de fechas."""
    calculator = DateCalculator()
    
    # Frases de prueba organizadas por categorías
    test_cases = {
        "Inmediato": [
            "hoy",
            "ahorita",
            "hoy mismo",
            "en el día"
        ],
        
        "Próximos días": [
            "mañana",
            "manana",
            "pasado mañana",
            "pasado manana",
            "en dos días",
            "en dos dias",
            "en tres días",
            "en tres dias"
        ],
        
        "Días de la semana": [
            "lunes",
            "el lunes",
            "este lunes",
            "el próximo lunes",
            "el proximo lunes",
            "lunes que viene",
            "martes",
            "el martes",
            "miércoles",
            "miercoles",
            "jueves",
            "el jueves",
            "este jueves",
            "viernes",
            "el viernes",
            "sábado",
            "sabado",
            "domingo"
        ],
        
        "Períodos largos": [
            "en una semana",
            "la próxima semana",
            "la proxima semana",
            "en dos semanas",
            "el próximo mes",
            "el proximo mes"
        ],
        
        "Fechas específicas": [
            "15 de marzo",
            "20 de abril",
            "5 de mayo",
            "15/03",
            "20/04",
            "25-12"
        ],
        
        "Frases complejas": [
            "mañana por la mañana",
            "el próximo viernes por favor",
            "este jueves que viene",
            "el lunes de la próxima semana",
            "pasado mañana sin falta"
        ]
    }
    
    print("=" * 80)
    print("PRUEBA EXHAUSTIVA DEL CALCULADOR DE FECHAS")
    print("=" * 80)
    print(f"Fecha actual: {datetime.now().date()}")
    print()
    
    total_tests = 0
    successful_tests = 0
    
    for category, phrases in test_cases.items():
        print(f"\n📅 {category.upper()}")
        print("-" * 50)
        
        for phrase in phrases:
            total_tests += 1
            result = calculator.parse_natural_date(phrase)
            
            if result:
                successful_tests += 1
                days_diff = (result - datetime.now().date()).days
                status = "✅"
                detail = f"-> {result} (en {days_diff} días)"
            else:
                status = "❌"
                detail = "-> No se pudo calcular"
            
            print(f"{status} '{phrase}' {detail}")
    
    print("\n" + "=" * 80)
    print("ESTADÍSTICAS")
    print("=" * 80)
    print(f"Total de pruebas: {total_tests}")
    print(f"Exitosas: {successful_tests}")
    print(f"Fallidas: {total_tests - successful_tests}")
    print(f"Tasa de éxito: {(successful_tests/total_tests)*100:.1f}%")
    
    # Mostrar estadísticas del cache
    cache_stats = calculator.get_cache_stats()
    print(f"\nCache LRU:")
    print(f"  - Hits: {cache_stats['lru_hits']}")
    print(f"  - Misses: {cache_stats['lru_misses']}")
    print(f"  - Tamaño actual: {cache_stats['lru_currsize']}")
    print(f"  - Tamaño máximo: {cache_stats['lru_maxsize']}")
    
    return successful_tests == total_tests


def test_performance():
    """Prueba de rendimiento del calculador."""
    calculator = DateCalculator()
    
    # Frases comunes para prueba de velocidad
    common_phrases = [
        "mañana", "pasado mañana", "el próximo lunes", "viernes",
        "en una semana", "hoy", "jueves que viene"
    ]
    
    print("\n" + "=" * 80)
    print("PRUEBA DE RENDIMIENTO")
    print("=" * 80)
    
    import time
    
    # Primera pasada (sin cache)
    start_time = time.time()
    for _ in range(1000):
        for phrase in common_phrases:
            calculator.parse_natural_date(phrase)
    first_pass_time = time.time() - start_time
    
    # Segunda pasada (con cache)
    start_time = time.time()
    for _ in range(1000):
        for phrase in common_phrases:
            calculator.parse_natural_date(phrase)
    second_pass_time = time.time() - start_time
    
    print(f"Primera pasada (sin cache): {first_pass_time:.4f} segundos")
    print(f"Segunda pasada (con cache): {second_pass_time:.4f} segundos")
    print(f"Mejora de velocidad: {first_pass_time/second_pass_time:.1f}x")
    
    cache_stats = calculator.get_cache_stats()
    print(f"Cache hits: {cache_stats['lru_hits']}")
    print(f"Cache misses: {cache_stats['lru_misses']}")


if __name__ == "__main__":
    print("Iniciando pruebas del calculador de fechas...")
    
    # Prueba exhaustiva
    success = test_comprehensive_date_calculator()
    
    # Prueba de rendimiento
    test_performance()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 TODAS LAS PRUEBAS PASARON EXITOSAMENTE")
    else:
        print("⚠️  ALGUNAS PRUEBAS FALLARON")
    print("=" * 80)
