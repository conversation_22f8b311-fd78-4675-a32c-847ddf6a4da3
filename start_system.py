#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Script de Inicio del Sistema
====================================================

Este script facilita el inicio, parada y gestión del sistema de cobranza.
Incluye opciones para:
- Iniciar el sistema completo
- Verificar configuración
- Ejecutar pruebas
- Monitorear estado
- Gestionar logs

Uso:
    python start_system.py [comando] [opciones]

Comandos:
    start       - Inicia el sistema
    stop        - Detiene el sistema
    restart     - Reinicia el sistema
    status      - Muestra estado del sistema
    test        - Ejecuta pruebas
    config      - Verifica configuración
    logs        - Muestra logs del sistema

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import sys
import os
import asyncio
import argparse
import signal
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

# Agregar directorio src al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from main import CobranzaBotSystem
    from config import Config
    from database import DatabaseManager
    from utils import ConfigValidator
except ImportError as e:
    print(f"Error importando módulos: {e}")
    print("Asegúrese de que todos los archivos estén en el directorio correcto")
    sys.exit(1)


class SystemManager:
    """Gestor del sistema de cobranza."""
    
    def __init__(self):
        self.system: Optional[CobranzaBotSystem] = None
        self.config: Optional[Config] = None
        self.pid_file = "/tmp/cobranza_bot.pid"
        self.log_file = "/opt/cobranza-bot/logs/system.log"
    
    def load_config(self) -> bool:
        """Carga y valida la configuración."""
        try:
            self.config = Config()
            if not self.config.validate():
                print("❌ Configuración inválida")
                return False
            
            print("✅ Configuración cargada correctamente")
            return True
            
        except Exception as e:
            print(f"❌ Error cargando configuración: {e}")
            return False
    
    def check_dependencies(self) -> bool:
        """Verifica dependencias del sistema."""
        print("🔍 Verificando dependencias...")
        
        dependencies = {
            'asyncio': 'Biblioteca asyncio',
            'asyncpg': 'Cliente PostgreSQL async',
            'openai': 'Cliente OpenAI',
            'deepgram': 'SDK Deepgram',
            'elevenlabs': 'SDK ElevenLabs'
        }
        
        missing = []
        for dep, desc in dependencies.items():
            try:
                __import__(dep)
                print(f"  ✅ {desc}")
            except ImportError:
                print(f"  ❌ {desc} - No encontrado")
                missing.append(dep)
        
        if missing:
            print(f"\n❌ Dependencias faltantes: {', '.join(missing)}")
            print("Ejecute: pip install -r requirements.txt")
            return False
        
        print("✅ Todas las dependencias están disponibles")
        return True
    
    async def test_database_connection(self) -> bool:
        """Prueba la conexión a la base de datos."""
        print("🔍 Probando conexión a base de datos...")
        
        try:
            if not self.config:
                return False
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Probar consulta simple
            result = await db_manager.execute_query("SELECT 1 as test")
            if result and result[0]['test'] == 1:
                print("✅ Conexión a base de datos exitosa")
                await db_manager.close()
                return True
            else:
                print("❌ Error en consulta de prueba")
                return False
                
        except Exception as e:
            print(f"❌ Error conectando a base de datos: {e}")
            return False
    
    async def test_external_apis(self) -> bool:
        """Prueba conexiones a APIs externas."""
        print("🔍 Probando APIs externas...")
        
        if not self.config:
            return False
        
        success = True
        
        # Probar OpenAI
        try:
            from openai import AsyncOpenAI
            client = AsyncOpenAI(api_key=self.config.openai.api_key)
            response = await client.chat.completions.create(
                model=self.config.openai.model,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=5
            )
            print("  ✅ OpenAI API")
        except Exception as e:
            print(f"  ❌ OpenAI API: {e}")
            success = False
        
        # Probar Deepgram
        try:
            from deepgram import DeepgramClient
            client = DeepgramClient(self.config.deepgram.api_key)
            # Solo verificar que el cliente se crea correctamente
            print("  ✅ Deepgram API")
        except Exception as e:
            print(f"  ❌ Deepgram API: {e}")
            success = False
        
        # Probar ElevenLabs
        try:
            from elevenlabs.client import ElevenLabs
            client = ElevenLabs(api_key=self.config.elevenlabs.api_key)
            print("  ✅ ElevenLabs API")
        except Exception as e:
            print(f"  ❌ ElevenLabs API: {e}")
            success = False
        
        return success
    
    def is_running(self) -> bool:
        """Verifica si el sistema está ejecutándose."""
        try:
            if os.path.exists(self.pid_file):
                with open(self.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                # Verificar si el proceso existe
                try:
                    os.kill(pid, 0)
                    return True
                except OSError:
                    # Proceso no existe, limpiar PID file
                    os.remove(self.pid_file)
                    return False
            
            return False
            
        except Exception:
            return False
    
    def save_pid(self) -> None:
        """Guarda el PID del proceso actual."""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
        except Exception as e:
            print(f"⚠️  No se pudo guardar PID: {e}")
    
    def remove_pid(self) -> None:
        """Remueve el archivo PID."""
        try:
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
        except Exception as e:
            print(f"⚠️  No se pudo remover PID: {e}")
    
    async def start_system(self) -> bool:
        """Inicia el sistema completo."""
        try:
            if self.is_running():
                print("⚠️  El sistema ya está ejecutándose")
                return False
            
            print("🚀 Iniciando sistema de cobranza con IA...")
            
            # Crear directorio de logs si no existe
            log_dir = os.path.dirname(self.log_file)
            os.makedirs(log_dir, exist_ok=True)
            
            # Crear sistema
            self.system = CobranzaBotSystem()
            
            # Configurar manejo de señales
            def signal_handler(signum, frame):
                print(f"\n📡 Señal {signum} recibida. Deteniendo sistema...")
                asyncio.create_task(self.stop_system())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # Guardar PID
            self.save_pid()
            
            # Inicializar y iniciar
            await self.system.initialize()
            await self.system.start()
            
            print("✅ Sistema iniciado correctamente")
            print(f"📊 Puerto AudioSocket: {self.config.audiosocket.port}")
            print("📝 Presione Ctrl+C para detener")
            
            # Mantener sistema ejecutándose
            while self.system.is_running:
                await asyncio.sleep(1)
            
            return True
            
        except Exception as e:
            print(f"❌ Error iniciando sistema: {e}")
            await self.stop_system()
            return False
    
    async def stop_system(self) -> bool:
        """Detiene el sistema."""
        try:
            print("🛑 Deteniendo sistema...")
            
            if self.system:
                await self.system.stop()
                self.system = None
            
            self.remove_pid()
            print("✅ Sistema detenido")
            return True
            
        except Exception as e:
            print(f"❌ Error deteniendo sistema: {e}")
            return False
    
    async def restart_system(self) -> bool:
        """Reinicia el sistema."""
        print("🔄 Reiniciando sistema...")
        
        if self.is_running():
            await self.stop_system()
            await asyncio.sleep(2)
        
        return await self.start_system()
    
    async def show_status(self) -> None:
        """Muestra el estado del sistema."""
        print("📊 Estado del Sistema de Cobranza")
        print("=" * 40)
        
        # Estado básico
        running = self.is_running()
        print(f"Estado: {'🟢 Ejecutándose' if running else '🔴 Detenido'}")
        
        if running:
            try:
                # Intentar conectar y obtener estadísticas
                # Esto requeriría una API de estado o conexión directa
                print("📈 Estadísticas no disponibles (sistema en ejecución)")
            except Exception:
                print("⚠️  No se pudieron obtener estadísticas")
        
        # Información de configuración
        if self.config:
            print(f"\n🔧 Configuración:")
            print(f"  Puerto AudioSocket: {self.config.audiosocket.port}")
            print(f"  Base de datos: {self.config.database.host}:{self.config.database.port}")
            print(f"  Modelo OpenAI: {self.config.openai.model}")
            print(f"  Modelo Deepgram: {self.config.deepgram.model}")
            print(f"  Modelo ElevenLabs: {self.config.elevenlabs.model_id}")
        
        # Información de archivos
        print(f"\n📁 Archivos:")
        print(f"  PID: {self.pid_file} {'✅' if os.path.exists(self.pid_file) else '❌'}")
        print(f"  Log: {self.log_file} {'✅' if os.path.exists(self.log_file) else '❌'}")
    
    async def run_tests(self) -> bool:
        """Ejecuta pruebas del sistema."""
        print("🧪 Ejecutando pruebas del sistema...")
        print("=" * 40)
        
        success = True
        
        # Verificar dependencias
        if not self.check_dependencies():
            success = False
        
        print()
        
        # Verificar configuración
        if not self.load_config():
            success = False
        
        print()
        
        # Probar base de datos
        if not await self.test_database_connection():
            success = False
        
        print()
        
        # Probar APIs externas
        if not await self.test_external_apis():
            success = False
        
        print("\n" + "=" * 40)
        if success:
            print("✅ Todas las pruebas pasaron")
        else:
            print("❌ Algunas pruebas fallaron")
        
        return success
    
    def show_logs(self, lines: int = 50) -> None:
        """Muestra los logs del sistema."""
        try:
            if not os.path.exists(self.log_file):
                print(f"❌ Archivo de log no encontrado: {self.log_file}")
                return
            
            print(f"📝 Últimas {lines} líneas del log:")
            print("=" * 60)
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                for line in recent_lines:
                    print(line.rstrip())
            
        except Exception as e:
            print(f"❌ Error leyendo logs: {e}")
    
    def show_config(self) -> None:
        """Muestra la configuración actual."""
        if not self.load_config():
            return
        
        print("🔧 Configuración del Sistema")
        print("=" * 40)
        
        # Mostrar configuración sin datos sensibles
        config_info = {
            'database': {
                'host': self.config.database.host,
                'port': self.config.database.port,
                'database': self.config.database.database,
                'user': self.config.database.user
            },
            'audiosocket': {
                'host': self.config.audiosocket.host,
                'port': self.config.audiosocket.port,
                'buffer_size': self.config.audiosocket.buffer_size
            },
            'openai': {
                'model': self.config.openai.model,
                'max_tokens': self.config.openai.max_tokens,
                'temperature': self.config.openai.temperature
            },
            'deepgram': {
                'model': self.config.deepgram.model,
                'language': self.config.deepgram.language,
                'sample_rate': self.config.deepgram.sample_rate
            },
            'elevenlabs': {
                'model_id': self.config.elevenlabs.model_id,
                'voice_id': self.config.elevenlabs.voice_id
            }
        }
        
        print(json.dumps(config_info, indent=2, ensure_ascii=False))


def main():
    """Función principal del script."""
    parser = argparse.ArgumentParser(
        description='Gestor del Sistema de Cobranza con IA',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos:
  python start_system.py start          # Inicia el sistema
  python start_system.py test           # Ejecuta pruebas
  python start_system.py status         # Muestra estado
  python start_system.py logs --lines 100  # Muestra 100 líneas de log
        """
    )
    
    parser.add_argument(
        'command',
        choices=['start', 'stop', 'restart', 'status', 'test', 'config', 'logs'],
        help='Comando a ejecutar'
    )
    
    parser.add_argument(
        '--lines',
        type=int,
        default=50,
        help='Número de líneas de log a mostrar (default: 50)'
    )
    
    parser.add_argument(
        '--daemon',
        action='store_true',
        help='Ejecutar en modo daemon (solo para start)'
    )
    
    args = parser.parse_args()
    
    # Crear gestor del sistema
    manager = SystemManager()
    
    # Ejecutar comando
    try:
        if args.command == 'start':
            if args.daemon:
                print("❌ Modo daemon no implementado aún")
                sys.exit(1)
            else:
                success = asyncio.run(manager.start_system())
                sys.exit(0 if success else 1)
        
        elif args.command == 'stop':
            success = asyncio.run(manager.stop_system())
            sys.exit(0 if success else 1)
        
        elif args.command == 'restart':
            success = asyncio.run(manager.restart_system())
            sys.exit(0 if success else 1)
        
        elif args.command == 'status':
            asyncio.run(manager.show_status())
        
        elif args.command == 'test':
            success = asyncio.run(manager.run_tests())
            sys.exit(0 if success else 1)
        
        elif args.command == 'config':
            manager.show_config()
        
        elif args.command == 'logs':
            manager.show_logs(args.lines)
    
    except KeyboardInterrupt:
        print("\n🛑 Operación cancelada por el usuario")
        sys.exit(1)
    
    except Exception as e:
        print(f"❌ Error ejecutando comando: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()