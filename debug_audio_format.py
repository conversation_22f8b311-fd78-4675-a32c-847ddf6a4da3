#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug del Formato de Audio para AudioSocket
===========================================

Script para diagnosticar problemas de formato de audio
entre Google TTS y AudioSocket/Asterisk.

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import asyncio
import struct
import wave
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def debug_audio_format():
    """Diagnostica el formato de audio generado por Google TTS."""
    
    try:
        # Importar configuración
        import sys
        sys.path.append('src')
        from config import Config
        from google_tts_handler import GoogleTTSHandler
        
        # Inicializar
        config = Config()
        tts_handler = GoogleTTSHandler(config.google_tts)
        await tts_handler.initialize()
        
        # Generar audio de prueba
        test_text = "Hola, esta es una prueba de audio."
        logger.info(f"Generando audio para: '{test_text}'")
        
        audio_data = await tts_handler.text_to_speech(test_text)
        logger.info(f"Audio generado: {len(audio_data)} bytes")
        
        # Analizar formato
        logger.info("=== ANÁLISIS DE FORMATO ===")
        logger.info(f"Tamaño total: {len(audio_data)} bytes")
        logger.info(f"Primeros 20 bytes: {audio_data[:20].hex()}")
        logger.info(f"Últimos 20 bytes: {audio_data[-20:].hex()}")
        
        # Verificar si es WAV o PCM crudo
        if audio_data.startswith(b'RIFF'):
            logger.error("❌ PROBLEMA: Audio está en formato WAV, no PCM crudo")
            logger.info("AudioSocket requiere PCM crudo, no WAV")
            
            # Extraer PCM del WAV
            try:
                # Buscar chunk 'data'
                data_pos = audio_data.find(b'data')
                if data_pos != -1:
                    pcm_data = audio_data[data_pos + 8:]
                    logger.info(f"✅ PCM extraído: {len(pcm_data)} bytes")
                    
                    # Guardar ambos formatos para comparación
                    with open('/tmp/debug_audio.wav', 'wb') as f:
                        f.write(audio_data)
                    with open('/tmp/debug_audio.pcm', 'wb') as f:
                        f.write(pcm_data)
                    
                    logger.info("Archivos guardados: /tmp/debug_audio.wav y /tmp/debug_audio.pcm")
                    
                    return pcm_data
                    
            except Exception as e:
                logger.error(f"Error extrayendo PCM: {e}")
                
        else:
            logger.info("✅ Audio parece ser PCM crudo")
            
            # Guardar para análisis
            with open('/tmp/debug_audio.pcm', 'wb') as f:
                f.write(audio_data)
            logger.info("Archivo guardado: /tmp/debug_audio.pcm")
            
            return audio_data
            
    except Exception as e:
        logger.error(f"Error en diagnóstico: {e}")
        return None


async def test_audiosocket_protocol():
    """Prueba el protocolo AudioSocket con audio real."""
    
    logger.info("=== PRUEBA DE PROTOCOLO AUDIOSOCKET ===")
    
    try:
        # Generar audio de prueba
        pcm_data = await debug_audio_format()
        if not pcm_data:
            logger.error("No se pudo generar audio de prueba")
            return
        
        # Conectar a AudioSocket
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection('127.0.0.1', 5001),
                timeout=5.0
            )
            logger.info("✅ Conectado a AudioSocket")
            
            # Enviar UUID falso
            fake_uuid = b'\x01\x23\x45\x67\x89\xab\xcd\xef\x01\x23\x45\x67\x89\xab\xcd\xef'
            uuid_header = struct.pack('>BH', 0x01, 16)
            writer.write(uuid_header + fake_uuid)
            await writer.drain()
            logger.info("✅ UUID enviado")
            
            # Enviar audio en chunks pequeños
            chunk_size = 1024
            total_chunks = (len(pcm_data) + chunk_size - 1) // chunk_size
            
            logger.info(f"Enviando {len(pcm_data)} bytes en {total_chunks} chunks de {chunk_size} bytes")
            
            for i in range(0, len(pcm_data), chunk_size):
                chunk = pcm_data[i:i + chunk_size]
                header = struct.pack('>BH', 0x10, len(chunk))
                
                logger.info(f"Enviando chunk {i//chunk_size + 1}/{total_chunks}: {len(chunk)} bytes")
                
                writer.write(header + chunk)
                await asyncio.wait_for(writer.drain(), timeout=2.0)
                
                # Pausa pequeña
                await asyncio.sleep(0.01)
            
            logger.info("✅ Audio enviado completamente")
            
            # Mantener conexión un momento
            await asyncio.sleep(2.0)
            
            writer.close()
            await writer.wait_closed()
            
        except Exception as e:
            logger.error(f"❌ Error en protocolo AudioSocket: {e}")
            
    except Exception as e:
        logger.error(f"Error en prueba: {e}")


async def analyze_audio_samples():
    """Analiza las muestras de audio para detectar problemas."""
    
    logger.info("=== ANÁLISIS DE MUESTRAS DE AUDIO ===")
    
    try:
        pcm_data = await debug_audio_format()
        if not pcm_data:
            return
        
        # Analizar muestras (asumiendo 16-bit, mono, 8kHz)
        sample_rate = 8000
        bytes_per_sample = 2
        num_samples = len(pcm_data) // bytes_per_sample
        duration = num_samples / sample_rate
        
        logger.info(f"Muestras: {num_samples}")
        logger.info(f"Duración calculada: {duration:.2f} segundos")
        logger.info(f"Sample rate asumido: {sample_rate} Hz")
        
        # Verificar si hay silencio al inicio (padding)
        silence_samples = 0
        for i in range(0, min(len(pcm_data), 4800), 2):  # Primeros 300ms
            sample = struct.unpack('<h', pcm_data[i:i+2])[0]
            if abs(sample) < 100:  # Umbral de silencio
                silence_samples += 1
            else:
                break
        
        silence_duration = (silence_samples * 2) / sample_rate
        logger.info(f"Silencio al inicio: {silence_duration:.3f} segundos")
        
        # Verificar niveles de audio
        max_amplitude = 0
        total_energy = 0
        
        for i in range(0, len(pcm_data) - 1, 2):
            sample = struct.unpack('<h', pcm_data[i:i+2])[0]
            max_amplitude = max(max_amplitude, abs(sample))
            total_energy += sample * sample
        
        rms = (total_energy / num_samples) ** 0.5
        
        logger.info(f"Amplitud máxima: {max_amplitude} / 32767 ({max_amplitude/32767*100:.1f}%)")
        logger.info(f"RMS: {rms:.1f}")
        
        if max_amplitude < 1000:
            logger.warning("⚠️  Audio muy silencioso")
        elif max_amplitude > 30000:
            logger.warning("⚠️  Audio puede estar saturado")
        else:
            logger.info("✅ Niveles de audio normales")
            
    except Exception as e:
        logger.error(f"Error analizando muestras: {e}")


async def main():
    """Función principal de diagnóstico."""
    
    logger.info("🔍 INICIANDO DIAGNÓSTICO COMPLETO DE AUDIO")
    
    # 1. Analizar formato de audio
    await debug_audio_format()
    
    # 2. Analizar muestras
    await analyze_audio_samples()
    
    # 3. Probar protocolo AudioSocket
    await test_audiosocket_protocol()
    
    logger.info("🏁 DIAGNÓSTICO COMPLETADO")
    logger.info("Revisa los archivos en /tmp/ y los logs para identificar el problema")


if __name__ == "__main__":
    asyncio.run(main())
