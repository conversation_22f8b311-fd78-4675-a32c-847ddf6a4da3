#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-
"""
Bot de Cobranza con IA - Sistema de Monitoreo
==============================================

Este script proporciona herramientas de monitoreo y diagnóstico
para el sistema de cobranza automatizada.

Características:
- Monitoreo de estado del sistema
- Verificación de conectividad
- Análisis de rendimiento
- Alertas automáticas
- Reportes de salud

Uso:
    python3 monitor.py [comando] [opciones]

Comandos:
    status      - Estado general del sistema
    health      - Verificación de salud completa
    performance - Métricas de rendimiento
    alerts      - Verificar alertas activas
    report      - Generar reporte completo
    watch       - Monitoreo en tiempo real

Autor: Sistema de Cobranza Automatizada
Fecha: 2024
"""

import os
import sys
import time
import json
import psutil
import argparse
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# Agregar el directorio src al path
sys.path.insert(0, '/opt/cobranza-bot/src')

try:
    from config import Config
    from database import DatabaseManager
except ImportError as e:
    print(f"Error importando módulos: {e}")
    print("Asegúrese de que el sistema esté instalado correctamente")
    sys.exit(1)


@dataclass
class SystemMetrics:
    """Métricas del sistema."""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    load_average: Tuple[float, float, float]
    uptime_hours: float


@dataclass
class ServiceStatus:
    """Estado de un servicio."""
    name: str
    running: bool
    pid: Optional[int]
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    status: str
    uptime: Optional[str]


@dataclass
class DatabaseHealth:
    """Salud de la base de datos."""
    connected: bool
    response_time_ms: float
    active_connections: int
    total_size_mb: float
    tables_count: int
    last_backup: Optional[str]
    errors: List[str]


@dataclass
class APIHealth:
    """Salud de APIs externas."""
    name: str
    available: bool
    response_time_ms: float
    last_check: str
    error_message: Optional[str]


@dataclass
class SystemHealth:
    """Salud general del sistema."""
    timestamp: str
    overall_status: str
    system_metrics: SystemMetrics
    services: List[ServiceStatus]
    database: DatabaseHealth
    apis: List[APIHealth]
    alerts: List[str]
    recommendations: List[str]


class SystemMonitor:
    """Monitor del sistema de cobranza."""
    
    def __init__(self):
        """Inicializar el monitor."""
        self.config = Config()
        self.db_manager = None
        self.app_dir = Path('/opt/cobranza-bot')
        self.log_dir = self.app_dir / 'logs'
        
        # Inicializar conexión a base de datos
        try:
            self.db_manager = DatabaseManager()
        except Exception as e:
            print(f"Warning: No se pudo conectar a la base de datos: {e}")
    
    def get_system_metrics(self) -> SystemMetrics:
        """Obtener métricas del sistema."""
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memoria
        memory = psutil.virtual_memory()
        memory_used_gb = memory.used / (1024**3)
        memory_total_gb = memory.total / (1024**3)
        
        # Disco
        disk = psutil.disk_usage('/')
        disk_used_gb = disk.used / (1024**3)
        disk_total_gb = disk.total / (1024**3)
        
        # Red
        network = psutil.net_io_counters()
        network_sent_mb = network.bytes_sent / (1024**2)
        network_recv_mb = network.bytes_recv / (1024**2)
        
        # Load average
        load_avg = os.getloadavg()
        
        # Uptime
        boot_time = psutil.boot_time()
        uptime_hours = (time.time() - boot_time) / 3600
        
        return SystemMetrics(
            timestamp=datetime.now().isoformat(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_gb=round(memory_used_gb, 2),
            memory_total_gb=round(memory_total_gb, 2),
            disk_percent=disk.percent,
            disk_used_gb=round(disk_used_gb, 2),
            disk_total_gb=round(disk_total_gb, 2),
            network_sent_mb=round(network_sent_mb, 2),
            network_recv_mb=round(network_recv_mb, 2),
            load_average=load_avg,
            uptime_hours=round(uptime_hours, 2)
        )
    
    def get_service_status(self, service_name: str) -> ServiceStatus:
        """Obtener estado de un servicio."""
        try:
            # Verificar si el servicio está corriendo
            result = subprocess.run(
                ['systemctl', 'is-active', service_name],
                capture_output=True,
                text=True
            )
            running = result.returncode == 0
            status = result.stdout.strip()
            
            if running:
                # Obtener PID del servicio
                pid_result = subprocess.run(
                    ['systemctl', 'show', service_name, '--property=MainPID'],
                    capture_output=True,
                    text=True
                )
                pid_line = pid_result.stdout.strip()
                pid = int(pid_line.split('=')[1]) if '=' in pid_line else None
                
                # Obtener métricas del proceso
                if pid and pid > 0:
                    try:
                        process = psutil.Process(pid)
                        cpu_percent = process.cpu_percent()
                        memory_info = process.memory_info()
                        memory_percent = process.memory_percent()
                        memory_mb = memory_info.rss / (1024**2)
                        
                        # Uptime del proceso
                        create_time = process.create_time()
                        uptime_seconds = time.time() - create_time
                        uptime = str(timedelta(seconds=int(uptime_seconds)))
                    except psutil.NoSuchProcess:
                        cpu_percent = 0.0
                        memory_percent = 0.0
                        memory_mb = 0.0
                        uptime = None
                else:
                    cpu_percent = 0.0
                    memory_percent = 0.0
                    memory_mb = 0.0
                    uptime = None
            else:
                pid = None
                cpu_percent = 0.0
                memory_percent = 0.0
                memory_mb = 0.0
                uptime = None
            
            return ServiceStatus(
                name=service_name,
                running=running,
                pid=pid,
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_mb=round(memory_mb, 2),
                status=status,
                uptime=uptime
            )
            
        except Exception as e:
            return ServiceStatus(
                name=service_name,
                running=False,
                pid=None,
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_mb=0.0,
                status=f"Error: {e}",
                uptime=None
            )
    
    def get_database_health(self) -> DatabaseHealth:
        """Verificar salud de la base de datos."""
        errors = []
        
        if not self.db_manager:
            return DatabaseHealth(
                connected=False,
                response_time_ms=0.0,
                active_connections=0,
                total_size_mb=0.0,
                tables_count=0,
                last_backup=None,
                errors=["No se pudo conectar al gestor de base de datos"]
            )
        
        try:
            # Verificar conexión y tiempo de respuesta
            start_time = time.time()
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
            response_time_ms = (time.time() - start_time) * 1000
            connected = True
            
            # Obtener estadísticas de la base de datos
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Conexiones activas
                    cursor.execute(
                        "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
                    )
                    active_connections = cursor.fetchone()[0]
                    
                    # Tamaño de la base de datos
                    cursor.execute(
                        "SELECT pg_size_pretty(pg_database_size(current_database()))"
                    )
                    size_str = cursor.fetchone()[0]
                    # Convertir a MB (aproximado)
                    if 'MB' in size_str:
                        total_size_mb = float(size_str.replace(' MB', ''))
                    elif 'GB' in size_str:
                        total_size_mb = float(size_str.replace(' GB', '')) * 1024
                    elif 'KB' in size_str:
                        total_size_mb = float(size_str.replace(' KB', '')) / 1024
                    else:
                        total_size_mb = 0.0
                    
                    # Número de tablas
                    cursor.execute(
                        "SELECT count(*) FROM information_schema.tables "
                        "WHERE table_schema = 'public'"
                    )
                    tables_count = cursor.fetchone()[0]
            
            # Verificar último backup (si existe)
            last_backup = None
            backup_dir = self.app_dir / 'backups'
            if backup_dir.exists():
                backup_files = list(backup_dir.glob('*.sql'))
                if backup_files:
                    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
                    last_backup = datetime.fromtimestamp(
                        latest_backup.stat().st_mtime
                    ).isoformat()
            
        except Exception as e:
            connected = False
            response_time_ms = 0.0
            active_connections = 0
            total_size_mb = 0.0
            tables_count = 0
            last_backup = None
            errors.append(f"Error de base de datos: {e}")
        
        return DatabaseHealth(
            connected=connected,
            response_time_ms=round(response_time_ms, 2),
            active_connections=active_connections,
            total_size_mb=round(total_size_mb, 2),
            tables_count=tables_count,
            last_backup=last_backup,
            errors=errors
        )
    
    def check_api_health(self, api_name: str, test_func) -> APIHealth:
        """Verificar salud de una API externa."""
        start_time = time.time()
        
        try:
            test_func()
            available = True
            error_message = None
        except Exception as e:
            available = False
            error_message = str(e)
        
        response_time_ms = (time.time() - start_time) * 1000
        
        return APIHealth(
            name=api_name,
            available=available,
            response_time_ms=round(response_time_ms, 2),
            last_check=datetime.now().isoformat(),
            error_message=error_message
        )
    
    def test_openai_api(self):
        """Probar conectividad con OpenAI."""
        import openai
        openai.api_key = self.config.openai.api_key
        # Hacer una llamada simple
        openai.Model.list()
    
    def test_deepgram_api(self):
        """Probar conectividad con Deepgram."""
        from deepgram import Deepgram
        dg_client = Deepgram(self.config.deepgram.api_key)
        # Verificar que la API key sea válida
        # Nota: Deepgram no tiene un endpoint simple de verificación
        # por lo que solo verificamos que la key esté configurada
        if not self.config.deepgram.api_key:
            raise Exception("API key no configurada")
    
    def test_elevenlabs_api(self):
        """Probar conectividad con ElevenLabs."""
        import requests
        headers = {
            'xi-api-key': self.config.elevenlabs.api_key
        }
        response = requests.get(
            'https://api.elevenlabs.io/v1/voices',
            headers=headers,
            timeout=10
        )
        response.raise_for_status()
    
    def get_system_health(self) -> SystemHealth:
        """Obtener salud completa del sistema."""
        # Métricas del sistema
        system_metrics = self.get_system_metrics()
        
        # Estado de servicios
        services = [
            self.get_service_status('cobranza-bot'),
            self.get_service_status('postgresql'),
            self.get_service_status('nginx')
        ]
        
        # Salud de la base de datos
        database = self.get_database_health()
        
        # Salud de APIs
        apis = [
            self.check_api_health('OpenAI', self.test_openai_api),
            self.check_api_health('Deepgram', self.test_deepgram_api),
            self.check_api_health('ElevenLabs', self.test_elevenlabs_api)
        ]
        
        # Generar alertas
        alerts = []
        recommendations = []
        
        # Verificar métricas del sistema
        if system_metrics.cpu_percent > 80:
            alerts.append(f"Alto uso de CPU: {system_metrics.cpu_percent}%")
            recommendations.append("Considere optimizar procesos o aumentar recursos de CPU")
        
        if system_metrics.memory_percent > 85:
            alerts.append(f"Alto uso de memoria: {system_metrics.memory_percent}%")
            recommendations.append("Considere aumentar la memoria RAM del sistema")
        
        if system_metrics.disk_percent > 90:
            alerts.append(f"Poco espacio en disco: {system_metrics.disk_percent}%")
            recommendations.append("Libere espacio en disco o aumente el almacenamiento")
        
        # Verificar servicios
        for service in services:
            if not service.running:
                alerts.append(f"Servicio {service.name} no está corriendo")
                recommendations.append(f"Reinicie el servicio {service.name}")
        
        # Verificar base de datos
        if not database.connected:
            alerts.append("Base de datos no disponible")
            recommendations.append("Verifique la configuración y estado de PostgreSQL")
        elif database.response_time_ms > 1000:
            alerts.append(f"Base de datos lenta: {database.response_time_ms}ms")
            recommendations.append("Optimice consultas o aumente recursos de base de datos")
        
        # Verificar APIs
        for api in apis:
            if not api.available:
                alerts.append(f"API {api.name} no disponible: {api.error_message}")
                recommendations.append(f"Verifique la configuración de {api.name}")
        
        # Determinar estado general
        if alerts:
            overall_status = "WARNING" if len(alerts) <= 2 else "CRITICAL"
        else:
            overall_status = "HEALTHY"
        
        return SystemHealth(
            timestamp=datetime.now().isoformat(),
            overall_status=overall_status,
            system_metrics=system_metrics,
            services=services,
            database=database,
            apis=apis,
            alerts=alerts,
            recommendations=recommendations
        )
    
    def print_status(self):
        """Imprimir estado del sistema."""
        health = self.get_system_health()
        
        # Colores para output
        colors = {
            'HEALTHY': '\033[92m',
            'WARNING': '\033[93m',
            'CRITICAL': '\033[91m',
            'RESET': '\033[0m'
        }
        
        color = colors.get(health.overall_status, colors['RESET'])
        
        print(f"\n{color}=== ESTADO DEL SISTEMA ==={colors['RESET']}")
        print(f"Estado General: {color}{health.overall_status}{colors['RESET']}")
        print(f"Timestamp: {health.timestamp}")
        
        print("\n--- MÉTRICAS DEL SISTEMA ---")
        metrics = health.system_metrics
        print(f"CPU: {metrics.cpu_percent}%")
        print(f"Memoria: {metrics.memory_percent}% ({metrics.memory_used_gb}GB/{metrics.memory_total_gb}GB)")
        print(f"Disco: {metrics.disk_percent}% ({metrics.disk_used_gb}GB/{metrics.disk_total_gb}GB)")
        print(f"Load Average: {metrics.load_average}")
        print(f"Uptime: {metrics.uptime_hours} horas")
        
        print("\n--- SERVICIOS ---")
        for service in health.services:
            status_color = '\033[92m' if service.running else '\033[91m'
            print(f"{service.name}: {status_color}{service.status}{colors['RESET']}")
            if service.running and service.pid:
                print(f"  PID: {service.pid}, CPU: {service.cpu_percent}%, RAM: {service.memory_mb}MB")
        
        print("\n--- BASE DE DATOS ---")
        db_color = '\033[92m' if health.database.connected else '\033[91m'
        print(f"Estado: {db_color}{'Conectada' if health.database.connected else 'Desconectada'}{colors['RESET']}")
        if health.database.connected:
            print(f"Tiempo de respuesta: {health.database.response_time_ms}ms")
            print(f"Conexiones activas: {health.database.active_connections}")
            print(f"Tamaño: {health.database.total_size_mb}MB")
            print(f"Tablas: {health.database.tables_count}")
        
        print("\n--- APIs EXTERNAS ---")
        for api in health.apis:
            api_color = '\033[92m' if api.available else '\033[91m'
            print(f"{api.name}: {api_color}{'Disponible' if api.available else 'No disponible'}{colors['RESET']}")
            if api.available:
                print(f"  Tiempo de respuesta: {api.response_time_ms}ms")
            elif api.error_message:
                print(f"  Error: {api.error_message}")
        
        if health.alerts:
            print(f"\n{colors['WARNING']}--- ALERTAS ---{colors['RESET']}")
            for alert in health.alerts:
                print(f"⚠️  {alert}")
        
        if health.recommendations:
            print(f"\n{colors['RESET']}--- RECOMENDACIONES ---{colors['RESET']}")
            for rec in health.recommendations:
                print(f"💡 {rec}")
        
        print()
    
    def generate_report(self, output_file: Optional[str] = None):
        """Generar reporte completo."""
        health = self.get_system_health()
        report = asdict(health)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"Reporte guardado en: {output_file}")
        else:
            print(json.dumps(report, indent=2, ensure_ascii=False))
    
    def watch_system(self, interval: int = 30):
        """Monitoreo en tiempo real."""
        print(f"Iniciando monitoreo en tiempo real (intervalo: {interval}s)")
        print("Presione Ctrl+C para detener")
        
        try:
            while True:
                os.system('clear')  # Limpiar pantalla
                self.print_status()
                print(f"\nPróxima actualización en {interval} segundos...")
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\nMonitoreo detenido.")


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(
        description='Monitor del Sistema de Cobranza con IA'
    )
    
    parser.add_argument(
        'command',
        choices=['status', 'health', 'performance', 'alerts', 'report', 'watch'],
        help='Comando a ejecutar'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='Archivo de salida para reportes'
    )
    
    parser.add_argument(
        '--interval', '-i',
        type=int,
        default=30,
        help='Intervalo en segundos para el comando watch (default: 30)'
    )
    
    args = parser.parse_args()
    
    monitor = SystemMonitor()
    
    if args.command == 'status':
        monitor.print_status()
    
    elif args.command == 'health':
        monitor.print_status()
    
    elif args.command == 'performance':
        metrics = monitor.get_system_metrics()
        print(json.dumps(asdict(metrics), indent=2))
    
    elif args.command == 'alerts':
        health = monitor.get_system_health()
        if health.alerts:
            print("ALERTAS ACTIVAS:")
            for alert in health.alerts:
                print(f"⚠️  {alert}")
        else:
            print("✅ No hay alertas activas")
    
    elif args.command == 'report':
        monitor.generate_report(args.output)
    
    elif args.command == 'watch':
        monitor.watch_system(args.interval)


if __name__ == '__main__':
    main()