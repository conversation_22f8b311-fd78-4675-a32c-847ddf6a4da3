#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar las optimizaciones de ASR y interrupción de audio.
"""

import sys
import os
import asyncio
import time
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.asr_handler import ASRHandler
from src.call_session import CallSession, CallSessionManager
from config import Config


async def test_asr_initialization_speed():
    """Prueba la velocidad de inicialización del ASR."""
    print("🚀 PRUEBA DE VELOCIDAD DE INICIALIZACIÓN ASR")
    print("=" * 60)
    
    config = Config()
    
    # Prueba 1: Inicialización tradicional
    start_time = time.time()
    asr_handler = ASRHandler(config.deepgram)
    await asr_handler.initialize()
    init_time = time.time() - start_time
    print(f"✅ Inicialización ASR: {init_time:.3f}s")
    
    # Prueba 2: Pre-inicialización de streaming
    start_time = time.time()
    await asr_handler.start_streaming()
    streaming_time = time.time() - start_time
    print(f"✅ Inicio de streaming: {streaming_time:.3f}s")
    
    total_time = init_time + streaming_time
    print(f"📊 Tiempo total: {total_time:.3f}s")
    
    # Cleanup
    await asr_handler.cleanup()
    
    return total_time


async def test_session_initialization_speed():
    """Prueba la velocidad de inicialización de sesión completa."""
    print("\n🚀 PRUEBA DE VELOCIDAD DE INICIALIZACIÓN DE SESIÓN")
    print("=" * 60)
    
    config = Config()
    
    # Mock database manager
    class MockDBManager:
        async def log_call(self, *args, **kwargs):
            pass
    
    db_manager = MockDBManager()
    
    # Crear session manager
    import logging
    logger = logging.getLogger(__name__)
    session_manager = CallSessionManager(config, db_manager, logger)
    
    # Medir tiempo de creación de sesión
    start_time = time.time()
    session = await session_manager.create_session("test-session", "1234567890")
    session_time = time.time() - start_time
    
    print(f"✅ Creación de sesión completa: {session_time:.3f}s")
    print(f"   - ASR inicializado: {session.asr_handler.is_initialized}")
    print(f"   - ASR escuchando: {session.asr_handler.is_listening}")
    print(f"   - TTS inicializado: {session.tts_handler is not None}")
    print(f"   - AI inicializado: {session.ai_engine is not None}")
    
    # Cleanup
    await session_manager.cleanup_session("test-session")
    
    return session_time


async def test_audio_interruption_logic():
    """Prueba la lógica de interrupción de audio."""
    print("\n🚀 PRUEBA DE LÓGICA DE INTERRUPCIÓN DE AUDIO")
    print("=" * 60)
    
    # Crear sesión mock
    session = CallSession(
        session_id="test-interruption",
        caller_number="1234567890",
        start_time=time.time()
    )
    
    # Simular estados
    test_cases = [
        {
            "name": "Bot no hablando",
            "audio_playing": False,
            "audio_interrupted": False,
            "expected_interrupted": False
        },
        {
            "name": "Bot hablando, primera interrupción",
            "audio_playing": True,
            "audio_interrupted": False,
            "expected_interrupted": True
        },
        {
            "name": "Bot hablando, ya interrumpido",
            "audio_playing": True,
            "audio_interrupted": True,
            "expected_interrupted": True
        },
        {
            "name": "Bot terminó de hablar",
            "audio_playing": False,
            "audio_interrupted": True,
            "expected_interrupted": False  # Se debería resetear
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Caso {i}: {test_case['name']}")
        
        # Configurar estado inicial
        session.audio_playing = test_case['audio_playing']
        session.audio_interrupted = test_case['audio_interrupted']
        
        print(f"   Estado inicial: playing={session.audio_playing}, interrupted={session.audio_interrupted}")
        
        # Simular lógica de interrupción
        if session.audio_playing and not session.audio_interrupted:
            session.audio_interrupted = True
            print(f"   🔥 INTERRUPCIÓN DETECTADA")
        elif not session.audio_playing and session.audio_interrupted:
            session.audio_interrupted = False
            print(f"   ✅ INTERRUPCIÓN RESETEADA")
        
        result = "✅ CORRECTO" if session.audio_interrupted == test_case['expected_interrupted'] else "❌ ERROR"
        print(f"   Estado final: playing={session.audio_playing}, interrupted={session.audio_interrupted}")
        print(f"   Resultado: {result}")
    
    return True


async def test_deepgram_config_optimization():
    """Prueba las optimizaciones de configuración de Deepgram."""
    print("\n🚀 PRUEBA DE CONFIGURACIÓN OPTIMIZADA DE DEEPGRAM")
    print("=" * 60)
    
    config = Config()
    asr_handler = ASRHandler(config.deepgram)
    await asr_handler.initialize()
    
    # Verificar configuraciones optimizadas
    options = asr_handler.live_options
    
    optimizations = [
        ("Modelo rápido (nova-2)", getattr(options, 'model', None) == 'nova-2'),
        ("Sin puntuación", getattr(options, 'punctuate', True) == False),
        ("Sin filtro de profanidad", getattr(options, 'profanity_filter', True) == False),
        ("Sin redacción", getattr(options, 'redact', True) == False),
        ("Sin formato inteligente", getattr(options, 'smart_format', True) == False),
        ("Sin diarización", getattr(options, 'diarize', True) == False),
        ("Sin palabras de relleno", getattr(options, 'filler_words', True) == False),
        ("Resultados interinos", getattr(options, 'interim_results', False) == True),
        ("Eventos VAD", getattr(options, 'vad_events', False) == True),
    ]
    
    for name, is_optimized in optimizations:
        status = "✅ ACTIVADO" if is_optimized else "❌ NO ACTIVADO"
        print(f"   {name}: {status}")
    
    # Cleanup
    await asr_handler.cleanup()
    
    return all(opt[1] for opt in optimizations)


async def run_all_tests():
    """Ejecuta todas las pruebas de optimización."""
    print("🚀 INICIANDO PRUEBAS DE OPTIMIZACIÓN")
    print("=" * 80)
    
    results = {}
    
    try:
        # Prueba 1: Velocidad de ASR
        results['asr_speed'] = await test_asr_initialization_speed()
        
        # Prueba 2: Velocidad de sesión
        results['session_speed'] = await test_session_initialization_speed()
        
        # Prueba 3: Lógica de interrupción
        results['interruption_logic'] = await test_audio_interruption_logic()
        
        # Prueba 4: Configuración de Deepgram
        results['deepgram_config'] = await test_deepgram_config_optimization()
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Resumen de resultados
    print("\n" + "=" * 80)
    print("📊 RESUMEN DE RESULTADOS")
    print("=" * 80)
    
    print(f"🚀 Velocidad de inicialización ASR: {results.get('asr_speed', 0):.3f}s")
    print(f"🚀 Velocidad de inicialización de sesión: {results.get('session_speed', 0):.3f}s")
    print(f"🔥 Lógica de interrupción: {'✅ CORRECTA' if results.get('interruption_logic') else '❌ ERROR'}")
    print(f"⚡ Configuración Deepgram: {'✅ OPTIMIZADA' if results.get('deepgram_config') else '❌ NO OPTIMIZADA'}")
    
    # Evaluación general
    total_init_time = results.get('asr_speed', 0) + results.get('session_speed', 0)
    
    if total_init_time < 2.0:
        print(f"\n🎉 EXCELENTE: Inicialización total < 2s ({total_init_time:.3f}s)")
    elif total_init_time < 5.0:
        print(f"\n✅ BUENO: Inicialización total < 5s ({total_init_time:.3f}s)")
    else:
        print(f"\n⚠️ MEJORABLE: Inicialización total > 5s ({total_init_time:.3f}s)")
    
    return True


if __name__ == "__main__":
    # Configurar logging básico
    import logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
    
    # Ejecutar pruebas
    asyncio.run(run_all_tests())
