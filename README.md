# Bot de Cobranza con IA 🤖💰

Sistema automatizado de cobranza telefónica utilizando Inteligencia Artificial para gestionar llamadas de recuperación de cartera de manera eficiente y profesional.

## 📋 Descripción

Este sistema integra múltiples tecnologías de IA para automatizar el proceso de cobranza:

- **ASR (Automatic Speech Recognition)**: Deepgram para transcripción en tiempo real
- **LLM (Large Language Model)**: OpenAI GPT para conversaciones inteligentes
- **TTS (Text-to-Speech)**: ElevenLabs para síntesis de voz natural
- **AudioSocket**: Integración con Asterisk para manejo de llamadas
- **Base de Datos**: PostgreSQL para gestión de datos

## 🏗️ Arquitectura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Asterisk     │◄──►│  AudioSocket    │◄──►│   Cobranza Bot  │
│   (Telefonía)   │    │    Server       │    │   (IA System)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                        ┌─────────────────┐            │
                        │   PostgreSQL    │◄───────────┘
                        │  (Base de Datos)│
                        └─────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │           │           │
            ┌───────▼────┐ ┌────▼────┐ ┌────▼────┐
            │  Deepgram  │ │ OpenAI  │ │ElevenLabs│
            │   (ASR)    │ │ (LLM)   │ │  (TTS)  │
            └────────────┘ └─────────┘ └─────────┘
```

## 🚀 Características

### Funcionalidades Principales
- ✅ Transcripción de voz en tiempo real
- ✅ Conversaciones inteligentes con IA
- ✅ Síntesis de voz natural
- ✅ Gestión automática de llamadas
- ✅ Base de datos completa de clientes y deudas
- ✅ Registro detallado de conversaciones
- ✅ Sistema de compromisos de pago
- ✅ Reportes y estadísticas

### Características Técnicas
- 🔧 Arquitectura modular y escalable
- 🔧 Configuración mediante variables de entorno
- 🔧 Logging completo y estructurado
- 🔧 Manejo robusto de errores
- 🔧 Monitoreo y alertas
- 🔧 Backups automáticos
- 🔧 Servicio systemd

## 📦 Instalación

### Requisitos del Sistema

- **Sistema Operativo**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Python**: 3.9+
- **PostgreSQL**: 13+
- **Memoria RAM**: 4GB mínimo, 8GB recomendado
- **Espacio en Disco**: 20GB mínimo
- **CPU**: 2 cores mínimo, 4 cores recomendado

### Instalación Automática

```bash
# Clonar el repositorio
git clone <repository-url>
cd cobranza-bot

# Ejecutar instalador (requiere sudo)
sudo ./install.sh
```

### Instalación Manual

#### 1. Preparar el Sistema

```bash
# Actualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar dependencias
sudo apt install -y python3 python3-pip python3-venv python3-dev \
    postgresql postgresql-contrib build-essential libpq-dev \
    libffi-dev libssl-dev curl wget git
```

#### 2. Crear Usuario del Sistema

```bash
sudo useradd -r -s /bin/bash -d /opt/cobranza-bot -m cobranza
```

#### 3. Configurar Directorios

```bash
sudo mkdir -p /opt/cobranza-bot/{src,logs,backups,sql,systemd}
sudo chown -R cobranza:cobranza /opt/cobranza-bot
```

#### 4. Configurar Python

```bash
# Cambiar al usuario cobranza
sudo -u cobranza bash
cd /opt/cobranza-bot

# Crear entorno virtual
python3 -m venv venv
source venv/bin/activate

# Instalar dependencias
pip install --upgrade pip
pip install -r requirements.txt
```

#### 5. Configurar PostgreSQL

```bash
# Crear usuario y base de datos
sudo -u postgres psql << EOF
CREATE USER cobranza_user WITH PASSWORD 'cobranza_pass';
CREATE DATABASE cobranza_db OWNER cobranza_user;
GRANT ALL PRIVILEGES ON DATABASE cobranza_db TO cobranza_user;
\q
EOF

# Aplicar esquema
sudo -u postgres psql -d cobranza_db -f /opt/cobranza-bot/sql/schema.sql
```

#### 6. Configurar Variables de Entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar configuración
nano .env
```

#### 7. Configurar Servicio

```bash
# Copiar archivo de servicio
sudo cp systemd/cobranza-bot.service /etc/systemd/system/

# Habilitar servicio
sudo systemctl daemon-reload
sudo systemctl enable cobranza-bot
```

## ⚙️ Configuración

### Variables de Entorno

Edite el archivo `/opt/cobranza-bot/.env`:

```bash
# Base de Datos
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cobranza_db
DB_USER=cobranza_user
DB_PASSWORD=cobranza_pass

# OpenAI
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# Deepgram
DEEPGRAM_API_KEY=your-deepgram-api-key
DEEPGRAM_MODEL=nova-2
DEEPGRAM_LANGUAGE=es
DEEPGRAM_ENCODING=linear16
DEEPGRAM_SAMPLE_RATE=8000

# ElevenLabs
ELEVENLABS_API_KEY=your-elevenlabs-api-key
ELEVENLABS_VOICE_ID=default-voice-id
ELEVENLABS_MODEL=eleven_multilingual_v2
ELEVENLABS_STABILITY=0.5
ELEVENLABS_SIMILARITY_BOOST=0.8

# AudioSocket
AUDIOSOCKET_HOST=0.0.0.0
AUDIOSOCKET_PORT=8080
AUDIOSOCKET_BUFFER_SIZE=1024

# Sistema
LOG_LEVEL=INFO
LOG_FILE=/opt/cobranza-bot/logs/cobranza.log
MAX_CONCURRENT_CALLS=10
CALL_TIMEOUT=300
```

### APIs Requeridas

#### OpenAI
1. Crear cuenta en [OpenAI](https://platform.openai.com/)
2. Generar API key
3. Configurar límites de uso

#### Deepgram
1. Crear cuenta en [Deepgram](https://deepgram.com/)
2. Obtener API key
3. Configurar créditos

#### ElevenLabs
1. Crear cuenta en [ElevenLabs](https://elevenlabs.io/)
2. Obtener API key
3. Seleccionar voces

## 🎯 Uso

### Gestión del Servicio

```bash
# Iniciar servicio
sudo systemctl start cobranza-bot

# Detener servicio
sudo systemctl stop cobranza-bot

# Reiniciar servicio
sudo systemctl restart cobranza-bot

# Ver estado
sudo systemctl status cobranza-bot

# Ver logs
sudo journalctl -u cobranza-bot -f
```

### Gestión Manual

```bash
# Cambiar al usuario cobranza
sudo -u cobranza bash
cd /opt/cobranza-bot

# Activar entorno virtual
source venv/bin/activate

# Comandos disponibles
python3 start_system.py start    # Iniciar sistema
python3 start_system.py stop     # Detener sistema
python3 start_system.py status   # Ver estado
python3 start_system.py test     # Ejecutar pruebas
python3 start_system.py config   # Ver configuración
python3 start_system.py logs     # Ver logs
```

### Monitoreo

```bash
# Estado general
python3 monitor.py status

# Verificación de salud
python3 monitor.py health

# Métricas de rendimiento
python3 monitor.py performance

# Monitoreo en tiempo real
python3 monitor.py watch

# Generar reporte
python3 monitor.py report --output report.json
```

### Backups

```bash
# Crear backup completo
python3 backup.py create

# Backup solo de base de datos
python3 backup.py database

# Listar backups
python3 backup.py list

# Verificar backup
python3 backup.py verify --file backup_file.tar.gz

# Restaurar backup
python3 backup.py restore --file backup_file.tar.gz

# Configurar backups automáticos
python3 backup.py schedule

# Limpiar backups antiguos
python3 backup.py cleanup
```

## 📊 Base de Datos

### Esquema Principal

- **clients**: Información de clientes
- **debts**: Deudas y obligaciones
- **calls**: Registro de llamadas
- **conversations**: Transcripciones de conversaciones
- **commitments**: Compromisos de pago
- **payments**: Registro de pagos
- **call_statistics**: Estadísticas de llamadas
- **system_logs**: Logs del sistema

### Consultas Útiles

```sql
-- Clientes con deudas pendientes
SELECT c.name, c.phone, d.amount, d.due_date 
FROM clients c 
JOIN debts d ON c.id = d.client_id 
WHERE d.status = 'pending';

-- Estadísticas de llamadas del día
SELECT 
    COUNT(*) as total_calls,
    AVG(duration) as avg_duration,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_calls
FROM calls 
WHERE DATE(created_at) = CURRENT_DATE;

-- Compromisos próximos a vencer
SELECT c.name, c.phone, cm.amount, cm.due_date
FROM clients c
JOIN commitments cm ON c.id = cm.client_id
WHERE cm.due_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '7 days'
AND cm.status = 'active';
```

## 🔧 Desarrollo

### Estructura del Proyecto

```
cobranza-bot/
├── src/                      # Código fuente
│   ├── main.py              # Módulo principal
│   ├── config.py            # Configuración
│   ├── database.py          # Gestor de base de datos
│   ├── audiosocket_server.py # Servidor AudioSocket
│   ├── asr_handler.py       # Manejador ASR (Deepgram)
│   ├── tts_handler.py       # Manejador TTS (ElevenLabs)
│   ├── ai_conversation.py   # Motor de conversación IA
│   └── utils.py             # Utilidades
├── sql/                     # Scripts SQL
│   └── schema.sql           # Esquema de base de datos
├── systemd/                 # Archivos systemd
│   └── cobranza-bot.service # Servicio systemd
├── logs/                    # Logs del sistema
├── backups/                 # Backups automáticos
├── requirements.txt         # Dependencias Python
├── .env.example            # Ejemplo de configuración
├── start_system.py         # Script de gestión
├── monitor.py              # Sistema de monitoreo
├── backup.py               # Sistema de backup
├── install.sh              # Instalador automático
└── README.md               # Documentación
```

### Agregar Nuevas Funcionalidades

1. **Crear nuevo módulo** en `src/`
2. **Agregar configuración** en `config.py`
3. **Actualizar base de datos** en `sql/schema.sql`
4. **Integrar en main.py**
5. **Agregar pruebas**
6. **Actualizar documentación**

### Pruebas

```bash
# Ejecutar todas las pruebas
python3 start_system.py test

# Probar componente específico
python3 -c "from src.database import DatabaseManager; dm = DatabaseManager(); print('DB OK')"
```

## 📈 Monitoreo y Alertas

### Métricas Monitoreadas

- **Sistema**: CPU, memoria, disco, red
- **Servicios**: Estado, uptime, recursos
- **Base de datos**: Conexiones, rendimiento, tamaño
- **APIs**: Disponibilidad, latencia
- **Llamadas**: Volumen, duración, éxito

### Alertas Automáticas

- Alto uso de recursos (CPU > 80%, RAM > 85%)
- Servicios caídos
- Errores de conectividad
- Base de datos lenta
- APIs no disponibles

## 🔒 Seguridad

### Medidas Implementadas

- **Cifrado**: Comunicaciones HTTPS/TLS
- **Autenticación**: API keys seguras
- **Autorización**: Permisos de usuario limitados
- **Logs**: Auditoría completa
- **Backups**: Cifrados y verificados
- **Firewall**: Puertos específicos

### Recomendaciones

1. **Cambiar contraseñas** por defecto
2. **Configurar firewall** apropiadamente
3. **Actualizar sistema** regularmente
4. **Monitorear logs** de seguridad
5. **Realizar backups** frecuentes
6. **Limitar acceso** SSH

## 🚨 Solución de Problemas

### Problemas Comunes

#### El servicio no inicia
```bash
# Verificar logs
sudo journalctl -u cobranza-bot -n 50

# Verificar configuración
python3 start_system.py config

# Probar manualmente
sudo -u cobranza /opt/cobranza-bot/venv/bin/python3 /opt/cobranza-bot/start_system.py test
```

#### Error de conexión a base de datos
```bash
# Verificar PostgreSQL
sudo systemctl status postgresql

# Probar conexión
psql -h localhost -U cobranza_user -d cobranza_db

# Verificar configuración
grep DB_ /opt/cobranza-bot/.env
```

#### APIs no responden
```bash
# Verificar conectividad
curl -I https://api.openai.com/v1/models
curl -I https://api.deepgram.com/v1/listen
curl -I https://api.elevenlabs.io/v1/voices

# Verificar API keys
grep API_KEY /opt/cobranza-bot/.env
```

#### Alto uso de recursos
```bash
# Monitorear sistema
python3 monitor.py performance

# Ver procesos
top -u cobranza

# Verificar logs
tail -f /opt/cobranza-bot/logs/cobranza.log
```

### Logs Importantes

- **Sistema**: `/opt/cobranza-bot/logs/cobranza.log`
- **Servicio**: `sudo journalctl -u cobranza-bot`
- **PostgreSQL**: `/var/log/postgresql/`
- **Sistema**: `/var/log/syslog`

## 📞 Integración con Asterisk

### Configuración AudioSocket

En `/etc/asterisk/extensions.conf`:

```
[cobranza]
exten => _X.,1,Answer()
exten => _X.,n,AudioSocket(40325ec2-5efd-4bd3-805f-53576e581d13,localhost:8080)
exten => _X.,n,Hangup()
```

### Dialplan Ejemplo

```
[outbound-cobranza]
exten => _X.,1,Set(CALLERID(name)=Cobranza Bot)
exten => _X.,n,Set(CALLERID(num)=**********)
exten => _X.,n,Dial(SIP/provider/${EXTEN})
exten => _X.,n,Goto(cobranza,${EXTEN},1)
```

## 📋 Mantenimiento

### Tareas Diarias
- ✅ Verificar estado del servicio
- ✅ Revisar logs de errores
- ✅ Monitorear uso de recursos

### Tareas Semanales
- ✅ Verificar backups
- ✅ Limpiar logs antiguos
- ✅ Revisar estadísticas

### Tareas Mensuales
- ✅ Actualizar dependencias
- ✅ Revisar configuración
- ✅ Optimizar base de datos
- ✅ Auditoría de seguridad

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver archivo `LICENSE` para más detalles.

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama de feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📞 Soporte

Para soporte técnico:

- **Email**: <EMAIL>
- **Documentación**: [Wiki del proyecto]
- **Issues**: [GitHub Issues]

## 📚 Referencias

- [Deepgram API Documentation](https://developers.deepgram.com/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)
- [ElevenLabs API Documentation](https://docs.elevenlabs.io/)
- [Asterisk AudioSocket](https://wiki.asterisk.org/wiki/display/AST/AudioSocket)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---

**Bot de Cobranza con IA** - Automatizando la recuperación de cartera con tecnología de vanguardia 🚀