#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Script de prueba REAL para el sistema de despedida y corte de llamada.
Simula una conversación completa con el sistema AudioSocket.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import Config
from src.ai_conversation import AIConversationEngine
from src.database import DatabaseManager
from src.call_manager import CallManager, CallEndReason


class MockAudioProcessor:
    """Mock del audio processor para simular el flujo real."""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
    
    async def _check_call_termination(self, session, ai_response):
        """Simula la verificación de terminación de llamada."""
        try:
            # Obtener contexto de conversación
            context = None
            if hasattr(session, 'ai_engine') and session.ai_engine:
                context = session.ai_engine.get_conversation_context(session.session_id)
            
            if not context:
                return
            
            # Verificar si debe finalizar por compromiso exitoso
            if getattr(context, 'should_end_call', False) and getattr(context, 'commitment_data', None):
                self.logger.info(f"🎉 FINALIZANDO LLAMADA POR COMPROMISO EXITOSO (Sesión {session.session_id})")
                
                # Importar CallManager
                try:
                    from call_manager import CallManager
                except ImportError:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(__file__))
                    from call_manager import CallManager
                
                call_manager = CallManager(self.config, session.ai_engine.db_manager, self.logger)
                
                # Agregar información del cliente al contexto de sesión
                session.client_name = getattr(context, 'nombre_cliente', 'cliente')
                
                # Manejar compromiso exitoso y corte de llamada
                await call_manager.handle_commitment_success(session, context.commitment_data)
                return True
            
            # Verificar si debe finalizar por despedida en la respuesta
            if ai_response and hasattr(ai_response, 'text'):
                try:
                    from call_manager import CallManager, CallEndReason
                except ImportError:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(__file__))
                    from call_manager import CallManager, CallEndReason
                
                call_manager = CallManager(self.config, session.ai_engine.db_manager, self.logger)
                
                if call_manager.should_end_conversation(ai_response.text):
                    self.logger.info(f"👋 FINALIZANDO LLAMADA POR DESPEDIDA (Sesión {session.session_id})")
                    
                    # Agregar información del cliente
                    session.client_name = getattr(context, 'nombre_cliente', 'cliente')
                    
                    # Manejar final de conversación
                    await call_manager.handle_conversation_end(session, CallEndReason.DESPEDIDA_NORMAL)
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error verificando terminación de llamada: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False


class MockSession:
    """Mock de sesión que simula una sesión real."""
    
    def __init__(self, session_id: str, caller_number: str, ai_engine):
        self.session_id = session_id
        self.caller_number = caller_number
        self.client_name = "Juan Pérez García"
        self.start_time = datetime.now()
        self.ai_engine = ai_engine


async def simulate_conversation_with_commitment():
    """Simula una conversación completa que termina en compromiso."""
    print("🎭 SIMULANDO CONVERSACIÓN CON COMPROMISO")
    print("=" * 60)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    try:
        # Inicializar componentes
        config = Config()
        db_manager = DatabaseManager()
        ai_engine = AIConversationEngine(config.openai, db_manager)
        await ai_engine.initialize()
        
        audio_processor = MockAudioProcessor(config, logger)
        
        # Crear sesión mock
        session_id = "test-real-session-001"
        caller_number = "987654321"
        session = MockSession(session_id, caller_number, ai_engine)
        
        print(f"📞 Iniciando conversación: {session_id}")
        
        # 1. Iniciar conversación
        context = await ai_engine.start_conversation(session_id, caller_number)
        print(f"✅ Conversación iniciada para {caller_number}")
        
        # 2. Simular identificación con DNI
        print(f"\n👤 Usuario dice: '12345678'")
        response1 = await ai_engine.process_user_input(session_id, "12345678")
        print(f"🤖 Bot responde: '{response1.text}'")
        
        # Verificar terminación después de respuesta
        terminated = await audio_processor._check_call_termination(session, response1)
        if terminated:
            print("🔚 Llamada terminada después de identificación")
            return
        
        # 3. Simular consulta de deuda
        print(f"\n💰 Usuario dice: '¿cuánto debo?'")
        response2 = await ai_engine.process_user_input(session_id, "¿cuánto debo?")
        print(f"🤖 Bot responde: '{response2.text}'")
        
        # Verificar terminación
        terminated = await audio_processor._check_call_termination(session, response2)
        if terminated:
            print("🔚 Llamada terminada después de consulta")
            return
        
        # 4. Simular compromiso de pago
        print(f"\n💳 Usuario dice: 'sí puedo pagar mañana 500 soles'")
        response3 = await ai_engine.process_user_input(session_id, "sí puedo pagar mañana 500 soles")
        print(f"🤖 Bot responde: '{response3.text}'")
        
        # Verificar terminación después de compromiso
        terminated = await audio_processor._check_call_termination(session, response3)
        if terminated:
            print("🎉 Llamada terminada después de compromiso exitoso")
            return
        
        # 5. Si no terminó, simular despedida manual
        print(f"\n👋 Usuario dice: 'adiós'")
        response4 = await ai_engine.process_user_input(session_id, "adiós")
        print(f"🤖 Bot responde: '{response4.text}'")
        
        # Verificar terminación final
        terminated = await audio_processor._check_call_termination(session, response4)
        if terminated:
            print("👋 Llamada terminada por despedida")
        else:
            print("⚠️ Llamada NO terminó automáticamente")
        
        # Cleanup
        await ai_engine.cleanup()
        
    except Exception as e:
        print(f"❌ Error en simulación: {e}")
        import traceback
        traceback.print_exc()


async def simulate_conversation_with_goodbye():
    """Simula una conversación que termina con despedida del usuario."""
    print("\n🎭 SIMULANDO CONVERSACIÓN CON DESPEDIDA DEL USUARIO")
    print("=" * 60)
    
    # Configurar logging
    logger = logging.getLogger(__name__)
    
    try:
        # Inicializar componentes
        config = Config()
        db_manager = DatabaseManager()
        ai_engine = AIConversationEngine(config.openai, db_manager)
        await ai_engine.initialize()
        
        audio_processor = MockAudioProcessor(config, logger)
        
        # Crear sesión mock
        session_id = "test-goodbye-session-002"
        caller_number = "987654322"
        session = MockSession(session_id, caller_number, ai_engine)
        
        print(f"📞 Iniciando conversación: {session_id}")
        
        # 1. Iniciar conversación
        context = await ai_engine.start_conversation(session_id, caller_number)
        print(f"✅ Conversación iniciada para {caller_number}")
        
        # 2. Usuario dice adiós inmediatamente
        print(f"\n👋 Usuario dice: 'no no no adiós'")
        response1 = await ai_engine.process_user_input(session_id, "no no no adiós")
        print(f"🤖 Bot responde: '{response1.text}'")
        
        # Verificar terminación después de adiós
        terminated = await audio_processor._check_call_termination(session, response1)
        if terminated:
            print("👋 Llamada terminada por despedida del usuario")
        else:
            print("⚠️ Llamada NO terminó automáticamente")
            
            # Intentar otra respuesta
            print(f"\n👋 Usuario insiste: 'adiós, corta la llamada'")
            response2 = await ai_engine.process_user_input(session_id, "adiós, corta la llamada")
            print(f"🤖 Bot responde: '{response2.text}'")
            
            terminated = await audio_processor._check_call_termination(session, response2)
            if terminated:
                print("👋 Llamada terminada en segundo intento")
            else:
                print("⚠️ Llamada AÚN NO terminó")
        
        # Cleanup
        await ai_engine.cleanup()
        
    except Exception as e:
        print(f"❌ Error en simulación: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Función principal de prueba."""
    print("🚀 PRUEBA REAL DEL SISTEMA DE TERMINACIÓN DE LLAMADAS")
    print("=" * 80)
    
    # Prueba 1: Conversación con compromiso
    await simulate_conversation_with_commitment()
    
    # Esperar un poco entre pruebas
    await asyncio.sleep(2)
    
    # Prueba 2: Conversación con despedida del usuario
    await simulate_conversation_with_goodbye()
    
    print("\n" + "=" * 80)
    print("📊 PRUEBAS COMPLETADAS")
    print("=" * 80)
    print("✅ Si viste mensajes de 'Llamada terminada', el sistema funciona")
    print("❌ Si no terminó automáticamente, revisar logs para debugging")


if __name__ == "__main__":
    asyncio.run(main())
