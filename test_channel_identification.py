#!/opt/cobranza-bot/venv/bin/python3
# -*- coding: utf-8 -*-

"""
Test para verificar la identificación precisa de canales en AMI.
"""

import sys
import os
import asyncio
import logging

# Agregar src al path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.asterisk_ami import AsteriskAMI, AMIConfig
from src.config import Config


async def test_channel_identification():
    """Prueba la identificación de canales con múltiples escenarios."""
    print("🔍 PRUEBA DE IDENTIFICACIÓN DE CANALES")
    print("=" * 60)
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(name)s - %(message)s')
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            print("✅ Conectado a AMI para prueba de identificación")
            
            # Obtener todos los canales con detalles
            print(f"\n📋 LISTANDO TODOS LOS CANALES ACTIVOS:")
            print("-" * 50)
            
            channels = await ami._get_all_channels()
            
            if channels:
                print(f"📞 {len(channels)} canales encontrados:")
                for i, channel in enumerate(channels, 1):
                    print(f"   {i}. {channel}")
                
                # Probar búsqueda con diferentes UUIDs
                test_uuids = [
                    "12345678-1234-1234-1234-123456789abc",  # UUID típico
                    "test-session-123",                       # Session ID simple
                    channels[0] if channels else "no-channel" # Primer canal como test
                ]
                
                print(f"\n🔍 PROBANDO BÚSQUEDA DE CANALES:")
                print("-" * 50)
                
                for uuid in test_uuids:
                    print(f"\n🎯 Buscando UUID: {uuid}")
                    
                    # Buscar canal por sesión
                    found_channel = await ami.find_channel_by_session(uuid, "987654321")
                    
                    if found_channel:
                        print(f"   ✅ Canal encontrado: {found_channel}")
                    else:
                        print(f"   ❌ Canal NO encontrado")
                
                # Simular escenario con múltiples canales AudioSocket
                print(f"\n⚠️ ESCENARIO: MÚLTIPLES CANALES ACTIVOS")
                print("-" * 50)
                print(f"   Canales activos: {len(channels)}")
                
                if len(channels) > 1:
                    print(f"   🛡️ SEGURIDAD: Sistema debe identificar canal específico")
                    print(f"   🚫 NO debe usar hangup masivo")
                    
                    # Probar con UUID que no existe
                    fake_uuid = "fake-uuid-that-does-not-exist"
                    result = await ami.find_channel_by_session(fake_uuid, "999999999")
                    
                    if result is None:
                        print(f"   ✅ Correcto: No encontró canal para UUID falso")
                        print(f"   ✅ Sistema NO ejecutará hangup masivo")
                    else:
                        print(f"   ❌ PELIGRO: Encontró canal para UUID falso: {result}")
                        print(f"   ❌ Podría cortar llamada incorrecta")
                else:
                    print(f"   ℹ️ Solo hay 1 canal activo - Escenario seguro")
                
            else:
                print("📞 No hay canales activos")
                print("   Para probar identificación real, necesitas llamadas activas")
                
                # Simular búsqueda sin canales
                print(f"\n🧪 SIMULANDO BÚSQUEDA SIN CANALES:")
                test_uuid = "test-uuid-no-channels"
                result = await ami.find_channel_by_session(test_uuid)
                
                if result is None:
                    print(f"   ✅ Correcto: No encontró canal (no hay canales activos)")
                else:
                    print(f"   ❌ Error: Encontró canal inexistente: {result}")
            
            await ami.disconnect()
            return True
            
        else:
            print("❌ No se pudo conectar a AMI")
            return False
            
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_security_scenarios():
    """Prueba escenarios de seguridad para evitar cortar llamadas incorrectas."""
    print(f"\n🛡️ PRUEBA DE ESCENARIOS DE SEGURIDAD")
    print("=" * 60)
    
    try:
        config = Config()
        ami_config = AMIConfig(
            host=config.asterisk.host,
            port=config.asterisk.ami_port,
            username=config.asterisk.ami_user,
            secret=config.asterisk.ami_secret
        )
        
        ami = AsteriskAMI(ami_config)
        
        if await ami.connect():
            print("✅ Conectado para pruebas de seguridad")
            
            # Escenario 1: UUID que no existe
            print(f"\n🧪 ESCENARIO 1: UUID inexistente")
            fake_uuid = "00000000-0000-0000-0000-000000000000"
            result1 = await ami.find_channel_by_session(fake_uuid, "123456789")
            
            if result1 is None:
                print(f"   ✅ SEGURO: No encontró canal para UUID falso")
            else:
                print(f"   ❌ PELIGRO: Encontró canal para UUID falso: {result1}")
            
            # Escenario 2: Número de teléfono que no existe
            print(f"\n🧪 ESCENARIO 2: Número inexistente")
            result2 = await ami.find_channel_by_session("any-uuid", "000000000")
            
            if result2 is None:
                print(f"   ✅ SEGURO: No encontró canal para número falso")
            else:
                print(f"   ❌ PELIGRO: Encontró canal para número falso: {result2}")
            
            # Escenario 3: Sin parámetros de identificación
            print(f"\n🧪 ESCENARIO 3: Sin parámetros de identificación")
            result3 = await ami.find_channel_by_session("", "")
            
            if result3 is None:
                print(f"   ✅ SEGURO: No encontró canal sin parámetros")
            else:
                print(f"   ❌ PELIGRO: Encontró canal sin parámetros: {result3}")
            
            await ami.disconnect()
            
            # Evaluar seguridad
            security_passed = all([
                result1 is None,
                result2 is None, 
                result3 is None
            ])
            
            if security_passed:
                print(f"\n🛡️ PRUEBAS DE SEGURIDAD PASADAS")
                print(f"   ✅ Sistema NO cortará llamadas incorrectas")
                print(f"   ✅ Identificación de canal es precisa")
            else:
                print(f"\n⚠️ FALLOS DE SEGURIDAD DETECTADOS")
                print(f"   ❌ Sistema podría cortar llamadas incorrectas")
                print(f"   ❌ Revisar lógica de identificación")
            
            return security_passed
            
        else:
            print("❌ No se pudo conectar para pruebas de seguridad")
            return False
            
    except Exception as e:
        print(f"❌ Error en pruebas de seguridad: {e}")
        return False


async def main():
    """Función principal."""
    print("🚀 PRUEBAS DE IDENTIFICACIÓN Y SEGURIDAD DE CANALES")
    print("=" * 80)
    
    # Prueba 1: Identificación de canales
    identification_ok = await test_channel_identification()
    
    # Prueba 2: Escenarios de seguridad
    security_ok = await test_security_scenarios()
    
    # Resumen
    print("\n" + "=" * 80)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 80)
    
    print(f"🔍 Identificación de canales: {'✅ FUNCIONAL' if identification_ok else '❌ PROBLEMAS'}")
    print(f"🛡️ Seguridad anti-hangup masivo: {'✅ SEGURO' if security_ok else '❌ PELIGROSO'}")
    
    if identification_ok and security_ok:
        print(f"\n🎉 SISTEMA DE HANGUP SEGURO Y PRECISO")
        print(f"   ✅ Identifica canales específicos correctamente")
        print(f"   ✅ NO cortará llamadas de otros usuarios")
        print(f"   ✅ Listo para uso en producción")
        
        print(f"\n📞 CÓMO FUNCIONA EN LLAMADAS REALES:")
        print(f"   1. Asterisk genera UUID único para cada llamada")
        print(f"   2. Python usa ese UUID como session_id")
        print(f"   3. AMI busca canal que contenga ese UUID específico")
        print(f"   4. Solo corta el canal de esa sesión específica")
        print(f"   5. Otras llamadas permanecen intactas")
    else:
        print(f"\n⚠️ PROBLEMAS DETECTADOS")
        if not identification_ok:
            print(f"   - Problemas con identificación de canales")
        if not security_ok:
            print(f"   - Riesgos de seguridad en hangup")
        print(f"   - Revisar configuración AMI y lógica de búsqueda")


if __name__ == "__main__":
    asyncio.run(main())
