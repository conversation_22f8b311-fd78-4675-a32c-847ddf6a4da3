# Configuración de Google Cloud Text-to-Speech

Este documento explica cómo configurar Google Cloud Text-to-Speech para el bot de cobranza.

## Prerrequisitos

1. Cuenta de Google Cloud Platform
2. Proyecto de Google Cloud creado
3. API de Text-to-Speech habilitada

## Pasos de Configuración

### 1. Habilitar la API de Text-to-Speech

```bash
# Habilitar la API
gcloud services enable texttospeech.googleapis.com
```

### 2. Crear una Cuenta de Servicio

```bash
# Crear cuenta de servicio
gcloud iam service-accounts create tts-service-account \
    --description="Service account for TTS bot" \
    --display-name="TTS Bot Service Account"

# Asignar permisos
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:tts-service-account@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudtts.user"

# Crear y descargar la clave JSON
gcloud iam service-accounts keys create ~/tts-credentials.json \
    --iam-account=tts-service-account@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

### 3. Configurar Variables de Entorno

Actualiza tu archivo `.env` con:

```bash
GOOGLE_TTS_CREDENTIALS_PATH=/path/to/your/tts-credentials.json
GOOGLE_TTS_PROJECT_ID=your-google-cloud-project-id
GOOGLE_TTS_LANGUAGE_CODE=es-US
GOOGLE_TTS_VOICE_NAME=es-US-Chirp3-HD-Achernar
GOOGLE_TTS_AUDIO_ENCODING=LINEAR16
GOOGLE_TTS_SAMPLE_RATE=24000
GOOGLE_TTS_SPEAKING_RATE=1.0
GOOGLE_TTS_PITCH=0.0
GOOGLE_TTS_VOLUME_GAIN=0.0
```

### 4. Generar Token de Acceso (para pruebas)

```bash
# Autenticarse con gcloud
gcloud auth login

# Configurar proyecto por defecto
gcloud config set project YOUR_PROJECT_ID

# Generar token de acceso
gcloud auth print-access-token
```

### 5. Probar la API con curl

```bash
curl -X POST -H "Content-Type: application/json" \
 -H "X-Goog-User-Project: $(gcloud config list --format='value(core.project)')" \
 -H "Authorization: Bearer $(gcloud auth print-access-token)" \
 --data '{
 "input": {
   "text": "Hola, como estás"
 },
 "voice": {
   "languageCode": "es-US",
   "name": "es-US-Chirp3-HD-Achernar"
 },
 "audioConfig": {
   "audioEncoding": "LINEAR16"
 }
 }' \
 "https://texttospeech.googleapis.com/v1/text:synthesize"
```

## Notas Importantes

- La voz `es-US-Chirp3-HD-Achernar` es una voz premium de alta calidad
- Asegúrate de que tu proyecto tenga facturación habilitada
- Guarda el archivo de credenciales en un lugar seguro
- No subas el archivo de credenciales al repositorio

## Solución de Problemas

### Error de Autenticación
- Verifica que el archivo de credenciales existe y tiene los permisos correctos
- Asegúrate de que la cuenta de servicio tiene los roles necesarios

### Error de API no habilitada
```bash
gcloud services enable texttospeech.googleapis.com
```

### Error de cuota
- Verifica los límites de tu proyecto en Google Cloud Console
- Considera aumentar las cuotas si es necesario